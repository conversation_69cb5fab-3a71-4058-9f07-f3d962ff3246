-- 快速修复booking表结构的最简单方法
-- 请先备份您的数据！

USE car_service;

-- 方法1：如果booking表是空的或者可以重建，直接删除重建
-- 注意：这会删除所有现有数据！
-- DROP TABLE IF EXISTS booking;

-- 使用car_service_v2.sql中的完整表结构
CREATE TABLE IF NOT EXISTS booking (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '预约ID',
    booking_no VARCHAR(32) NOT NULL UNIQUE COMMENT '预约编号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    vehicle_id BIGINT NOT NULL COMMENT '车辆ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    service_name VARCHAR(50) NOT NULL COMMENT '服务名称',
    service_price DECIMAL(10,2) NOT NULL COMMENT '服务价格',
    contact_name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    booking_date DATE NOT NULL COMMENT '预约日期',
    booking_time TIME NOT NULL COMMENT '预约时间',
    estimated_duration INT DEFAULT 60 COMMENT '预计时长(分钟)',
    problem_description TEXT COMMENT '问题描述',
    remark TEXT COMMENT '备注',
    status INT DEFAULT 1 COMMENT '状态(1-待确认 2-已确认 3-服务中 4-已完成 5-已取消)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_service_id (service_id),
    INDEX idx_technician_id (technician_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_status (status)
) COMMENT = '预约记录';

-- 如果表已存在但结构不对，添加缺失的字段（忽略错误）
ALTER TABLE booking ADD COLUMN booking_time TIME NOT NULL DEFAULT '09:00:00' COMMENT '预约时间';
ALTER TABLE booking ADD COLUMN booking_no VARCHAR(32) UNIQUE COMMENT '预约编号';
ALTER TABLE booking ADD COLUMN estimated_duration INT DEFAULT 60 COMMENT '预计时长(分钟)';
ALTER TABLE booking ADD COLUMN problem_description TEXT COMMENT '问题描述';
ALTER TABLE booking ADD COLUMN remark TEXT COMMENT '备注';

-- 验证表结构
DESCRIBE booking;
