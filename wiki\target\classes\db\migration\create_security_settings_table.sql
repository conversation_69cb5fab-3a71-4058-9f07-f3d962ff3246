-- 创建安全设置表
CREATE TABLE IF NOT EXISTS security_settings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    max_login_fail_count INT NOT NULL DEFAULT 5 COMMENT '最大登录失败次数',
    lock_duration_minutes INT NOT NULL DEFAULT 30 COMMENT '锁定持续时间（分钟）',
    min_password_length INT NOT NULL DEFAULT 6 COMMENT '密码最小长度',
    require_complex_password BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否要求复杂密码',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全设置表';

-- 插入默认安全设置
INSERT INTO security_settings (max_login_fail_count, lock_duration_minutes, min_password_length, require_complex_password)
VALUES (5, 30, 6, FALSE)
ON DUPLICATE KEY UPDATE 
    max_login_fail_count = VALUES(max_login_fail_count),
    lock_duration_minutes = VALUES(lock_duration_minutes),
    min_password_length = VALUES(min_password_length),
    require_complex_password = VALUES(require_complex_password);
