### 测试车辆添加功能

### 1. 添加新车辆
POST http://localhost:8880/vehicle/save
Content-Type: application/json

{
  "licensePlate": "京A12345",
  "brand": "丰田",
  "model": "卡罗拉",
  "color": "白色",
  "year": 2022,
  "engineNumber": "TEST123456",
  "vin": "VIN123456789",
  "userId": 1
}

### 2. 获取用户车辆列表
GET http://localhost:8880/vehicle/getVehicleListByPage?page=1&size=10&userId=1

### 3. 编辑车辆信息
POST http://localhost:8880/vehicle/save
Content-Type: application/json

{
  "id": 1,
  "licensePlate": "京A12346",
  "brand": "丰田",
  "model": "卡罗拉",
  "color": "黑色",
  "year": 2023,
  "engineNumber": "TEST123456",
  "vin": "VIN123456789",
  "userId": 1
}

### 4. 删除车辆
GET http://localhost:8080/vehicle/remove?id=1
