<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gec.wiki.mapper.CategoryMapper">

    <resultMap id="BaseResultMap" type="com.gec.wiki.pojo.Category">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="parent" column="parent" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent,name,
        sort
    </sql>
</mapper>
