#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录锁定功能的脚本
"""

import requests
import json
import time

def test_login_lock():
    """测试登录锁定功能"""
    url = "http://localhost:8880/auth/login"
    headers = {
        "Content-Type": "application/json"
    }
    
    # 测试数据 - 使用错误的密码
    login_data = {
        "username": "小石头的店铺",
        "password": "wrong_password"
    }
    
    print("开始测试登录锁定功能...")
    print("=" * 50)
    
    # 连续尝试登录5次
    for i in range(1, 6):
        print(f"\n第 {i} 次尝试登录...")
        
        try:
            response = requests.post(url, headers=headers, json=login_data, timeout=10)
            result = response.json()
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if not result.get("success", False):
                message = result.get("message", "")
                print(f"登录失败: {message}")
                
                # 检查是否包含失败次数信息
                if "连续错误" in message:
                    print("✅ 失败次数显示正常")
                elif "已被锁定" in message:
                    print("🔒 账号已被锁定")
                    break
            else:
                print("✅ 登录成功")
                break
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
            break
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析异常: {e}")
            print(f"原始响应: {response.text}")
            break
        
        # 等待1秒再进行下一次尝试
        if i < 5:
            time.sleep(1)
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    test_login_lock()
