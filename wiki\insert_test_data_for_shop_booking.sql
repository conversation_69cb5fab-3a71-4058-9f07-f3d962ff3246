-- 为维修店预约功能插入测试数据
USE car_service;

-- 插入测试用户（如果不存在）
INSERT IGNORE INTO users (id, username, password, real_name, phone, email, user_type, status, create_time) VALUES 
(2, 'customer1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '张先生', '13800138001', '<EMAIL>', 1, 1, NOW()),
(3, 'customer2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '李女士', '13800138002', '<EMAIL>', 1, 1, NOW()),
(4, 'customer3', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '王先生', '13800138003', '<EMAIL>', 1, 1, NOW());

-- 插入测试车辆（如果不存在）
INSERT IGNORE INTO vehicle (id, user_id, license_plate, brand, model, color, year, is_default, status, create_time) VALUES 
(1, 2, '京A12345', '大众', '朗逸', '白色', 2020, 1, 1, NOW()),
(2, 3, '京B23456', '丰田', '凯美瑞', '黑色', 2019, 1, 1, NOW()),
(3, 4, '京C34567', '本田', '雅阁', '银色', 2021, 1, 1, NOW());

-- 确保service表有shop_id字段并设置值
UPDATE service SET shop_id = 1 WHERE shop_id IS NULL OR shop_id = 0;

-- 插入测试技师（如果不存在）
INSERT IGNORE INTO technician (id, name, phone, specialties, experience_years, level, rating_score, rating_count, status, create_time) VALUES 
(1, '张师傅', '13800001001', '发动机维修,变速箱维修', 15, 4, 4.8, 128, 1, NOW()),
(2, '李师傅', '13800001002', '电气系统,空调维修', 8, 3, 4.6, 89, 1, NOW()),
(3, '王师傅', '13800001003', '轮胎更换,制动系统', 5, 2, 4.7, 56, 1, NOW());

-- 清理今日的测试预约数据
DELETE FROM booking WHERE booking_date = CURDATE();

-- 插入今日预约测试数据
INSERT INTO booking (booking_no, user_id, vehicle_id, service_id, technician_id, service_name, service_price, contact_name, contact_phone, booking_date, booking_time, estimated_duration, problem_description, remark, status, create_time, update_time) VALUES 
('B20250910001', 2, 1, 1, 1, '机油更换', 150.00, '张先生', '13800138001', CURDATE(), '09:00:00', 30, '需要更换机油，机油已经使用5000公里', '预约时间请准时', 1, NOW(), NOW()),
('B20250910002', 3, 2, 2, 2, '轮胎更换', 400.00, '李女士', '13800138002', CURDATE(), '14:30:00', 45, '前轮轮胎磨损严重，需要更换', '更换后请检查动平衡', 2, NOW(), NOW()),
('B20250910003', 4, 3, 3, 3, '刹车片更换', 300.00, '王先生', '13800138003', CURDATE(), '16:00:00', 60, '刹车有异响，怀疑刹车片磨损', '刹车系统全面检查', 3, NOW(), NOW()),
('B20250910004', 2, 1, 5, 1, '水箱清洗', 120.00, '赵师傅', '13800138004', CURDATE(), '11:00:00', 40, '水温偏高，需要清洗水箱', '检查冷却液是否需要更换', 1, NOW(), NOW()),
('B20250910005', 3, 2, 6, 2, '电瓶检测', 50.00, '陈师傅', '13800138005', CURDATE(), '15:00:00', 20, '启动困难，怀疑电瓶问题', '如有问题建议更换', 4, NOW(), NOW());

-- 插入一些历史预约用于统计
INSERT INTO booking (booking_no, user_id, vehicle_id, service_id, technician_id, service_name, service_price, contact_name, contact_phone, booking_date, booking_time, estimated_duration, problem_description, status, create_time, update_time) VALUES 
('B20250909001', 2, 1, 1, 1, '机油更换', 150.00, '张先生', '13800138001', DATE_SUB(CURDATE(), INTERVAL 1 DAY), '10:00:00', 30, '定期保养', 4, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
('B20250908001', 3, 2, 2, 2, '轮胎更换', 400.00, '李女士', '13800138002', DATE_SUB(CURDATE(), INTERVAL 2 DAY), '14:00:00', 45, '轮胎老化', 4, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)),
('B20250907001', 4, 3, 7, 3, '空调清洗', 180.00, '王先生', '13800138003', DATE_SUB(CURDATE(), INTERVAL 3 DAY), '16:30:00', 50, '空调有异味', 4, DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
('B20250901001', 2, 1, 1, 1, '机油更换', 150.00, '张先生', '13800138001', '2025-09-01', '09:00:00', 30, '定期保养', 4, '2025-09-01 09:00:00', '2025-09-01 12:00:00'),
('B20250902001', 3, 2, 2, 2, '轮胎更换', 400.00, '李女士', '13800138002', '2025-09-02', '14:00:00', 45, '轮胎老化', 4, '2025-09-02 14:00:00', '2025-09-02 17:00:00');

-- 验证数据插入
SELECT '插入的今日预约数据:' as info;
SELECT b.id, b.booking_no, b.service_name, b.contact_name, b.booking_time, b.status
FROM booking b
JOIN service s ON b.service_id = s.id
WHERE b.booking_date = CURDATE() AND s.shop_id = 1
ORDER BY b.booking_time;

SELECT '统计数据:' as info;
SELECT 
    COUNT(CASE WHEN b.status = 1 THEN 1 END) as pending_orders,
    COUNT(CASE WHEN b.status IN (2, 3) THEN 1 END) as processing_orders,
    COUNT(CASE WHEN b.status = 4 THEN 1 END) as completed_orders
FROM booking b
JOIN service s ON b.service_id = s.id
WHERE s.shop_id = 1;
