# 测试维修店预约管理API

### 1. 获取维修店今日预约列表
GET http://localhost:8080/shop/booking/today
Content-Type: application/json

### 2. 获取维修店预约统计
GET http://localhost:8080/shop/booking/stats
Content-Type: application/json

### 3. 更新预约状态 - 确认预约（状态1 -> 2）
PUT http://localhost:8080/shop/booking/1/status?status=2
Content-Type: application/json

### 4. 更新预约状态 - 开始服务（状态2 -> 3）
PUT http://localhost:8080/shop/booking/1/status?status=3
Content-Type: application/json

### 5. 更新预约状态 - 完成服务（状态3 -> 4）
PUT http://localhost:8080/shop/booking/1/status?status=4
Content-Type: application/json

### 6. 确认预约（快捷方法）
PUT http://localhost:8080/shop/booking/4/confirm
Content-Type: application/json

### 7. 开始服务（快捷方法）
PUT http://localhost:8080/shop/booking/4/start
Content-Type: application/json

### 8. 完成服务（快捷方法）
PUT http://localhost:8080/shop/booking/4/complete
Content-type: application/json

### 9. 测试错误状态更新
PUT http://localhost:8080/shop/booking/999/status?status=2
Content-Type: application/json

### 10. 测试无效状态值
PUT http://localhost:8080/shop/booking/1/status?status=10
Content-Type: application/json
