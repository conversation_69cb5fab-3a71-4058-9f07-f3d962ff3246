# 测试管理员服务管理页面的所有API功能
Write-Host "=== 汽车维修服务管理API功能测试 ===" -ForegroundColor Green

$baseUrl = "http://localhost:8880"
$headers = @{'Content-Type' = 'application/json'}

Write-Host "`n1. 测试服务列表查询..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/service/getServiceListByPage?page=1&size=10" -Method GET
    Write-Host "✅ 服务列表查询成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "   返回数据: success=$($data.success), total=$($data.content.total)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 服务列表查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n2. 测试分类数据查询..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/category/allList" -Method GET
    Write-Host "✅ 分类数据查询成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "   返回数据: success=$($data.success), 分类数量=$($data.content.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 分类数据查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n3. 测试服务搜索功能..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/service/getServiceListByPage?page=1&size=10&name=Test" -Method GET
    Write-Host "✅ 服务搜索成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "   搜索结果: success=$($data.success), 匹配数量=$($data.content.total)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 服务搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n4. 测试服务添加功能..." -ForegroundColor Yellow
$newService = @{
    name = "API测试服务"
    category1Id = 100
    category2Id = 101
    description = "这是一个API测试服务"
    price = 199.99
    originalPrice = 299.99
    duration = 90
    status = 1
    isRecommend = 0
    cover = ""
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/service/save" -Method POST -Body $newService -Headers $headers
    Write-Host "✅ 服务添加成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "   添加结果: success=$($data.success), message=$($data.message)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 服务添加失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $errorContent = $reader.ReadToEnd()
        Write-Host "   错误详情: $errorContent" -ForegroundColor Red
    }
}

Write-Host "`n5. 测试获取单个服务详情..." -ForegroundColor Yellow
try {
    # 先获取一个服务ID
    $listResponse = Invoke-WebRequest -Uri "$baseUrl/service/getServiceListByPage?page=1&size=1" -Method GET
    $listData = $listResponse.Content | ConvertFrom-Json
    
    if ($listData.success -and $listData.content.list.Count -gt 0) {
        $serviceId = $listData.content.list[0].id
        $response = Invoke-WebRequest -Uri "$baseUrl/service/getServiceById?id=$serviceId" -Method GET
        Write-Host "✅ 服务详情查询成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
        $data = $response.Content | ConvertFrom-Json
        Write-Host "   服务详情: success=$($data.success), 服务名称=$($data.content.name)" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ 没有可用的服务数据进行测试" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 服务详情查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果所有测试都通过，说明后端API正常工作" -ForegroundColor Cyan
Write-Host "如果前端功能仍有问题，可能是前端JavaScript代码的问题" -ForegroundColor Cyan
