package com.gec.wiki.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gec.wiki.pojo.Vehicle;
import com.gec.wiki.pojo.req.VehicleQueryReq;
import com.gec.wiki.pojo.req.VehicleSaveReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.service.VehicleService;
import com.gec.wiki.utils.CopyUtil;
import com.gec.wiki.utils.SnowFlake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/vehicle")
public class VehicleController {

    @Autowired
    private VehicleService vehicleService;

    private static final Logger LOG = LoggerFactory.getLogger(VehicleController.class);

    @GetMapping("/getVehicleListByPage")
    public CommonResp getVehicleListByPage(VehicleQueryReq vehicleReq){
        Page<Vehicle> page = new Page<>(vehicleReq.getPage(),vehicleReq.getSize());

        QueryWrapper<Vehicle> wrapper=new QueryWrapper<>();
        if (!ObjectUtils.isEmpty(vehicleReq.getLicensePlate())){
            wrapper.like("license_plate",vehicleReq.getLicensePlate());
        }
        if (!ObjectUtils.isEmpty(vehicleReq.getBrand())){
            wrapper.like("brand",vehicleReq.getBrand());
        }
        if (vehicleReq.getUserId()!=null){
            wrapper.eq("user_id",vehicleReq.getUserId());
        }

        page = vehicleService.page(page,wrapper);
        List<Vehicle> list = page.getRecords();

        LOG.info("总行数：{}",page.getTotal()+"");
        LOG.info("总页数：{}",page.getPages()+"");

        List<Vehicle> vehicleResps =CopyUtil.copyList(list, Vehicle.class);

        //分页处理
        PageResp<Vehicle> pageResp=new PageResp<>();
        pageResp.setTotal(page.getTotal());
        pageResp.setList(vehicleResps);

        CommonResp< PageResp<Vehicle> > resp = new CommonResp<>();
        resp.setContent(pageResp);
        return resp;
    }

    @PostMapping("/save")
    public CommonResp save(@RequestBody VehicleSaveReq req){
        CommonResp resp=new CommonResp();
        LOG.info("收到的保存请求：ID = {}, 车牌号 = {}", req.getId(), req.getLicensePlate());
        
        Vehicle vehicle=CopyUtil.copy(req,Vehicle.class);
        
        if (req.getId() == null){
            // 新增
            SnowFlake snowFlake=new SnowFlake();
            vehicle.setId(snowFlake.nextId());
            vehicle.setCreateTime(LocalDateTime.now());
            vehicle.setUpdateTime(LocalDateTime.now());
            vehicleService.save(vehicle);
            resp.setMessage("添加成功");
        }else {
            // 修改
            LOG.info("更新ID = {}", vehicle.getId());
            vehicle.setUpdateTime(LocalDateTime.now());
            vehicleService.updateById(vehicle);
            resp.setMessage("修改成功");
        }
        return resp;
    }
    
    @GetMapping("/remove")
    public CommonResp remove(long id){
        vehicleService.removeById(id);
        CommonResp<Object> resp = new CommonResp<>();
        resp.setMessage("删除成功");
        return resp;
    }
}
