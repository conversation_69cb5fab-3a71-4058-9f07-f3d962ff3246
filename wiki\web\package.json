{"name": "car-service-web", "version": "0.1.0", "private": true, "scripts": {"serve-dev": "vue-cli-service serve --mode dev --port 8081", "serve-prod": "vue-cli-service serve --mode prod", "build-dev": "vue-cli-service build --mode dev", "build-prod": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/icons-vue": "^6.0.1", "ant-design-vue": "^3.2.6", "axios": "^1.10.0", "vue": "^3.2.0", "vue-class-component": "^8.0.0-0", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0"}, "devDependencies": {"@vue/cli-plugin-router": "~4.5.9", "@vue/cli-plugin-typescript": "~4.5.9", "@vue/cli-plugin-vuex": "~4.5.9", "@vue/cli-service": "~4.5.9", "@vue/compiler-sfc": "^3.0.0", "typescript": "^4.5.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}