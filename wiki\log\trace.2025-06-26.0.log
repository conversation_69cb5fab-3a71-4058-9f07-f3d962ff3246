2025-06-26 09:04:43.414 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 7580 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-26 09:04:43.420 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:04:44.679 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m <PERSON><PERSON> initialized with port(s): 8080 (http)
2025-06-26 09:04:44.687 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 09:04:44.688 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-26 09:04:44.688 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-26 09:04:44.760 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-26 09:04:44.760 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1301 ms
2025-06-26 09:04:45.401 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-26 09:04:45.588 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 09:04:45.603 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-26 09:04:45.613 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 2.548 seconds (JVM running for 3.529)
2025-06-26 09:04:45.615 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-26 09:04:45.615 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-26 09:05:58.680 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 09:05:58.681 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-26 09:05:58.682 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-26 09:05:58.745 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634730782524448  [0;39m ------------- 开始 -------------
2025-06-26 09:05:58.747 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634730782524448  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-26 09:05:58.747 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634730782524448  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:05:58.748 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634730782524448  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:05:58.846 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634730782524448  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-26 09:05:59.025 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4634730782524448  [0;39m {dataSource-1} inited
2025-06-26 09:05:59.278 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634730782524448  [0;39m 总行数：13
2025-06-26 09:05:59.279 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634730782524448  [0;39m 总页数：1
2025-06-26 09:05:59.332 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634730782524448  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070114640928,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070366135328,"name":"虎鲸1","viewCount":744,"voteCount":484},{"category1Id":6,"category2Id":6,"description":"6","id":4632073233073184,"name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:05:59.332 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634730782524448  [0;39m ------------- 结束 耗时：588 ms -------------
2025-06-26 09:06:01.799 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634730882630688  [0;39m ------------- 开始 -------------
2025-06-26 09:06:01.800 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634730882630688  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-26 09:06:01.800 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634730882630688  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:06:01.800 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634730882630688  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:06:01.800 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634730882630688  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:06:01.810 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634730882630688  [0;39m 总行数：13
2025-06-26 09:06:01.810 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634730882630688  [0;39m 总页数：7
2025-06-26 09:06:01.811 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634730882630688  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:06:01.811 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634730882630688  [0;39m ------------- 结束 耗时：12 ms -------------
2025-06-26 09:06:04.355 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634730966385696  [0;39m ------------- 开始 -------------
2025-06-26 09:06:04.355 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634730966385696  [0;39m 请求地址: http://localhost:8080/save POST
2025-06-26 09:06:04.355 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634730966385696  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-26 09:06:04.356 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634730966385696  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:06:04.360 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634730966385696  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484}]
2025-06-26 09:06:04.387 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634730966385696  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-06-26 09:06:04.387 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634730966385696  [0;39m ------------- 结束 耗时：32 ms -------------
2025-06-26 09:06:04.409 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634730968155168  [0;39m ------------- 开始 -------------
2025-06-26 09:06:04.409 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634730968155168  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-26 09:06:04.409 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634730968155168  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:06:04.409 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634730968155168  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:06:04.409 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634730968155168  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:06:04.419 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634730968155168  [0;39m 总行数：13
2025-06-26 09:06:04.419 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634730968155168  [0;39m 总页数：7
2025-06-26 09:06:04.419 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634730968155168  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:06:04.419 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634730968155168  [0;39m ------------- 结束 耗时：10 ms -------------
2025-06-26 09:06:10.031 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634731152376864  [0;39m ------------- 开始 -------------
2025-06-26 09:06:10.031 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634731152376864  [0;39m 请求地址: http://localhost:8080/save POST
2025-06-26 09:06:10.031 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634731152376864  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-26 09:06:10.032 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634731152376864  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:06:10.032 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634731152376864  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484}]
2025-06-26 09:06:10.043 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634731152376864  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-06-26 09:06:10.043 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634731152376864  [0;39m ------------- 结束 耗时：12 ms -------------
2025-06-26 09:06:10.065 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634731153490976  [0;39m ------------- 开始 -------------
2025-06-26 09:06:10.065 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634731153490976  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-26 09:06:10.066 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634731153490976  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:06:10.066 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634731153490976  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:06:10.066 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634731153490976  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:06:10.073 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634731153490976  [0;39m 总行数：13
2025-06-26 09:06:10.073 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634731153490976  [0;39m 总页数：7
2025-06-26 09:06:10.074 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634731153490976  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:06:10.074 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634731153490976  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-26 09:06:12.131 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634731221189664  [0;39m ------------- 开始 -------------
2025-06-26 09:06:12.131 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634731221189664  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-26 09:06:12.131 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634731221189664  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:06:12.132 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634731221189664  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:06:12.132 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634731221189664  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-26 09:06:12.139 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634731221189664  [0;39m 总行数：13
2025-06-26 09:06:12.139 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634731221189664  [0;39m 总页数：7
2025-06-26 09:06:12.139 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634731221189664  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:06:12.139 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634731221189664  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-26 09:06:15.977 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634731347215392  [0;39m ------------- 开始 -------------
2025-06-26 09:06:15.977 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634731347215392  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-26 09:06:15.977 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634731347215392  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:06:15.977 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634731347215392  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:06:15.977 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634731347215392  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:06:15.983 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634731347215392  [0;39m 总行数：13
2025-06-26 09:06:15.984 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634731347215392  [0;39m 总页数：7
2025-06-26 09:06:15.984 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634731347215392  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:06:15.984 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634731347215392  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 09:53:45.146 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-26 09:53:45.147 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-26 09:53:51.697 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 17024 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-26 09:53:51.699 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:53:52.609 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-26 09:53:52.616 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 09:53:52.617 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-26 09:53:52.617 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-26 09:53:52.680 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-26 09:53:52.681 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 952 ms
2025-06-26 09:53:53.226 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-26 09:53:53.383 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 09:53:53.395 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-26 09:53:53.402 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.965 seconds (JVM running for 3.128)
2025-06-26 09:53:53.403 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-26 09:53:53.404 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-26 09:54:03.817 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 09:54:03.817 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-26 09:54:03.818 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-06-26 09:54:03.889 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634825322923040  [0;39m ------------- 开始 -------------
2025-06-26 09:54:03.890 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634825322923040  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:54:03.891 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634825322923040  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:54:03.891 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634825322923040  [0;39m 远程地址: 127.0.0.1
2025-06-26 09:54:03.943 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634825322923040  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-26 09:54:04.051 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4634825322923040  [0;39m {dataSource-1} inited
2025-06-26 09:54:04.211 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634825322923040  [0;39m 总行数：13
2025-06-26 09:54:04.211 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634825322923040  [0;39m 总页数：1
2025-06-26 09:54:04.223 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634825322923040  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070114640928,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070366135328,"name":"虎鲸1","viewCount":744,"voteCount":484},{"category1Id":6,"category2Id":6,"description":"6","id":4632073233073184,"name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:54:04.223 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634825322923040  [0;39m ------------- 结束 耗时：335 ms -------------
2025-06-26 09:54:06.195 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634825398518816  [0;39m ------------- 开始 -------------
2025-06-26 09:54:06.196 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634825398518816  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:54:06.196 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634825398518816  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:54:06.196 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634825398518816  [0;39m 远程地址: 127.0.0.1
2025-06-26 09:54:06.196 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634825398518816  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:54:06.202 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634825398518816  [0;39m 总行数：13
2025-06-26 09:54:06.202 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634825398518816  [0;39m 总页数：7
2025-06-26 09:54:06.203 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634825398518816  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:54:06.203 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634825398518816  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-26 09:54:08.347 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634825469035552  [0;39m ------------- 开始 -------------
2025-06-26 09:54:08.348 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634825469035552  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:54:08.348 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634825469035552  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:54:08.348 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634825469035552  [0;39m 远程地址: 127.0.0.1
2025-06-26 09:54:08.348 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634825469035552  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-26 09:54:08.356 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634825469035552  [0;39m 总行数：13
2025-06-26 09:54:08.356 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634825469035552  [0;39m 总页数：7
2025-06-26 09:54:08.358 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634825469035552  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:54:08.359 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634825469035552  [0;39m ------------- 结束 耗时：12 ms -------------
2025-06-26 09:54:09.825 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634825517466656  [0;39m ------------- 开始 -------------
2025-06-26 09:54:09.826 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634825517466656  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:54:09.826 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634825517466656  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:54:09.826 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634825517466656  [0;39m 远程地址: 127.0.0.1
2025-06-26 09:54:09.827 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634825517466656  [0;39m 请求参数: [{}]
2025-06-26 09:54:09.831 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4634825517466656  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null
	at com.gec.wiki.controller.EbookController.getebookListByPage(EbookController.java:56)
	at com.gec.wiki.controller.EbookController$$FastClassBySpringCGLIB$$3a6c4900.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.gec.wiki.aspect.LogAspect.doAround(LogAspect.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.gec.wiki.controller.EbookController$$EnhancerBySpringCGLIB$$c30f8caa.getebookListByPage(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-26 09:54:46.023 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634826703602720  [0;39m ------------- 开始 -------------
2025-06-26 09:54:46.023 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634826703602720  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:54:46.023 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634826703602720  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:54:46.024 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634826703602720  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:54:46.024 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634826703602720  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:54:46.031 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634826703602720  [0;39m 总行数：13
2025-06-26 09:54:46.031 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634826703602720  [0;39m 总页数：7
2025-06-26 09:54:46.031 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634826703602720  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:54:46.032 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634826703602720  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-26 09:54:48.222 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634826775626784  [0;39m ------------- 开始 -------------
2025-06-26 09:54:48.222 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634826775626784  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:54:48.222 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634826775626784  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:54:48.222 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634826775626784  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:54:48.223 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634826775626784  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-26 09:54:48.228 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634826775626784  [0;39m 总行数：13
2025-06-26 09:54:48.229 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634826775626784  [0;39m 总页数：7
2025-06-26 09:54:48.229 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634826775626784  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:54:48.229 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634826775626784  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-26 09:54:49.651 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634826822485024  [0;39m ------------- 开始 -------------
2025-06-26 09:54:49.651 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634826822485024  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:54:49.651 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634826822485024  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:54:49.651 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634826822485024  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:54:49.652 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634826822485024  [0;39m 请求参数: [{}]
2025-06-26 09:54:49.652 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4634826822485024  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null
	at com.gec.wiki.controller.EbookController.getebookListByPage(EbookController.java:56)
	at com.gec.wiki.controller.EbookController$$FastClassBySpringCGLIB$$3a6c4900.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.gec.wiki.aspect.LogAspect.doAround(LogAspect.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.gec.wiki.controller.EbookController$$EnhancerBySpringCGLIB$$c30f8caa.getebookListByPage(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-26 09:54:53.953 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634826963452960  [0;39m ------------- 开始 -------------
2025-06-26 09:54:53.953 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634826963452960  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:54:53.954 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634826963452960  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:54:53.954 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634826963452960  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:54:53.954 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634826963452960  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:54:53.961 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634826963452960  [0;39m 总行数：13
2025-06-26 09:54:53.961 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634826963452960  [0;39m 总页数：7
2025-06-26 09:54:53.962 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634826963452960  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:54:53.962 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634826963452960  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-26 09:54:55.398 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634827010802720  [0;39m ------------- 开始 -------------
2025-06-26 09:54:55.398 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634827010802720  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:54:55.398 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634827010802720  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:54:55.398 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634827010802720  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:54:55.399 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634827010802720  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-26 09:54:55.403 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634827010802720  [0;39m 总行数：13
2025-06-26 09:54:55.407 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634827010802720  [0;39m 总页数：7
2025-06-26 09:54:55.408 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634827010802720  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:54:55.408 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634827010802720  [0;39m ------------- 结束 耗时：10 ms -------------
2025-06-26 09:55:19.547 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634827802117152  [0;39m ------------- 开始 -------------
2025-06-26 09:55:19.547 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634827802117152  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:55:19.548 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634827802117152  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:55:19.548 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634827802117152  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:55:19.548 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634827802117152  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-26 09:55:19.557 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634827802117152  [0;39m 总行数：13
2025-06-26 09:55:19.557 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634827802117152  [0;39m 总页数：1
2025-06-26 09:55:19.558 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634827802117152  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070114640928,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070366135328,"name":"虎鲸1","viewCount":744,"voteCount":484},{"category1Id":6,"category2Id":6,"description":"6","id":4632073233073184,"name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:55:19.559 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634827802117152  [0;39m ------------- 结束 耗时：12 ms -------------
2025-06-26 09:55:20.789 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634827842815008  [0;39m ------------- 开始 -------------
2025-06-26 09:55:20.789 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634827842815008  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:55:20.790 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634827842815008  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:55:20.790 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634827842815008  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:55:20.790 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634827842815008  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:55:20.796 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634827842815008  [0;39m 总行数：13
2025-06-26 09:55:20.797 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634827842815008  [0;39m 总页数：7
2025-06-26 09:55:20.797 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634827842815008  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:55:20.797 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634827842815008  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-26 09:55:24.083 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634827950752800  [0;39m ------------- 开始 -------------
2025-06-26 09:55:24.083 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634827950752800  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:55:24.083 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634827950752800  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:55:24.083 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634827950752800  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:55:24.083 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634827950752800  [0;39m 请求参数: [{"page":6,"size":2}]
2025-06-26 09:55:24.087 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634827950752800  [0;39m 总行数：13
2025-06-26 09:55:24.087 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634827950752800  [0;39m 总页数：7
2025-06-26 09:55:24.087 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634827950752800  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:55:24.087 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634827950752800  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 09:55:25.908 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634828010554400  [0;39m ------------- 开始 -------------
2025-06-26 09:55:25.909 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634828010554400  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:55:25.909 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634828010554400  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:55:25.909 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634828010554400  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:55:25.909 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634828010554400  [0;39m 请求参数: [{}]
2025-06-26 09:55:25.910 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4634828010554400  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null
	at com.gec.wiki.controller.EbookController.getebookListByPage(EbookController.java:56)
	at com.gec.wiki.controller.EbookController$$FastClassBySpringCGLIB$$3a6c4900.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.gec.wiki.aspect.LogAspect.doAround(LogAspect.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.gec.wiki.controller.EbookController$$EnhancerBySpringCGLIB$$c30f8caa.getebookListByPage(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-26 09:56:10.620 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634829475677216  [0;39m ------------- 开始 -------------
2025-06-26 09:56:10.620 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634829475677216  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:56:10.620 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634829475677216  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:56:10.620 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634829475677216  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:56:10.621 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634829475677216  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:56:10.626 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634829475677216  [0;39m 总行数：13
2025-06-26 09:56:10.626 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634829475677216  [0;39m 总页数：7
2025-06-26 09:56:10.627 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634829475677216  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:56:10.627 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634829475677216  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 09:56:12.397 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634829533905952  [0;39m ------------- 开始 -------------
2025-06-26 09:56:12.398 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634829533905952  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:56:12.398 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634829533905952  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:56:12.398 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634829533905952  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:56:12.398 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634829533905952  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-26 09:56:12.403 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634829533905952  [0;39m 总行数：13
2025-06-26 09:56:12.404 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634829533905952  [0;39m 总页数：7
2025-06-26 09:56:12.404 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634829533905952  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:56:12.404 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634829533905952  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 09:56:16.917 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634829682017312  [0;39m ------------- 开始 -------------
2025-06-26 09:56:16.917 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634829682017312  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:56:16.917 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634829682017312  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:56:16.917 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634829682017312  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:56:16.918 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634829682017312  [0;39m 请求参数: [{}]
2025-06-26 09:56:16.918 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4634829682017312  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null
	at com.gec.wiki.controller.EbookController.getebookListByPage(EbookController.java:56)
	at com.gec.wiki.controller.EbookController$$FastClassBySpringCGLIB$$3a6c4900.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.gec.wiki.aspect.LogAspect.doAround(LogAspect.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.gec.wiki.controller.EbookController$$EnhancerBySpringCGLIB$$c30f8caa.getebookListByPage(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-26 09:56:40.298 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634830448165920  [0;39m ------------- 开始 -------------
2025-06-26 09:56:40.298 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634830448165920  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:56:40.298 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634830448165920  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:56:40.298 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634830448165920  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:56:40.299 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634830448165920  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:56:40.304 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634830448165920  [0;39m 总行数：13
2025-06-26 09:56:40.304 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634830448165920  [0;39m 总页数：7
2025-06-26 09:56:40.304 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634830448165920  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:56:40.304 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634830448165920  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 09:56:48.382 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634830713062432  [0;39m ------------- 开始 -------------
2025-06-26 09:56:48.382 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634830713062432  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:56:48.382 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634830713062432  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:56:48.382 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634830713062432  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:56:48.382 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634830713062432  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-26 09:56:48.386 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634830713062432  [0;39m 总行数：13
2025-06-26 09:56:48.387 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634830713062432  [0;39m 总页数：7
2025-06-26 09:56:48.387 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634830713062432  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:56:48.387 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634830713062432  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 09:57:13.134 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634831524135968  [0;39m ------------- 开始 -------------
2025-06-26 09:57:13.135 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634831524135968  [0;39m 请求地址: http://localhost:8080/ebook/save POST
2025-06-26 09:57:13.135 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634831524135968  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-26 09:57:13.135 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634831524135968  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:57:13.137 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634831524135968  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸00","viewCount":744,"voteCount":484}]
2025-06-26 09:57:13.159 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634831524135968  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-06-26 09:57:13.159 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634831524135968  [0;39m ------------- 结束 耗时：25 ms -------------
2025-06-26 09:57:13.177 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634831525544992  [0;39m ------------- 开始 -------------
2025-06-26 09:57:13.177 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634831525544992  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:57:13.177 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634831525544992  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:57:13.177 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634831525544992  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:57:13.177 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634831525544992  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-26 09:57:13.182 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634831525544992  [0;39m 总行数：13
2025-06-26 09:57:13.182 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634831525544992  [0;39m 总页数：7
2025-06-26 09:57:13.183 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634831525544992  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸00","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 09:57:13.183 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634831525544992  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 09:57:14.810 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634831579055136  [0;39m ------------- 开始 -------------
2025-06-26 09:57:14.811 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634831579055136  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:57:14.811 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634831579055136  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:57:14.811 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634831579055136  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 09:57:14.811 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634831579055136  [0;39m 请求参数: [{}]
2025-06-26 09:57:14.811 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4634831579055136  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "com.gec.wiki.pojo.req.EbookQueryReq.getPage()" is null
	at com.gec.wiki.controller.EbookController.getebookListByPage(EbookController.java:56)
	at com.gec.wiki.controller.EbookController$$FastClassBySpringCGLIB$$3a6c4900.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.gec.wiki.aspect.LogAspect.doAround(LogAspect.java:85)
	at jdk.internal.reflect.GeneratedMethodAccessor63.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.gec.wiki.controller.EbookController$$EnhancerBySpringCGLIB$$c30f8caa.getebookListByPage(<generated>)
	at jdk.internal.reflect.GeneratedMethodAccessor62.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-26 09:58:44.471 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634834517066784  [0;39m ------------- 开始 -------------
2025-06-26 09:58:44.471 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634834517066784  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 09:58:44.471 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634834517066784  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 09:58:44.472 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634834517066784  [0;39m 远程地址: 127.0.0.1
2025-06-26 09:58:44.472 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634834517066784  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 09:58:44.488 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634834517066784  [0;39m 总行数：13
2025-06-26 09:58:44.489 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634834517066784  [0;39m 总页数：7
2025-06-26 09:58:44.489 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634834517066784  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 09:58:44.489 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634834517066784  [0;39m ------------- 结束 耗时：18 ms -------------
2025-06-26 10:08:07.814 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634852976690208  [0;39m ------------- 开始 -------------
2025-06-26 10:08:07.816 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634852976690208  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:08:07.816 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634852976690208  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:08:07.816 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634852976690208  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:08:07.816 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634852976690208  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:08:07.819 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634852976690208  [0;39m 总行数：13
2025-06-26 10:08:07.819 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634852976690208  [0;39m 总页数：7
2025-06-26 10:08:07.819 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634852976690208  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-26 10:08:07.820 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634852976690208  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 10:08:10.367 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634853060346912  [0;39m ------------- 开始 -------------
2025-06-26 10:08:10.367 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634853060346912  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:08:10.368 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634853060346912  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:08:10.368 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634853060346912  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:08:10.368 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634853060346912  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-26 10:08:10.372 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634853060346912  [0;39m 总行数：13
2025-06-26 10:08:10.372 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634853060346912  [0;39m 总页数：7
2025-06-26 10:08:10.372 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634853060346912  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632126531568672,"name":"虎鲸00","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-26 10:08:10.372 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634853060346912  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 10:08:18.268 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634853319246880  [0;39m ------------- 开始 -------------
2025-06-26 10:08:18.268 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634853319246880  [0;39m 请求地址: http://localhost:8080/ebook/remove GET
2025-06-26 10:08:18.268 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634853319246880  [0;39m 类名方法: com.gec.wiki.controller.EbookController.remove
2025-06-26 10:08:18.268 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634853319246880  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:08:18.268 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634853319246880  [0;39m 请求参数: [4632126531568672]
2025-06-26 10:08:18.275 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634853319246880  [0;39m 返回结果: {"success":true}
2025-06-26 10:08:18.275 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634853319246880  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 10:08:18.291 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634853320000544  [0;39m ------------- 开始 -------------
2025-06-26 10:08:18.291 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634853320000544  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:08:18.291 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634853320000544  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:08:18.291 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634853320000544  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:08:18.291 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634853320000544  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-26 10:08:18.293 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634853320000544  [0;39m 总行数：12
2025-06-26 10:08:18.293 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634853320000544  [0;39m 总页数：6
2025-06-26 10:08:18.293 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634853320000544  [0;39m 返回结果: {"content":{"list":[],"total":12},"success":true}
2025-06-26 10:08:18.293 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634853320000544  [0;39m ------------- 结束 耗时：2 ms -------------
2025-06-26 10:14:10.914 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634864874751008  [0;39m ------------- 开始 -------------
2025-06-26 10:14:10.915 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634864874751008  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:14:10.915 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634864874751008  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:14:10.916 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634864874751008  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:14:10.916 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634864874751008  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:14:10.924 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634864874751008  [0;39m 总行数：12
2025-06-26 10:14:10.924 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634864874751008  [0;39m 总页数：6
2025-06-26 10:14:10.925 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634864874751008  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:14:10.926 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634864874751008  [0;39m ------------- 结束 耗时：12 ms -------------
2025-06-26 10:14:45.027 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634865992565792  [0;39m ------------- 开始 -------------
2025-06-26 10:14:45.027 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634865992565792  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:14:45.027 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634865992565792  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:14:45.027 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634865992565792  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:14:45.028 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634865992565792  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:14:45.033 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634865992565792  [0;39m 总行数：12
2025-06-26 10:14:45.033 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634865992565792  [0;39m 总页数：6
2025-06-26 10:14:45.033 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634865992565792  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:14:45.034 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634865992565792  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 10:20:08.561 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634876594127904  [0;39m ------------- 开始 -------------
2025-06-26 10:20:08.561 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634876594127904  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:20:08.561 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634876594127904  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:20:08.561 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634876594127904  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:20:08.561 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634876594127904  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:20:08.567 INFO  com.gec.wiki.service.EbookService                 :60   [32m4634876594127904  [0;39m 总行数：12
2025-06-26 10:20:08.567 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634876594127904  [0;39m 总页数：6
2025-06-26 10:20:08.567 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634876594127904  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:20:08.568 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634876594127904  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 10:42:50.842 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-26 10:42:50.845 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-26 10:42:55.476 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 20676 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-26 10:42:55.478 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:42:56.264 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-26 10:42:56.272 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 10:42:56.272 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-26 10:42:56.272 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-26 10:42:56.336 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-26 10:42:56.336 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 825 ms
2025-06-26 10:42:56.867 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-26 10:42:57.018 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 10:42:57.034 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-26 10:42:57.041 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.837 seconds (JVM running for 2.419)
2025-06-26 10:42:57.042 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-26 10:42:57.043 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-26 10:47:20.000 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:47:20.001 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-26 10:47:20.002 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-26 10:47:20.044 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634930054562848  [0;39m ------------- 开始 -------------
2025-06-26 10:47:20.045 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634930054562848  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:47:20.045 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634930054562848  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:47:20.045 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634930054562848  [0;39m 远程地址: 127.0.0.1
2025-06-26 10:47:20.085 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634930054562848  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:47:20.184 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4634930054562848  [0;39m {dataSource-1} inited
2025-06-26 10:47:20.323 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634930054562848  [0;39m 总行数：12
2025-06-26 10:47:20.324 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634930054562848  [0;39m 总页数：6
2025-06-26 10:47:20.332 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634930054562848  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:47:20.333 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634930054562848  [0;39m ------------- 结束 耗时：290 ms -------------
2025-06-26 10:47:22.949 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634930149753888  [0;39m ------------- 开始 -------------
2025-06-26 10:47:22.950 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634930149753888  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:47:22.950 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634930149753888  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:47:22.950 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634930149753888  [0;39m 远程地址: 127.0.0.1
2025-06-26 10:47:22.951 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634930149753888  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-26 10:47:22.960 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634930149753888  [0;39m 总行数：12
2025-06-26 10:47:22.960 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634930149753888  [0;39m 总页数：1
2025-06-26 10:47:22.962 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634930149753888  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070114640928,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070366135328,"name":"虎鲸1","viewCount":744,"voteCount":484},{"category1Id":6,"category2Id":6,"description":"6","id":4632073233073184,"name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":12},"success":true}
2025-06-26 10:47:22.964 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634930149753888  [0;39m ------------- 结束 耗时：15 ms -------------
2025-06-26 10:47:23.460 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634930166498336  [0;39m ------------- 开始 -------------
2025-06-26 10:47:23.460 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634930166498336  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:47:23.461 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634930166498336  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:47:23.461 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634930166498336  [0;39m 远程地址: 127.0.0.1
2025-06-26 10:47:23.462 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634930166498336  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:47:23.469 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634930166498336  [0;39m 总行数：12
2025-06-26 10:47:23.470 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634930166498336  [0;39m 总页数：6
2025-06-26 10:47:23.472 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634930166498336  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:47:23.474 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634930166498336  [0;39m ------------- 结束 耗时：14 ms -------------
2025-06-26 10:47:36.573 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634930596185120  [0;39m ------------- 开始 -------------
2025-06-26 10:47:36.573 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634930596185120  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:47:36.573 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634930596185120  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:47:36.574 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634930596185120  [0;39m 远程地址: 127.0.0.1
2025-06-26 10:47:36.574 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634930596185120  [0;39m 请求参数: [{"page":5,"size":2}]
2025-06-26 10:47:36.579 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634930596185120  [0;39m 总行数：12
2025-06-26 10:47:36.579 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634930596185120  [0;39m 总页数：6
2025-06-26 10:47:36.580 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634930596185120  [0;39m 返回结果: {"content":{"list":[{"category1Id":6,"category2Id":6,"description":"6","id":4632073233073184,"name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":12},"success":true}
2025-06-26 10:47:36.580 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634930596185120  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 10:48:03.582 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634931481216032  [0;39m ------------- 开始 -------------
2025-06-26 10:48:03.583 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634931481216032  [0;39m 请求地址: http://localhost:8080/ebook/uploadImage POST
2025-06-26 10:48:03.583 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634931481216032  [0;39m 类名方法: com.gec.wiki.controller.EbookController.uploadImage
2025-06-26 10:48:03.583 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634931481216032  [0;39m 远程地址: 127.0.0.1
2025-06-26 10:48:03.584 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634931481216032  [0;39m 请求参数: [null]
2025-06-26 10:48:03.589 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634931481216032  [0;39m 文件名:929aec79c41a4a91b7195f55a1962b60.gif
2025-06-26 10:48:03.589 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634931481216032  [0;39m 返回结果: {"content":"929aec79c41a4a91b7195f55a1962b60.gif","success":true}
2025-06-26 10:48:03.589 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634931481216032  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 10:48:04.886 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634931523945504  [0;39m ------------- 开始 -------------
2025-06-26 10:48:04.887 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634931523945504  [0;39m 请求地址: http://localhost:8080/ebook/save POST
2025-06-26 10:48:04.887 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634931523945504  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-26 10:48:04.887 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634931523945504  [0;39m 远程地址: 127.0.0.1
2025-06-26 10:48:04.890 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634931523945504  [0;39m 请求参数: [{"category1Id":6,"category2Id":6,"cover":"/image/929aec79c41a4a91b7195f55a1962b60.gif","description":"6","id":4632073233073184,"name":"6"}]
2025-06-26 10:48:04.912 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634931523945504  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-06-26 10:48:04.913 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634931523945504  [0;39m ------------- 结束 耗时：27 ms -------------
2025-06-26 10:48:04.932 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634931525452832  [0;39m ------------- 开始 -------------
2025-06-26 10:48:04.933 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634931525452832  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:48:04.933 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634931525452832  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:48:04.933 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634931525452832  [0;39m 远程地址: 127.0.0.1
2025-06-26 10:48:04.933 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634931525452832  [0;39m 请求参数: [{"page":5,"size":2}]
2025-06-26 10:48:04.939 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634931525452832  [0;39m 总行数：12
2025-06-26 10:48:04.939 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634931525452832  [0;39m 总页数：6
2025-06-26 10:48:04.939 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634931525452832  [0;39m 返回结果: {"content":{"list":[{"category1Id":6,"category2Id":6,"cover":"/image/929aec79c41a4a91b7195f55a1962b60.gif","description":"6","id":4632073233073184,"name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":12},"success":true}
2025-06-26 10:48:04.939 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634931525452832  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 10:51:12.183 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634937661293600  [0;39m ------------- 开始 -------------
2025-06-26 10:51:12.184 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634937661293600  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:51:12.184 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634937661293600  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:51:12.184 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634937661293600  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:51:12.184 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634937661293600  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:51:12.202 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634937661293600  [0;39m 总行数：12
2025-06-26 10:51:12.203 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634937661293600  [0;39m 总页数：6
2025-06-26 10:51:12.203 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634937661293600  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:51:12.203 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634937661293600  [0;39m ------------- 结束 耗时：20 ms -------------
2025-06-26 10:51:54.902 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634939061109792  [0;39m ------------- 开始 -------------
2025-06-26 10:51:54.903 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634939061109792  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:51:54.903 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634939061109792  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:51:54.904 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634939061109792  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:51:54.904 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634939061109792  [0;39m 请求参数: [{"page":6,"size":2}]
2025-06-26 10:51:54.912 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634939061109792  [0;39m 总行数：12
2025-06-26 10:51:54.912 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634939061109792  [0;39m 总页数：6
2025-06-26 10:51:54.912 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634939061109792  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":12},"success":true}
2025-06-26 10:51:54.912 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634939061109792  [0;39m ------------- 结束 耗时：10 ms -------------
2025-06-26 10:52:02.570 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634939312374816  [0;39m ------------- 开始 -------------
2025-06-26 10:52:02.570 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634939312374816  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:52:02.571 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634939312374816  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:52:02.571 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634939312374816  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:52:02.571 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634939312374816  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:52:02.577 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634939312374816  [0;39m 总行数：12
2025-06-26 10:52:02.577 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634939312374816  [0;39m 总页数：6
2025-06-26 10:52:02.578 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634939312374816  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:52:02.578 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634939312374816  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-26 10:53:38.205 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634942446142496  [0;39m ------------- 开始 -------------
2025-06-26 10:53:38.206 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634942446142496  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:53:38.206 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634942446142496  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:53:38.206 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634942446142496  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:53:38.206 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634942446142496  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:53:38.213 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634942446142496  [0;39m 总行数：12
2025-06-26 10:53:38.213 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634942446142496  [0;39m 总页数：6
2025-06-26 10:53:38.213 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634942446142496  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:53:38.214 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634942446142496  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-26 10:54:53.732 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-26 10:54:53.734 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-26 10:54:55.970 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 1276 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-26 10:54:55.974 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:54:56.675 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-26 10:54:56.685 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 10:54:56.685 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-26 10:54:56.685 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-26 10:54:56.739 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-26 10:54:56.739 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 736 ms
2025-06-26 10:54:57.259 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-26 10:54:57.414 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 10:54:57.427 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-26 10:54:57.434 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.727 seconds (JVM running for 2.191)
2025-06-26 10:54:57.436 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-26 10:54:57.436 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-26 10:56:05.618 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:56:05.618 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-26 10:56:05.619 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-26 10:56:05.683 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634947278668832  [0;39m ------------- 开始 -------------
2025-06-26 10:56:05.684 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634947278668832  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:56:05.685 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634947278668832  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:56:05.688 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634947278668832  [0;39m 远程地址: 127.0.0.1
2025-06-26 10:56:05.757 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634947278668832  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-26 10:56:05.911 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4634947278668832  [0;39m {dataSource-1} inited
2025-06-26 10:56:06.161 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634947278668832  [0;39m 总行数：12
2025-06-26 10:56:06.162 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634947278668832  [0;39m 总页数：1
2025-06-26 10:56:06.178 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634947278668832  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070114640928,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070366135328,"name":"虎鲸1","viewCount":744,"voteCount":484},{"category1Id":6,"category2Id":6,"cover":"/image/929aec79c41a4a91b7195f55a1962b60.gif","description":"6","id":4632073233073184,"name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":12},"success":true}
2025-06-26 10:56:06.179 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634947278668832  [0;39m ------------- 结束 耗时：497 ms -------------
2025-06-26 10:56:07.505 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634947338404896  [0;39m ------------- 开始 -------------
2025-06-26 10:56:07.505 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634947338404896  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:56:07.506 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634947338404896  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:56:07.506 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634947338404896  [0;39m 远程地址: 127.0.0.1
2025-06-26 10:56:07.506 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634947338404896  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:56:07.513 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634947338404896  [0;39m 总行数：12
2025-06-26 10:56:07.513 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634947338404896  [0;39m 总页数：6
2025-06-26 10:56:07.513 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634947338404896  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:56:07.514 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634947338404896  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-26 10:58:56.236 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634952867382304  [0;39m ------------- 开始 -------------
2025-06-26 10:58:56.237 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634952867382304  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:58:56.237 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634952867382304  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:58:56.237 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634952867382304  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:58:56.238 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634952867382304  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:58:56.256 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634952867382304  [0;39m 总行数：12
2025-06-26 10:58:56.257 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634952867382304  [0;39m 总页数：6
2025-06-26 10:58:56.260 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634952867382304  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:58:56.260 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634952867382304  [0;39m ------------- 结束 耗时：24 ms -------------
2025-06-26 10:59:32.401 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634954052437024  [0;39m ------------- 开始 -------------
2025-06-26 10:59:32.401 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634954052437024  [0;39m 请求地址: http://localhost:8080/ebook/uploadImage POST
2025-06-26 10:59:32.401 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634954052437024  [0;39m 类名方法: com.gec.wiki.controller.EbookController.uploadImage
2025-06-26 10:59:32.402 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634954052437024  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:59:32.402 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634954052437024  [0;39m 请求参数: [null]
2025-06-26 10:59:32.406 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634954052437024  [0;39m 文件名:84122b4c67d3499a9397811f5567bc85.png
2025-06-26 10:59:32.407 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634954052437024  [0;39m 返回结果: {"content":"84122b4c67d3499a9397811f5567bc85.png","success":true}
2025-06-26 10:59:32.407 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634954052437024  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 10:59:33.702 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634954095068192  [0;39m ------------- 开始 -------------
2025-06-26 10:59:33.703 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634954095068192  [0;39m 请求地址: http://localhost:8080/ebook/save POST
2025-06-26 10:59:33.703 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634954095068192  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-26 10:59:33.703 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634954095068192  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:59:33.705 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634954095068192  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/84122b4c67d3499a9397811f5567bc85.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484}]
2025-06-26 10:59:33.723 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634954095068192  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-06-26 10:59:33.723 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634954095068192  [0;39m ------------- 结束 耗时：21 ms -------------
2025-06-26 10:59:33.744 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634954096444448  [0;39m ------------- 开始 -------------
2025-06-26 10:59:33.745 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634954096444448  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:59:33.745 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634954096444448  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:59:33.745 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634954096444448  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:59:33.745 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634954096444448  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:59:33.752 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634954096444448  [0;39m 总行数：12
2025-06-26 10:59:33.752 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634954096444448  [0;39m 总页数：6
2025-06-26 10:59:33.753 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634954096444448  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/84122b4c67d3499a9397811f5567bc85.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:59:33.753 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634954096444448  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-26 10:59:43.937 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634954430448672  [0;39m ------------- 开始 -------------
2025-06-26 10:59:43.937 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634954430448672  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:59:43.937 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634954430448672  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:59:43.938 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634954430448672  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:59:43.938 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634954430448672  [0;39m 请求参数: [{"page":2,"size":2}]
2025-06-26 10:59:43.943 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634954430448672  [0;39m 总行数：12
2025-06-26 10:59:43.944 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634954430448672  [0;39m 总页数：6
2025-06-26 10:59:43.944 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634954430448672  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2}],"total":12},"success":true}
2025-06-26 10:59:43.944 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634954430448672  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 10:59:47.176 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634954536584224  [0;39m ------------- 开始 -------------
2025-06-26 10:59:47.176 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634954536584224  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:59:47.176 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634954536584224  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:59:47.176 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634954536584224  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:59:47.177 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634954536584224  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 10:59:47.182 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634954536584224  [0;39m 总行数：12
2025-06-26 10:59:47.182 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634954536584224  [0;39m 总页数：6
2025-06-26 10:59:47.182 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634954536584224  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":12},"success":true}
2025-06-26 10:59:47.183 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634954536584224  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 10:59:48.311 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634954573775904  [0;39m ------------- 开始 -------------
2025-06-26 10:59:48.311 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634954573775904  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:59:48.311 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634954573775904  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:59:48.312 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634954573775904  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:59:48.312 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634954573775904  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-26 10:59:48.318 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634954573775904  [0;39m 总行数：12
2025-06-26 10:59:48.318 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634954573775904  [0;39m 总页数：6
2025-06-26 10:59:48.318 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634954573775904  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070114640928,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070366135328,"name":"虎鲸1","viewCount":744,"voteCount":484}],"total":12},"success":true}
2025-06-26 10:59:48.318 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634954573775904  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 10:59:52.078 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634954697212960  [0;39m ------------- 开始 -------------
2025-06-26 10:59:52.078 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634954697212960  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 10:59:52.078 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634954697212960  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 10:59:52.078 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634954697212960  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 10:59:52.078 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634954697212960  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 10:59:52.084 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634954697212960  [0;39m 总行数：12
2025-06-26 10:59:52.085 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634954697212960  [0;39m 总页数：6
2025-06-26 10:59:52.085 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634954697212960  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/84122b4c67d3499a9397811f5567bc85.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 10:59:52.085 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634954697212960  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 11:01:53.513 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634958676395040  [0;39m ------------- 开始 -------------
2025-06-26 11:01:53.513 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634958676395040  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:01:53.514 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634958676395040  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:01:53.514 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634958676395040  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:01:53.514 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634958676395040  [0;39m 请求参数: [{"page":5,"size":2}]
2025-06-26 11:01:53.519 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634958676395040  [0;39m 总行数：12
2025-06-26 11:01:53.519 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634958676395040  [0;39m 总页数：6
2025-06-26 11:01:53.520 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634958676395040  [0;39m 返回结果: {"content":{"list":[{"category1Id":6,"category2Id":6,"cover":"/image/929aec79c41a4a91b7195f55a1962b60.gif","description":"6","id":4632073233073184,"name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":12},"success":true}
2025-06-26 11:01:53.520 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634958676395040  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 11:07:10.034 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634969048155168  [0;39m ------------- 开始 -------------
2025-06-26 11:07:10.034 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634969048155168  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:07:10.034 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634969048155168  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:07:10.034 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634969048155168  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:07:10.035 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634969048155168  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 11:07:10.039 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634969048155168  [0;39m 总行数：12
2025-06-26 11:07:10.039 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634969048155168  [0;39m 总页数：6
2025-06-26 11:07:10.039 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634969048155168  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/84122b4c67d3499a9397811f5567bc85.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 11:07:10.039 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634969048155168  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:07:14.360 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634969189909536  [0;39m ------------- 开始 -------------
2025-06-26 11:07:14.360 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634969189909536  [0;39m 请求地址: http://localhost:8080/ebook/uploadImage POST
2025-06-26 11:07:14.360 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634969189909536  [0;39m 类名方法: com.gec.wiki.controller.EbookController.uploadImage
2025-06-26 11:07:14.360 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634969189909536  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:07:14.360 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634969189909536  [0;39m 请求参数: [null]
2025-06-26 11:07:14.363 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634969189909536  [0;39m 文件名:fbe46dc9a6fc4f3f91867e384437775e.png
2025-06-26 11:07:14.363 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634969189909536  [0;39m 返回结果: {"content":"fbe46dc9a6fc4f3f91867e384437775e.png","success":true}
2025-06-26 11:07:14.363 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634969189909536  [0;39m ------------- 结束 耗时：3 ms -------------
2025-06-26 11:07:15.521 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634969227953184  [0;39m ------------- 开始 -------------
2025-06-26 11:07:15.521 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634969227953184  [0;39m 请求地址: http://localhost:8080/ebook/save POST
2025-06-26 11:07:15.521 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634969227953184  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-26 11:07:15.521 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634969227953184  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:07:15.522 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634969227953184  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484}]
2025-06-26 11:07:15.527 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634969227953184  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-06-26 11:07:15.527 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634969227953184  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 11:07:15.544 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634969228706848  [0;39m ------------- 开始 -------------
2025-06-26 11:07:15.545 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634969228706848  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:07:15.545 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634969228706848  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:07:15.545 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634969228706848  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:07:15.545 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634969228706848  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 11:07:15.550 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634969228706848  [0;39m 总行数：12
2025-06-26 11:07:15.550 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634969228706848  [0;39m 总页数：6
2025-06-26 11:07:15.552 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634969228706848  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 11:07:15.552 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634969228706848  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-26 11:14:31.672 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634983519749152  [0;39m ------------- 开始 -------------
2025-06-26 11:14:31.672 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634983519749152  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:14:31.672 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634983519749152  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:14:31.672 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634983519749152  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:14:31.672 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634983519749152  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:14:31.675 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634983519749152  [0;39m 总行数：12
2025-06-26 11:14:31.675 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634983519749152  [0;39m 总页数：6
2025-06-26 11:14:31.676 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634983519749152  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":12},"success":true}
2025-06-26 11:14:31.676 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634983519749152  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 11:17:55.091 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634990185382944  [0;39m ------------- 开始 -------------
2025-06-26 11:17:55.092 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634990185382944  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:17:55.092 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634990185382944  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:17:55.092 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634990185382944  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:17:55.092 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634990185382944  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 11:17:55.097 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634990185382944  [0;39m 总行数：12
2025-06-26 11:17:55.097 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634990185382944  [0;39m 总页数：6
2025-06-26 11:17:55.097 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634990185382944  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-26 11:17:55.098 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634990185382944  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 11:17:59.961 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634990344963104  [0;39m ------------- 开始 -------------
2025-06-26 11:17:59.962 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634990344963104  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:17:59.962 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634990344963104  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:17:59.962 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634990344963104  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:17:59.962 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634990344963104  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:17:59.968 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634990344963104  [0;39m 总行数：12
2025-06-26 11:17:59.968 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634990344963104  [0;39m 总页数：6
2025-06-26 11:17:59.968 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634990344963104  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":12},"success":true}
2025-06-26 11:17:59.969 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634990344963104  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-26 11:18:06.357 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634990554547232  [0;39m ------------- 开始 -------------
2025-06-26 11:18:06.357 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634990554547232  [0;39m 请求地址: http://localhost:8080/ebook/uploadImage POST
2025-06-26 11:18:06.357 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634990554547232  [0;39m 类名方法: com.gec.wiki.controller.EbookController.uploadImage
2025-06-26 11:18:06.357 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634990554547232  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:06.357 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634990554547232  [0;39m 请求参数: [null]
2025-06-26 11:18:06.358 INFO  com.gec.wiki.service.EbookService                 :59   [32m4634990554547232  [0;39m 文件名:293f7b1236804b9c9f61b832f410c694.png
2025-06-26 11:18:06.360 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634990554547232  [0;39m 返回结果: {"content":"293f7b1236804b9c9f61b832f410c694.png","success":true}
2025-06-26 11:18:06.360 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634990554547232  [0;39m ------------- 结束 耗时：3 ms -------------
2025-06-26 11:18:07.513 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634990592427040  [0;39m ------------- 开始 -------------
2025-06-26 11:18:07.513 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634990592427040  [0;39m 请求地址: http://localhost:8080/ebook/save POST
2025-06-26 11:18:07.513 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634990592427040  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-26 11:18:07.513 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634990592427040  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:07.514 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634990592427040  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/293f7b1236804b9c9f61b832f410c694.png","description":"虎虎虎","id":6,"name":"虎鲸"}]
2025-06-26 11:18:07.521 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634990592427040  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-06-26 11:18:07.522 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634990592427040  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-26 11:18:07.543 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634990593410080  [0;39m ------------- 开始 -------------
2025-06-26 11:18:07.543 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634990593410080  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:07.543 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634990593410080  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:07.543 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634990593410080  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:07.543 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634990593410080  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:18:07.548 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634990593410080  [0;39m 总行数：12
2025-06-26 11:18:07.549 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634990593410080  [0;39m 总页数：6
2025-06-26 11:18:07.549 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634990593410080  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/293f7b1236804b9c9f61b832f410c694.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":12},"success":true}
2025-06-26 11:18:07.549 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634990593410080  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 11:18:15.198 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634990844249120  [0;39m ------------- 开始 -------------
2025-06-26 11:18:15.198 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634990844249120  [0;39m 请求地址: http://localhost:8080/ebook/remove GET
2025-06-26 11:18:15.198 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634990844249120  [0;39m 类名方法: com.gec.wiki.controller.EbookController.remove
2025-06-26 11:18:15.199 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634990844249120  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:15.199 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634990844249120  [0;39m 请求参数: [6]
2025-06-26 11:18:15.203 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634990844249120  [0;39m 返回结果: {"success":true}
2025-06-26 11:18:15.203 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634990844249120  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:18:15.215 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634990844806176  [0;39m ------------- 开始 -------------
2025-06-26 11:18:15.216 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634990844806176  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:15.216 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634990844806176  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:15.216 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634990844806176  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:15.216 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634990844806176  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:18:15.220 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634990844806176  [0;39m 总行数：11
2025-06-26 11:18:15.220 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634990844806176  [0;39m 总页数：6
2025-06-26 11:18:15.220 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634990844806176  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070114640928,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":11},"success":true}
2025-06-26 11:18:15.220 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634990844806176  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:18:17.367 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634990915322912  [0;39m ------------- 开始 -------------
2025-06-26 11:18:17.368 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634990915322912  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:17.368 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634990915322912  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:17.368 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634990915322912  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:17.368 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634990915322912  [0;39m 请求参数: [{"page":2,"size":2}]
2025-06-26 11:18:17.372 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634990915322912  [0;39m 总行数：11
2025-06-26 11:18:17.372 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634990915322912  [0;39m 总页数：6
2025-06-26 11:18:17.372 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634990915322912  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2}],"total":11},"success":true}
2025-06-26 11:18:17.372 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634990915322912  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:18:18.664 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634990957823008  [0;39m ------------- 开始 -------------
2025-06-26 11:18:18.664 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634990957823008  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:18.664 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634990957823008  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:18.664 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634990957823008  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:18.664 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634990957823008  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:18:18.668 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634990957823008  [0;39m 总行数：11
2025-06-26 11:18:18.668 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634990957823008  [0;39m 总页数：6
2025-06-26 11:18:18.668 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634990957823008  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070114640928,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":11},"success":true}
2025-06-26 11:18:18.668 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634990957823008  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 11:18:19.986 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991001142304  [0;39m ------------- 开始 -------------
2025-06-26 11:18:19.986 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991001142304  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:19.986 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991001142304  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:19.986 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991001142304  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:19.987 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991001142304  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 11:18:19.990 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991001142304  [0;39m 总行数：11
2025-06-26 11:18:19.990 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991001142304  [0;39m 总页数：6
2025-06-26 11:18:19.990 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991001142304  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":11},"success":true}
2025-06-26 11:18:19.990 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991001142304  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 11:18:21.622 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991054750752  [0;39m ------------- 开始 -------------
2025-06-26 11:18:21.622 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991054750752  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:21.623 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991054750752  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:21.623 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991054750752  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:21.623 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991054750752  [0;39m 请求参数: [{"page":2,"size":2}]
2025-06-26 11:18:21.626 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991054750752  [0;39m 总行数：11
2025-06-26 11:18:21.626 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991054750752  [0;39m 总页数：6
2025-06-26 11:18:21.626 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991054750752  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2}],"total":11},"success":true}
2025-06-26 11:18:21.626 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991054750752  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 11:18:22.326 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991077819424  [0;39m ------------- 开始 -------------
2025-06-26 11:18:22.326 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991077819424  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:22.327 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991077819424  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:22.327 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991077819424  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:22.327 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991077819424  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:18:22.330 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991077819424  [0;39m 总行数：11
2025-06-26 11:18:22.330 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991077819424  [0;39m 总页数：6
2025-06-26 11:18:22.331 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991077819424  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070114640928,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":11},"success":true}
2025-06-26 11:18:22.331 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991077819424  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:18:24.893 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991161934880  [0;39m ------------- 开始 -------------
2025-06-26 11:18:24.893 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991161934880  [0;39m 请求地址: http://localhost:8080/ebook/remove GET
2025-06-26 11:18:24.893 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991161934880  [0;39m 类名方法: com.gec.wiki.controller.EbookController.remove
2025-06-26 11:18:24.893 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991161934880  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:24.893 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991161934880  [0;39m 请求参数: [4632070114640928]
2025-06-26 11:18:24.899 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991161934880  [0;39m 返回结果: {"success":true}
2025-06-26 11:18:24.900 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991161934880  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-26 11:18:24.910 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991162491936  [0;39m ------------- 开始 -------------
2025-06-26 11:18:24.911 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991162491936  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:24.911 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991162491936  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:24.911 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991162491936  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:24.911 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991162491936  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:18:24.916 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991162491936  [0;39m 总行数：10
2025-06-26 11:18:24.916 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991162491936  [0;39m 总页数：5
2025-06-26 11:18:24.916 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991162491936  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070366135328,"name":"虎鲸1","viewCount":744,"voteCount":484}],"total":10},"success":true}
2025-06-26 11:18:24.916 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991162491936  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 11:18:31.143 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991366734880  [0;39m ------------- 开始 -------------
2025-06-26 11:18:31.144 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991366734880  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:31.144 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991366734880  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:31.144 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991366734880  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:31.144 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991366734880  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 11:18:31.148 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991366734880  [0;39m 总行数：10
2025-06-26 11:18:31.148 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991366734880  [0;39m 总页数：5
2025-06-26 11:18:31.148 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991366734880  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":10},"success":true}
2025-06-26 11:18:31.148 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991366734880  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:18:32.881 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991423685664  [0;39m ------------- 开始 -------------
2025-06-26 11:18:32.881 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991423685664  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:32.881 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991423685664  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:32.881 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991423685664  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:32.881 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991423685664  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:18:32.885 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991423685664  [0;39m 总行数：10
2025-06-26 11:18:32.885 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991423685664  [0;39m 总页数：5
2025-06-26 11:18:32.885 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991423685664  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632070366135328,"name":"虎鲸1","viewCount":744,"voteCount":484}],"total":10},"success":true}
2025-06-26 11:18:32.885 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991423685664  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 11:18:35.780 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991518680096  [0;39m ------------- 开始 -------------
2025-06-26 11:18:35.780 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991518680096  [0;39m 请求地址: http://localhost:8080/ebook/remove GET
2025-06-26 11:18:35.780 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991518680096  [0;39m 类名方法: com.gec.wiki.controller.EbookController.remove
2025-06-26 11:18:35.780 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991518680096  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:35.781 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991518680096  [0;39m 请求参数: [4632070366135328]
2025-06-26 11:18:35.788 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991518680096  [0;39m 返回结果: {"success":true}
2025-06-26 11:18:35.788 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991518680096  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-26 11:18:35.806 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991519532064  [0;39m ------------- 开始 -------------
2025-06-26 11:18:35.806 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991519532064  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:35.806 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991519532064  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:35.806 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991519532064  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:35.806 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991519532064  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:18:35.809 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991519532064  [0;39m 总行数：9
2025-06-26 11:18:35.810 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991519532064  [0;39m 总页数：5
2025-06-26 11:18:35.810 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991519532064  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":6,"category2Id":6,"cover":"/image/929aec79c41a4a91b7195f55a1962b60.gif","description":"6","id":4632073233073184,"name":"6"}],"total":9},"success":true}
2025-06-26 11:18:35.810 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991519532064  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 11:18:40.806 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991683372064  [0;39m ------------- 开始 -------------
2025-06-26 11:18:40.806 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991683372064  [0;39m 请求地址: http://localhost:8080/ebook/remove GET
2025-06-26 11:18:40.806 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991683372064  [0;39m 类名方法: com.gec.wiki.controller.EbookController.remove
2025-06-26 11:18:40.807 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991683372064  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:40.807 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991683372064  [0;39m 请求参数: [4632073233073184]
2025-06-26 11:18:40.812 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991683372064  [0;39m 返回结果: {"success":true}
2025-06-26 11:18:40.812 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991683372064  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 11:18:40.826 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991684027424  [0;39m ------------- 开始 -------------
2025-06-26 11:18:40.826 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991684027424  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:40.826 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991684027424  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:40.826 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991684027424  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:40.826 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991684027424  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:18:40.831 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991684027424  [0;39m 总行数：8
2025-06-26 11:18:40.831 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991684027424  [0;39m 总页数：4
2025-06-26 11:18:40.831 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991684027424  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":8},"success":true}
2025-06-26 11:18:40.832 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991684027424  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 11:18:42.920 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991752643616  [0;39m ------------- 开始 -------------
2025-06-26 11:18:42.920 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991752643616  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:42.920 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991752643616  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:42.920 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991752643616  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:42.920 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991752643616  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-26 11:18:42.924 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991752643616  [0;39m 总行数：8
2025-06-26 11:18:42.924 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991752643616  [0;39m 总页数：4
2025-06-26 11:18:42.924 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991752643616  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":8},"success":true}
2025-06-26 11:18:42.925 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991752643616  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:18:44.715 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634991811429408  [0;39m ------------- 开始 -------------
2025-06-26 11:18:44.715 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634991811429408  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:18:44.715 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634991811429408  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:18:44.715 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634991811429408  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:18:44.715 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634991811429408  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-26 11:18:44.718 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634991811429408  [0;39m 总行数：8
2025-06-26 11:18:44.719 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634991811429408  [0;39m 总页数：4
2025-06-26 11:18:44.719 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634991811429408  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":8},"success":true}
2025-06-26 11:18:44.719 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634991811429408  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:20:56.017 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634996113966112  [0;39m ------------- 开始 -------------
2025-06-26 11:20:56.017 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634996113966112  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:20:56.017 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634996113966112  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:20:56.018 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634996113966112  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:20:56.018 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634996113966112  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-26 11:20:56.023 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634996113966112  [0;39m 总行数：8
2025-06-26 11:20:56.023 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634996113966112  [0;39m 总页数：4
2025-06-26 11:20:56.023 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634996113966112  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":8},"success":true}
2025-06-26 11:20:56.023 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634996113966112  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 11:20:59.546 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634996229604384  [0;39m ------------- 开始 -------------
2025-06-26 11:20:59.547 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634996229604384  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:20:59.547 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634996229604384  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:20:59.547 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634996229604384  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:20:59.547 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634996229604384  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:20:59.550 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634996229604384  [0;39m 总行数：8
2025-06-26 11:20:59.550 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634996229604384  [0;39m 总页数：4
2025-06-26 11:20:59.551 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634996229604384  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":8},"success":true}
2025-06-26 11:20:59.551 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634996229604384  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:21:02.121 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634996313981984  [0;39m ------------- 开始 -------------
2025-06-26 11:21:02.121 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634996313981984  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:21:02.121 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634996313981984  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:21:02.121 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634996313981984  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:21:02.122 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634996313981984  [0;39m 请求参数: [{"page":2,"size":2}]
2025-06-26 11:21:02.125 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634996313981984  [0;39m 总行数：8
2025-06-26 11:21:02.125 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634996313981984  [0;39m 总页数：4
2025-06-26 11:21:02.126 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634996313981984  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2}],"total":8},"success":true}
2025-06-26 11:21:02.126 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634996313981984  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:21:03.488 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634996358775840  [0;39m ------------- 开始 -------------
2025-06-26 11:21:03.488 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634996358775840  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:21:03.488 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634996358775840  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:21:03.488 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634996358775840  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:21:03.488 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634996358775840  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-26 11:21:03.492 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634996358775840  [0;39m 总行数：8
2025-06-26 11:21:03.493 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634996358775840  [0;39m 总页数：4
2025-06-26 11:21:03.493 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634996358775840  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":8},"success":true}
2025-06-26 11:21:03.493 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634996358775840  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-26 11:21:07.226 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634996481262624  [0;39m ------------- 开始 -------------
2025-06-26 11:21:07.226 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634996481262624  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:21:07.226 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634996481262624  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:21:07.226 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634996481262624  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:21:07.226 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634996481262624  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-26 11:21:07.229 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634996481262624  [0;39m 总行数：8
2025-06-26 11:21:07.230 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634996481262624  [0;39m 总页数：4
2025-06-26 11:21:07.230 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634996481262624  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632089189385248,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":8},"success":true}
2025-06-26 11:21:07.230 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634996481262624  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 11:21:13.317 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634996680852512  [0;39m ------------- 开始 -------------
2025-06-26 11:21:13.317 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634996680852512  [0;39m 请求地址: http://localhost:8080/ebook/remove GET
2025-06-26 11:21:13.317 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634996680852512  [0;39m 类名方法: com.gec.wiki.controller.EbookController.remove
2025-06-26 11:21:13.317 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634996680852512  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:21:13.318 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634996680852512  [0;39m 请求参数: [4632089189385248]
2025-06-26 11:21:13.323 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634996680852512  [0;39m 返回结果: {"success":true}
2025-06-26 11:21:13.323 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634996680852512  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-26 11:21:13.335 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634996681442336  [0;39m ------------- 开始 -------------
2025-06-26 11:21:13.335 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634996681442336  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:21:13.335 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634996681442336  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:21:13.335 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634996681442336  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:21:13.335 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634996681442336  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-26 11:21:13.339 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634996681442336  [0;39m 总行数：7
2025-06-26 11:21:13.339 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634996681442336  [0;39m 总页数：4
2025-06-26 11:21:13.339 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634996681442336  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-06-26 11:21:13.339 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634996681442336  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 11:21:15.198 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4634996742489120  [0;39m ------------- 开始 -------------
2025-06-26 11:21:15.199 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4634996742489120  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-26 11:21:15.199 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4634996742489120  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-26 11:21:15.199 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4634996742489120  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-26 11:21:15.199 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4634996742489120  [0;39m 请求参数: [{"page":2,"size":2}]
2025-06-26 11:21:15.202 INFO  com.gec.wiki.service.EbookService                 :61   [32m4634996742489120  [0;39m 总行数：7
2025-06-26 11:21:15.202 INFO  com.gec.wiki.service.EbookService                 :62   [32m4634996742489120  [0;39m 总页数：4
2025-06-26 11:21:15.202 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4634996742489120  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2}],"total":7},"success":true}
2025-06-26 11:21:15.202 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4634996742489120  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-26 11:31:12.060 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-26 11:31:12.066 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
