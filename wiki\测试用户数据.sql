-- 插入测试用户用于忘记密码功能测试
-- 注意：请确保数据库已创建并运行了主要的表结构SQL

USE car_service;

-- 插入测试用户（车先生）
-- 密码是123456，已进行BCrypt加密
INSERT INTO users (username, password, real_name, phone, email, user_type, status) VALUES 
('user123', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '车先生', '19978452934', '<EMAIL>', 1, 1);

-- 插入更多测试用户
INSERT INTO users (username, password, real_name, phone, email, user_type, status) VALUES 
('zhangsan', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '张三', '13800138000', 'zhang<PERSON>@example.com', 1, 1),
('lisi', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '李四', '13900139000', '<EMAIL>', 2, 1),
('wangwu', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '王五', '13700137000', '<EMAIL>', 1, 1);

-- 查询插入的用户验证
SELECT id, username, real_name, phone, email, user_type FROM users WHERE username IN ('user123', 'zhangsan', 'lisi', 'wangwu');

/*
测试用户信息：
1. 车先生 (user123)
   - 手机：19978452934
   - 邮箱：<EMAIL>
   - 密码：123456
   
2. 张三 (zhangsan)  
   - 手机：13800138000
   - 邮箱：<EMAIL>
   - 密码：123456
   
3. 李四 (lisi)
   - 手机：13900139000
   - 邮箱：<EMAIL>
   - 密码：123456
   
4. 王五 (wangwu)
   - 手机：13700137000
   - 邮箱：<EMAIL>
   - 密码：123456

使用车先生的信息测试忘记密码功能：
- 真实姓名：车先生
- 手机号：19978452934
- 邮箱：<EMAIL>
*/
