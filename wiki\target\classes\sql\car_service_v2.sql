-- 汽车维修服务平台数据库表结构 V2.0
-- 创建数据库
CREATE DATABASE IF NOT EXISTS car_service DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE car_service;

-- 用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) COMMENT '真实姓名',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(200) COMMENT '头像',
    gender INT DEFAULT 0 COMMENT '性别(0-未知 1-男 2-女)',
    birthday DATE COMMENT '生日',
    address VARCHAR(200) COMMENT '地址',
    status INT DEFAULT 1 COMMENT '状态(0-禁用 1-正常)',
    user_type INT DEFAULT 1 COMMENT '用户类型(1-普通用户 2-VIP 3-管理员)',
    register_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    last_login_time DATETIME COMMENT '最后登录时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT = '用户表';

-- 分类表
CREATE TABLE category (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    parent BIGINT NOT NULL DEFAULT 0 COMMENT '父id',
    name VARCHAR(50) NOT NULL COMMENT '名称',
    icon VARCHAR(100) COMMENT '图标',
    sort INT DEFAULT 0 COMMENT '顺序',
    status INT DEFAULT 1 COMMENT '状态(0-禁用 1-启用)'
) COMMENT = '汽车维修服务分类';

-- 服务表
CREATE TABLE service (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    name VARCHAR(50) NOT NULL COMMENT '服务名称',
    category1_id BIGINT COMMENT '一级分类',
    category2_id BIGINT COMMENT '二级分类',
    description TEXT COMMENT '服务描述',
    content TEXT COMMENT '服务详细内容',
    cover VARCHAR(200) COMMENT '服务图片',
    images TEXT COMMENT '服务图片集(JSON格式)',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '服务价格',
    original_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '原价',
    booking_count INT DEFAULT 0 COMMENT '预约次数',
    complete_count INT DEFAULT 0 COMMENT '完成次数',
    rating_count INT DEFAULT 0 COMMENT '好评数',
    rating_score DECIMAL(3,1) DEFAULT 5.0 COMMENT '评分',
    duration INT DEFAULT 60 COMMENT '服务时长(分钟)',
    is_recommend INT DEFAULT 0 COMMENT '是否推荐(0-否 1-是)',
    status INT DEFAULT 1 COMMENT '状态(0-下架 1-上架)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT = '汽车维修服务';

-- 车辆表
CREATE TABLE vehicle (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    user_id BIGINT NOT NULL COMMENT '车主用户ID',
    license_plate VARCHAR(20) NOT NULL COMMENT '车牌号',
    brand VARCHAR(50) COMMENT '车辆品牌',
    model VARCHAR(50) COMMENT '车辆型号',
    color VARCHAR(20) COMMENT '车辆颜色',
    year INT COMMENT '车辆年份',
    engine_number VARCHAR(50) COMMENT '发动机号',
    vin VARCHAR(50) COMMENT '车架号',
    mileage INT DEFAULT 0 COMMENT '里程数',
    is_default INT DEFAULT 0 COMMENT '是否默认车辆(0-否 1-是)',
    status INT DEFAULT 1 COMMENT '状态(0-停用 1-正常)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_license_plate (license_plate)
) COMMENT = '车辆信息';

-- 技师表
CREATE TABLE technician (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    name VARCHAR(50) NOT NULL COMMENT '技师姓名',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(50) COMMENT '邮箱',
    avatar VARCHAR(200) COMMENT '头像',
    specialties VARCHAR(200) COMMENT '专业技能',
    experience_years INT DEFAULT 0 COMMENT '工作年限',
    level INT DEFAULT 1 COMMENT '技师等级 (1-初级 2-中级 3-高级 4-专家)',
    rating_score DECIMAL(3,1) DEFAULT 5.0 COMMENT '评分',
    rating_count INT DEFAULT 0 COMMENT '评价数量',
    salary DECIMAL(10,2) COMMENT '工资',
    status INT DEFAULT 1 COMMENT '状态 (0-停用 1-正常 2-忙碌)',
    hire_date DATETIME COMMENT '入职时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT = '技师信息';

-- 预约表
CREATE TABLE booking (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '预约ID',
    booking_no VARCHAR(32) NOT NULL UNIQUE COMMENT '预约编号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    vehicle_id BIGINT NOT NULL COMMENT '车辆ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    service_name VARCHAR(50) NOT NULL COMMENT '服务名称',
    service_price DECIMAL(10,2) NOT NULL COMMENT '服务价格',
    contact_name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    booking_date DATE NOT NULL COMMENT '预约日期',
    booking_time TIME NOT NULL COMMENT '预约时间',
    estimated_duration INT DEFAULT 60 COMMENT '预计时长(分钟)',
    problem_description TEXT COMMENT '问题描述',
    remark TEXT COMMENT '备注',
    status INT DEFAULT 1 COMMENT '状态(1-待确认 2-已确认 3-服务中 4-已完成 5-已取消)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_service_id (service_id),
    INDEX idx_technician_id (technician_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_status (status)
) COMMENT = '预约记录';

-- 订单表
CREATE TABLE orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID',
    order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '订单编号',
    booking_id BIGINT NOT NULL COMMENT '预约ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    actual_amount DECIMAL(10,2) NOT NULL COMMENT '实际支付金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    payment_method INT COMMENT '支付方式(1-微信 2-支付宝 3-银行卡)',
    payment_status INT DEFAULT 1 COMMENT '支付状态(1-待支付 2-已支付 3-已退款)',
    payment_time DATETIME COMMENT '支付时间',
    order_status INT DEFAULT 1 COMMENT '订单状态(1-待支付 2-已支付 3-服务中 4-已完成 5-已取消)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_booking_id (booking_id),
    INDEX idx_user_id (user_id),
    INDEX idx_order_status (order_status),
    INDEX idx_payment_status (payment_status)
) COMMENT = '订单表';

-- 支付记录表
CREATE TABLE payment_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '支付记录ID',
    payment_no VARCHAR(32) NOT NULL UNIQUE COMMENT '支付流水号',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    payment_method INT NOT NULL COMMENT '支付方式(1-微信 2-支付宝 3-银行卡)',
    payment_amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    payment_status INT DEFAULT 1 COMMENT '支付状态(1-待支付 2-支付成功 3-支付失败 4-已退款)',
    third_party_order_no VARCHAR(64) COMMENT '第三方订单号',
    payment_time DATETIME COMMENT '支付时间',
    callback_time DATETIME COMMENT '回调时间',
    remark TEXT COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_status (payment_status)
) COMMENT = '支付记录';

-- 维修记录表
CREATE TABLE maintenance_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    booking_id BIGINT NOT NULL COMMENT '预约ID',
    order_id BIGINT COMMENT '订单ID',
    vehicle_id BIGINT NOT NULL COMMENT '车辆ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    problem_description TEXT COMMENT '问题描述',
    solution TEXT COMMENT '解决方案',
    maintenance_content TEXT COMMENT '维修内容',
    parts_used TEXT COMMENT '使用配件(JSON格式)',
    before_images TEXT COMMENT '维修前照片(JSON格式)',
    after_images TEXT COMMENT '维修后照片(JSON格式)',
    cost DECIMAL(10,2) DEFAULT 0.00 COMMENT '维修费用',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    status INT DEFAULT 1 COMMENT '状态 (1-进行中 2-已完成)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_booking_id (booking_id),
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_user_id (user_id),
    INDEX idx_service_id (service_id),
    INDEX idx_technician_id (technician_id),
    INDEX idx_status (status)
) COMMENT = '维修记录';

-- 评价表
CREATE TABLE review (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '评价ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    booking_id BIGINT NOT NULL COMMENT '预约ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    service_score INT NOT NULL COMMENT '服务评分(1-5)',
    technician_score INT COMMENT '技师评分(1-5)',
    content TEXT COMMENT '评价内容',
    images TEXT COMMENT '评价图片(JSON格式)',
    reply_content TEXT COMMENT '商家回复',
    reply_time DATETIME COMMENT '回复时间',
    status INT DEFAULT 1 COMMENT '状态(1-正常 2-隐藏)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_service_id (service_id),
    INDEX idx_technician_id (technician_id)
) COMMENT = '评价表';

-- 轮播图表
CREATE TABLE banner (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
    title VARCHAR(100) NOT NULL COMMENT '标题',
    image VARCHAR(200) NOT NULL COMMENT '图片地址',
    link_url VARCHAR(200) COMMENT '链接地址',
    sort INT DEFAULT 0 COMMENT '排序',
    status INT DEFAULT 1 COMMENT '状态(0-禁用 1-启用)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT = '轮播图';

-- 插入管理员用户
INSERT INTO users (id, username, password, real_name, phone, email, user_type, status) VALUES 
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '系统管理员', '13800138000', '<EMAIL>', 3, 1);

-- 插入示例分类数据
INSERT INTO category (id, parent, name, icon, sort, status) VALUES 
(100, 0, '常规保养', 'icon-maintenance', 1, 1),
(101, 100, '机油保养', 'icon-oil', 1, 1),
(102, 100, '轮胎保养', 'icon-tire', 2, 1),
(103, 100, '制动系统', 'icon-brake', 3, 1),
(200, 0, '发动机维修', 'icon-engine', 2, 1),
(201, 200, '发动机检修', 'icon-engine-repair', 1, 1),
(202, 200, '冷却系统', 'icon-cooling', 2, 1),
(300, 0, '电气系统', 'icon-electric', 3, 1),
(301, 300, '电瓶维护', 'icon-battery', 1, 1),
(302, 300, '空调系统', 'icon-ac', 2, 1);

-- 插入示例服务数据
INSERT INTO service (id, name, category1_id, category2_id, description, content, cover, price, original_price, duration, is_recommend, status) VALUES 
(1, '机油更换', 100, 101, '更换发动机机油，保证发动机正常运行', '使用优质机油，延长发动机寿命，提升动力性能。服务包括：检查机油品质、更换机油滤清器、添加新机油、检测发动机运行状态。', '/images/service/oil-change.jpg', 150.00, 180.00, 30, 1, 1),
(2, '轮胎更换', 100, 102, '更换磨损轮胎，确保行车安全', '专业轮胎更换服务，包括轮胎检测、拆装、动平衡调校等。确保行车安全，提升驾驶体验。', '/images/service/tire-change.jpg', 400.00, 500.00, 45, 1, 1),
(3, '刹车片更换', 100, 103, '更换磨损刹车片，保证制动效果', '使用原厂品质刹车片，确保制动性能。服务包括刹车系统检测、刹车片更换、制动液检查等。', '/images/service/brake-pad.jpg', 300.00, 350.00, 60, 0, 1),
(4, '发动机大修', 200, 201, '发动机全面检修，解决动力不足问题', '专业发动机大修服务，彻底解决发动机故障。包括拆解检测、零部件更换、性能调校等。', '/images/service/engine-repair.jpg', 2500.00, 3000.00, 480, 0, 1),
(5, '水箱清洗', 200, 202, '清洗水箱，保证发动机正常冷却', '专业水箱清洗服务，清除水垢和杂质，保证冷却系统正常工作，防止发动机过热。', '/images/service/radiator-clean.jpg', 120.00, 150.00, 40, 1, 1),
(6, '电瓶检测', 300, 301, '检测电瓶性能，预防启动故障', '专业电瓶检测服务，检查电瓶容量、电压、内阻等参数，及时发现电瓶问题。', '/images/service/battery-test.jpg', 50.00, 80.00, 20, 1, 1),
(7, '空调清洗', 300, 302, '清洗空调系统，改善车内空气质量', '深度清洗空调系统，清除细菌和异味，提升空气质量，保障健康出行。', '/images/service/ac-clean.jpg', 180.00, 220.00, 50, 1, 1);

-- 插入示例技师数据
INSERT INTO technician (id, name, phone, avatar, specialties, experience_years, level, rating_score, rating_count, status) VALUES 
(1, '张师傅', '13800001001', '/images/technician/zhang.jpg', '发动机维修,变速箱维修', 15, 4, 4.8, 128, 1),
(2, '李师傅', '13800001002', '/images/technician/li.jpg', '电气系统,空调维修', 8, 3, 4.6, 89, 1),
(3, '王师傅', '13800001003', '/images/technician/wang.jpg', '轮胎更换,制动系统', 5, 2, 4.7, 56, 1),
(4, '赵师傅', '13800001004', '/images/technician/zhao.jpg', '机油保养,常规维护', 3, 1, 4.5, 34, 1);

-- 插入轮播图数据
INSERT INTO banner (id, title, image, link_url, sort, status) VALUES 
(1, '专业汽车维修服务', '/images/banner/banner1.jpg', '', 1, 1),
(2, '快捷便民服务', '/images/banner/banner2.jpg', '', 2, 1),
(3, '资深技师团队', '/images/banner/banner3.jpg', '', 3, 1);
