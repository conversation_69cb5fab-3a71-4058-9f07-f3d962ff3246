-- 一键修复维修店预约功能脚本
-- 包含所有必要的修复步骤

USE car_service;

-- ===============================
-- 第一步：修复service表结构
-- ===============================
SELECT '正在检查service表结构...' as step;

-- 添加shop_id字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = 'car_service' 
     AND table_name = 'service' 
     AND column_name = 'shop_id') = 0,
    'ALTER TABLE service ADD COLUMN shop_id BIGINT NULL COMMENT "维修店ID(空表示平台服务)" AFTER status',
    'SELECT "shop_id字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM information_schema.statistics 
     WHERE table_schema = 'car_service' 
     AND table_name = 'service' 
     AND index_name = 'idx_service_shop_id') = 0,
    'CREATE INDEX idx_service_shop_id ON service(shop_id)',
    'SELECT "shop_id索引已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有服务设置shop_id
UPDATE service SET shop_id = 1 WHERE shop_id IS NULL;

SELECT '✅ service表结构修复完成' as result;

-- ===============================
-- 第二步：检查booking表结构
-- ===============================
SELECT '正在检查booking表结构...' as step;

-- 检查必要字段是否存在
SELECT 
    CASE WHEN COUNT(*) = 0 THEN '❌ booking_time字段缺失' 
         ELSE '✅ booking_time字段存在' END as booking_time_status
FROM information_schema.columns 
WHERE table_schema = 'car_service' AND table_name = 'booking' AND column_name = 'booking_time';

SELECT 
    CASE WHEN COUNT(*) = 0 THEN '❌ booking_no字段缺失' 
         ELSE '✅ booking_no字段存在' END as booking_no_status
FROM information_schema.columns 
WHERE table_schema = 'car_service' AND table_name = 'booking' AND column_name = 'booking_no';

-- ===============================
-- 第三步：插入测试数据
-- ===============================
SELECT '正在准备测试数据...' as step;

-- 插入基础用户数据
INSERT IGNORE INTO users (id, username, password, real_name, phone, email, user_type, status, create_time) VALUES 
(2, 'customer1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '张先生', '13800138001', '<EMAIL>', 1, 1, NOW()),
(3, 'customer2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '李女士', '13800138002', '<EMAIL>', 1, 1, NOW()),
(4, 'customer3', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '王先生', '13800138003', '<EMAIL>', 1, 1, NOW());

-- 插入车辆数据
INSERT IGNORE INTO vehicle (id, user_id, license_plate, brand, model, color, year, is_default, status, create_time) VALUES 
(1, 2, '京A12345', '大众', '朗逸', '白色', 2020, 1, 1, NOW()),
(2, 3, '京B23456', '丰田', '凯美瑞', '黑色', 2019, 1, 1, NOW()),
(3, 4, '京C34567', '本田', '雅阁', '银色', 2021, 1, 1, NOW());

-- 插入技师数据
INSERT IGNORE INTO technician (id, name, phone, specialties, experience_years, level, rating_score, rating_count, status, create_time) VALUES 
(1, '张师傅', '13800001001', '发动机维修,变速箱维修', 15, 4, 4.8, 128, 1, NOW()),
(2, '李师傅', '13800001002', '电气系统,空调维修', 8, 3, 4.6, 89, 1, NOW()),
(3, '王师傅', '13800001003', '轮胎更换,制动系统', 5, 2, 4.7, 56, 1, NOW());

-- 清理今日预约数据（重新开始）
DELETE FROM booking WHERE booking_date = CURDATE();

-- 插入今日预约测试数据
INSERT INTO booking (booking_no, user_id, vehicle_id, service_id, technician_id, service_name, service_price, contact_name, contact_phone, booking_date, booking_time, estimated_duration, problem_description, remark, status, create_time, update_time) VALUES 
('B' + DATE_FORMAT(NOW(), '%Y%m%d') + '001', 2, 1, 1, 1, '机油更换', 150.00, '张先生', '13800138001', CURDATE(), '09:00:00', 30, '需要更换机油，机油已经使用5000公里', '预约时间请准时', 1, NOW(), NOW()),
('B' + DATE_FORMAT(NOW(), '%Y%m%d') + '002', 3, 2, 2, 2, '轮胎更换', 400.00, '李女士', '13800138002', CURDATE(), '14:30:00', 45, '前轮轮胎磨损严重，需要更换', '更换后请检查动平衡', 2, NOW(), NOW()),
('B' + DATE_FORMAT(NOW(), '%Y%m%d') + '003', 4, 3, 3, 3, '刹车片更换', 300.00, '王先生', '13800138003', CURDATE(), '16:00:00', 60, '刹车有异响，怀疑刹车片磨损', '刹车系统全面检查', 3, NOW(), NOW()),
('B' + DATE_FORMAT(NOW(), '%Y%m%d') + '004', 2, 1, 5, 1, '水箱清洗', 120.00, '赵师傅', '13800138004', CURDATE(), '11:00:00', 40, '水温偏高，需要清洗水箱', '检查冷却液是否需要更换', 1, NOW(), NOW()),
('B' + DATE_FORMAT(NOW(), '%Y%m%d') + '005', 3, 2, 6, 2, '电瓶检测', 50.00, '陈师傅', '13800138005', CURDATE(), '15:00:00', 20, '启动困难，怀疑电瓶问题', '如有问题建议更换', 4, NOW(), NOW());

-- 插入月度统计数据
INSERT IGNORE INTO booking (booking_no, user_id, vehicle_id, service_id, technician_id, service_name, service_price, contact_name, contact_phone, booking_date, booking_time, estimated_duration, problem_description, status, create_time, update_time) VALUES 
('B20250901001', 2, 1, 1, 1, '机油更换', 150.00, '张先生', '13800138001', '2025-09-01', '09:00:00', 30, '定期保养', 4, '2025-09-01 09:00:00', '2025-09-01 12:00:00'),
('B20250902001', 3, 2, 2, 2, '轮胎更换', 400.00, '李女士', '13800138002', '2025-09-02', '14:00:00', 45, '轮胎老化', 4, '2025-09-02 14:00:00', '2025-09-02 17:00:00'),
('B20250903001', 4, 3, 7, 3, '空调清洗', 180.00, '王先生', '13800138003', '2025-09-03', '16:30:00', 50, '空调有异味', 4, '2025-09-03 16:30:00', '2025-09-03 19:00:00');

SELECT '✅ 测试数据插入完成' as result;

-- ===============================
-- 第四步：验证修复结果
-- ===============================
SELECT '正在验证修复结果...' as step;

-- 验证今日预约查询
SELECT '📋 今日预约数据:' as info;
SELECT b.id, b.booking_no, b.service_name, b.contact_name, b.booking_time, 
       CASE b.status 
           WHEN 1 THEN '待确认'
           WHEN 2 THEN '已确认' 
           WHEN 3 THEN '服务中'
           WHEN 4 THEN '已完成'
           WHEN 5 THEN '已取消'
       END as status_text
FROM booking b
JOIN service s ON b.service_id = s.id
WHERE b.booking_date = CURDATE() AND s.shop_id = 1
ORDER BY b.booking_time;

-- 验证统计数据
SELECT '📊 统计数据:' as info;
SELECT 
    SUM(CASE WHEN b.status = 1 THEN 1 ELSE 0 END) AS pendingOrders,
    SUM(CASE WHEN b.status IN (2, 3) THEN 1 ELSE 0 END) AS processingOrders,
    SUM(CASE WHEN b.status = 4 THEN 1 ELSE 0 END) AS completedOrders,
    COALESCE(SUM(CASE 
        WHEN b.status = 4 
        AND b.booking_date >= DATE_FORMAT(NOW(), '%Y-%m-01')
        AND b.booking_date < DATE_ADD(DATE_FORMAT(NOW(), '%Y-%m-01'), INTERVAL 1 MONTH)
        THEN b.service_price 
        ELSE 0 
    END), 0) AS monthlyRevenue
FROM booking b
INNER JOIN service s ON b.service_id = s.id
WHERE s.shop_id = 1;

SELECT '🎉 维修店预约功能修复完成！' as final_result;
SELECT '请重启Spring Boot应用，然后刷新页面测试功能。' as next_step;
