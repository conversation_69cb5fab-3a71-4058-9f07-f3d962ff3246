package com.gec.wiki;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;

import javax.annotation.Resource;

/**
 * 邮件发送测试
 */
@SpringBootTest
public class EmailTest {
    
    @Resource
    private JavaMailSender mailSender;
    
    @Test
    public void testSendEmail() {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom("<EMAIL>");
            message.setTo("<EMAIL>"); // 发送给自己测试
            message.setSubject("邮件发送测试");
            message.setText("这是一条测试邮件，如果收到说明邮件配置正确。");
            
            mailSender.send(message);
            System.out.println("邮件发送成功！");
        } catch (Exception e) {
            System.err.println("邮件发送失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
