-- 安全修复booking表结构脚本
-- 此脚本会先检查字段是否存在，避免重复添加

USE car_service;

-- 创建存储过程来安全添加字段
DELIMITER $$

CREATE PROCEDURE SafeAddColumn()
BEGIN
    DECLARE column_exists INT DEFAULT 0;
    
    -- 检查booking_time字段是否存在
    SELECT COUNT(*) INTO column_exists
    FROM information_schema.columns 
    WHERE table_schema = 'car_service' 
    AND table_name = 'booking' 
    AND column_name = 'booking_time';
    
    IF column_exists = 0 THEN
        ALTER TABLE booking ADD COLUMN booking_time TIME NOT NULL DEFAULT '09:00:00' COMMENT '预约时间' AFTER booking_date;
        SELECT 'Added booking_time column' as message;
    ELSE
        SELECT 'booking_time column already exists' as message;
    END IF;

    -- 检查booking_no字段是否存在
    SELECT COUNT(*) INTO column_exists
    FROM information_schema.columns 
    WHERE table_schema = 'car_service' 
    AND table_name = 'booking' 
    AND column_name = 'booking_no';
    
    IF column_exists = 0 THEN
        ALTER TABLE booking ADD COLUMN booking_no VARCHAR(32) UNIQUE COMMENT '预约编号' AFTER id;
        SELECT 'Added booking_no column' as message;
    ELSE
        SELECT 'booking_no column already exists' as message;
    END IF;

    -- 检查estimated_duration字段是否存在
    SELECT COUNT(*) INTO column_exists
    FROM information_schema.columns 
    WHERE table_schema = 'car_service' 
    AND table_name = 'booking' 
    AND column_name = 'estimated_duration';
    
    IF column_exists = 0 THEN
        ALTER TABLE booking ADD COLUMN estimated_duration INT DEFAULT 60 COMMENT '预计时长(分钟)';
        SELECT 'Added estimated_duration column' as message;
    ELSE
        SELECT 'estimated_duration column already exists' as message;
    END IF;

    -- 检查problem_description字段是否存在
    SELECT COUNT(*) INTO column_exists
    FROM information_schema.columns 
    WHERE table_schema = 'car_service' 
    AND table_name = 'booking' 
    AND column_name = 'problem_description';
    
    IF column_exists = 0 THEN
        ALTER TABLE booking ADD COLUMN problem_description TEXT COMMENT '问题描述';
        SELECT 'Added problem_description column' as message;
    ELSE
        SELECT 'problem_description column already exists' as message;
    END IF;

    -- 检查remark字段是否存在
    SELECT COUNT(*) INTO column_exists
    FROM information_schema.columns 
    WHERE table_schema = 'car_service' 
    AND table_name = 'booking' 
    AND column_name = 'remark';
    
    IF column_exists = 0 THEN
        ALTER TABLE booking ADD COLUMN remark TEXT COMMENT '备注';
        SELECT 'Added remark column' as message;
    ELSE
        SELECT 'remark column already exists' as message;
    END IF;

END$$

DELIMITER ;

-- 执行存储过程
CALL SafeAddColumn();

-- 删除存储过程
DROP PROCEDURE SafeAddColumn;

-- 显示当前表结构
DESCRIBE booking;

SELECT 'booking表结构检查和修复完成！' as result;
