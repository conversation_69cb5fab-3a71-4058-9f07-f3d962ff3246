2025-07-03 22:28:20.647 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 30452 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 22:28:20.650 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 22:28:21.853 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m <PERSON>cat initialized with port(s): 8080 (http)
2025-07-03 22:28:21.863 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 22:28:21.864 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 22:28:21.864 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 22:28:21.952 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 22:28:21.953 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1253 ms
2025-07-03 22:28:23.309 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 22:28:23.507 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 22:28:23.524 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 22:28:23.533 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.355 seconds (JVM running for 4.445)
2025-07-03 22:28:23.536 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 22:28:23.537 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 22:28:49.496 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 22:28:49.497 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 22:28:49.498 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-03 22:28:49.562 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127330223136  [0;39m ------------- 开始 -------------
2025-07-03 22:28:49.563 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127330223136  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 22:28:49.564 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127330354208  [0;39m ------------- 开始 -------------
2025-07-03 22:28:49.564 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127330223136  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 22:28:49.564 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127330354208  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:28:49.565 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127330223136  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:28:49.565 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127330354208  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:28:49.565 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127330354208  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:28:49.633 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127330354208  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 22:28:49.633 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127330223136  [0;39m 请求参数: [{}]
2025-07-03 22:28:49.757 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656127330223136  [0;39m {dataSource-1} inited
2025-07-03 22:28:50.184 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127330223136  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 22:28:50.184 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127330223136  [0;39m ------------- 结束 耗时：624 ms -------------
2025-07-03 22:28:50.185 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656127330354208  [0;39m 总行数：22
2025-07-03 22:28:50.186 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656127330354208  [0;39m 总页数：1
2025-07-03 22:28:50.204 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127330354208  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 22:28:50.205 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127330354208  [0;39m ------------- 结束 耗时：641 ms -------------
2025-07-03 22:28:50.357 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127356339232  [0;39m ------------- 开始 -------------
2025-07-03 22:28:50.357 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127356339232  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:28:50.357 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127356339232  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:28:50.357 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127356339232  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:28:50.357 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127356339232  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 22:28:50.371 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656127356339232  [0;39m 总行数：22
2025-07-03 22:28:50.372 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656127356339232  [0;39m 总页数：1
2025-07-03 22:28:50.373 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127356339232  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 22:28:50.374 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127356339232  [0;39m ------------- 结束 耗时：17 ms -------------
2025-07-03 22:28:53.477 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127458575392  [0;39m ------------- 开始 -------------
2025-07-03 22:28:53.477 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127458575392  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:28:53.477 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127458575392  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:28:53.478 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127458575392  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:28:53.478 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127458575392  [0;39m 请求参数: [{"category2Id":215,"page":1,"size":1000}]
2025-07-03 22:28:53.494 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656127458575392  [0;39m 总行数：0
2025-07-03 22:28:53.494 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656127458575392  [0;39m 总页数：0
2025-07-03 22:28:53.494 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127458575392  [0;39m 返回结果: {"content":{"list":[],"total":0},"success":true}
2025-07-03 22:28:53.496 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127458575392  [0;39m ------------- 结束 耗时：19 ms -------------
2025-07-03 22:28:54.485 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127491605536  [0;39m ------------- 开始 -------------
2025-07-03 22:28:54.487 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127491605536  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:28:54.487 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127491605536  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:28:54.488 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127491605536  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:28:54.488 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127491605536  [0;39m 请求参数: [{"category2Id":213,"page":1,"size":1000}]
2025-07-03 22:28:54.497 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656127491605536  [0;39m 总行数：4
2025-07-03 22:28:54.497 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656127491605536  [0;39m 总页数：1
2025-07-03 22:28:54.497 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127491605536  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":4},"success":true}
2025-07-03 22:28:54.497 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127491605536  [0;39m ------------- 结束 耗时：12 ms -------------
2025-07-03 22:28:55.406 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127521784864  [0;39m ------------- 开始 -------------
2025-07-03 22:28:55.406 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127521784864  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:28:55.406 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127521784864  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:28:55.406 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127521784864  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:28:55.406 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127521784864  [0;39m 请求参数: [{"category2Id":214,"page":1,"size":1000}]
2025-07-03 22:28:55.413 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656127521784864  [0;39m 总行数：0
2025-07-03 22:28:55.413 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656127521784864  [0;39m 总页数：0
2025-07-03 22:28:55.413 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127521784864  [0;39m 返回结果: {"content":{"list":[],"total":0},"success":true}
2025-07-03 22:28:55.413 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127521784864  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 22:28:56.897 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127570641952  [0;39m ------------- 开始 -------------
2025-07-03 22:28:56.897 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127570641952  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:28:56.897 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127570641952  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:28:56.897 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127570641952  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:28:56.897 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127570641952  [0;39m 请求参数: [{"category2Id":212,"page":1,"size":1000}]
2025-07-03 22:28:56.897 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656127570641952  [0;39m 总行数：1
2025-07-03 22:28:56.913 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656127570641952  [0;39m 总页数：1
2025-07-03 22:28:56.913 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127570641952  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3}],"total":1},"success":true}
2025-07-03 22:28:56.913 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127570641952  [0;39m ------------- 结束 耗时：16 ms -------------
2025-07-03 22:28:58.269 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127615599648  [0;39m ------------- 开始 -------------
2025-07-03 22:28:58.269 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127615599648  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:28:58.269 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127615599648  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:28:58.269 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127615599648  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:28:58.269 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127615599648  [0;39m 请求参数: [{"category2Id":215,"page":1,"size":1000}]
2025-07-03 22:28:58.274 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656127615599648  [0;39m 总行数：0
2025-07-03 22:28:58.274 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656127615599648  [0;39m 总页数：0
2025-07-03 22:28:58.274 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127615599648  [0;39m 返回结果: {"content":{"list":[],"total":0},"success":true}
2025-07-03 22:28:58.274 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127615599648  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-03 22:29:00.117 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127676154912  [0;39m ------------- 开始 -------------
2025-07-03 22:29:00.119 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127676154912  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:00.119 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127676154912  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:00.120 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127676154912  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:00.120 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127676154912  [0;39m 请求参数: [{"category2Id":213,"page":1,"size":1000}]
2025-07-03 22:29:00.127 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656127676154912  [0;39m 总行数：4
2025-07-03 22:29:00.127 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656127676154912  [0;39m 总页数：1
2025-07-03 22:29:00.128 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127676154912  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":4},"success":true}
2025-07-03 22:29:00.129 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127676154912  [0;39m ------------- 结束 耗时：12 ms -------------
2025-07-03 22:29:03.278 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127779734560  [0;39m ------------- 开始 -------------
2025-07-03 22:29:03.278 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127779734561  [0;39m ------------- 开始 -------------
2025-07-03 22:29:03.279 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127779734560  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:03.279 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127779734561  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 22:29:03.280 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127779734561  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 22:29:03.280 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127779734560  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:03.280 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127779734560  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:03.280 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127779734561  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:03.281 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127779734561  [0;39m 请求参数: [{}]
2025-07-03 22:29:03.281 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127779734560  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 22:29:03.292 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127779734561  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 22:29:03.293 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127779734561  [0;39m ------------- 结束 耗时：15 ms -------------
2025-07-03 22:29:03.295 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656127779734560  [0;39m 总行数：22
2025-07-03 22:29:03.295 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656127779734560  [0;39m 总页数：5
2025-07-03 22:29:03.295 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127779734560  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 22:29:03.298 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127779734560  [0;39m ------------- 结束 耗时：20 ms -------------
2025-07-03 22:29:07.452 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656127916508192  [0;39m ------------- 开始 -------------
2025-07-03 22:29:07.453 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656127916508192  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 22:29:07.453 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656127916508192  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 22:29:07.454 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656127916508192  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:07.454 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656127916508192  [0;39m 请求参数: [{"name":""}]
2025-07-03 22:29:07.460 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656127916508192  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 22:29:07.460 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656127916508192  [0;39m ------------- 结束 耗时：8 ms -------------
2025-07-03 22:29:11.613 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128052855840  [0;39m ------------- 开始 -------------
2025-07-03 22:29:11.614 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128052855840  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 22:29:11.614 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128052855840  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 22:29:11.615 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128052855840  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:11.615 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128052855840  [0;39m 请求参数: [{"name":""}]
2025-07-03 22:29:11.623 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128052855840  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 22:29:11.623 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128052855840  [0;39m ------------- 结束 耗时：10 ms -------------
2025-07-03 22:29:13.690 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128120914976  [0;39m ------------- 开始 -------------
2025-07-03 22:29:13.691 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128120947744  [0;39m ------------- 开始 -------------
2025-07-03 22:29:13.691 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128120914976  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 22:29:13.691 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128120947744  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:13.691 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128120947744  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:13.691 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128120914976  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 22:29:13.692 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128120914976  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:13.692 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128120947744  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:13.692 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128120947744  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 22:29:13.692 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128120914976  [0;39m 请求参数: [{}]
2025-07-03 22:29:13.697 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128120914976  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 22:29:13.697 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128120914976  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 22:29:13.699 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656128120947744  [0;39m 总行数：22
2025-07-03 22:29:13.699 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656128120947744  [0;39m 总页数：5
2025-07-03 22:29:13.700 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128120947744  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 22:29:13.701 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128120947744  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 22:29:28.135 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128594248736  [0;39m ------------- 开始 -------------
2025-07-03 22:29:28.135 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128594248736  [0;39m 请求地址: http://localhost:8080/ebook/save POST
2025-07-03 22:29:28.135 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128594248736  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-07-03 22:29:28.135 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128594248736  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:28.135 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128594248736  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}]
2025-07-03 22:29:28.170 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128594248736  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-07-03 22:29:28.170 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128594248736  [0;39m ------------- 结束 耗时：35 ms -------------
2025-07-03 22:29:28.203 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128596476960  [0;39m ------------- 开始 -------------
2025-07-03 22:29:28.204 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128596476960  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:28.204 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128596476960  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:28.204 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128596476960  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:28.205 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128596476960  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 22:29:28.212 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656128596476960  [0;39m 总行数：22
2025-07-03 22:29:28.213 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656128596476960  [0;39m 总页数：5
2025-07-03 22:29:28.213 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128596476960  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 22:29:28.214 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128596476960  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 22:29:31.765 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128713196576  [0;39m ------------- 开始 -------------
2025-07-03 22:29:31.764 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128713163808  [0;39m ------------- 开始 -------------
2025-07-03 22:29:31.765 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128713196576  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:31.765 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128713163808  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 22:29:31.766 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128713196576  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:31.766 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128713163808  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 22:29:31.766 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128713196576  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:31.766 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128713163808  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:31.766 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128713163808  [0;39m 请求参数: [{}]
2025-07-03 22:29:31.766 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128713196576  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 22:29:31.770 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128713163808  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 22:29:31.770 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128713163808  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-03 22:29:31.774 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656128713196576  [0;39m 总行数：22
2025-07-03 22:29:31.774 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656128713196576  [0;39m 总页数：1
2025-07-03 22:29:31.774 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128713196576  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 22:29:31.774 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128713196576  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-03 22:29:31.804 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128714474528  [0;39m ------------- 开始 -------------
2025-07-03 22:29:31.804 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128714474528  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:31.805 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128714474528  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:31.805 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128714474528  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:31.805 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128714474528  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 22:29:31.811 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656128714474528  [0;39m 总行数：22
2025-07-03 22:29:31.811 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656128714474528  [0;39m 总页数：1
2025-07-03 22:29:31.812 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128714474528  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 22:29:31.813 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128714474528  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-03 22:29:33.413 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128767198240  [0;39m ------------- 开始 -------------
2025-07-03 22:29:33.413 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128767198241  [0;39m ------------- 开始 -------------
2025-07-03 22:29:33.414 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128767198240  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 22:29:33.414 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128767198241  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:33.414 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128767198240  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 22:29:33.414 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128767198241  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:33.415 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128767198240  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:33.415 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128767198240  [0;39m 请求参数: [{}]
2025-07-03 22:29:33.415 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128767198241  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:33.415 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128767198241  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 22:29:33.419 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128767198240  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 22:29:33.420 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128767198240  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 22:29:33.422 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656128767198241  [0;39m 总行数：22
2025-07-03 22:29:33.423 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656128767198241  [0;39m 总页数：5
2025-07-03 22:29:33.424 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128767198241  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 22:29:33.424 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128767198241  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 22:29:36.697 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128874808353  [0;39m ------------- 开始 -------------
2025-07-03 22:29:36.697 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128874808352  [0;39m ------------- 开始 -------------
2025-07-03 22:29:36.698 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128874808353  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:36.698 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128874808353  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:36.698 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128874808352  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 22:29:36.698 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128874808353  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:36.698 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128874808352  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 22:29:36.699 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128874808353  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 22:29:36.699 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128874808352  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:36.700 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128874808352  [0;39m 请求参数: [{}]
2025-07-03 22:29:36.705 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128874808352  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 22:29:36.705 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128874808352  [0;39m ------------- 结束 耗时：8 ms -------------
2025-07-03 22:29:36.708 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656128874808353  [0;39m 总行数：22
2025-07-03 22:29:36.709 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656128874808353  [0;39m 总页数：1
2025-07-03 22:29:36.710 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128874808353  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 22:29:36.711 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128874808353  [0;39m ------------- 结束 耗时：14 ms -------------
2025-07-03 22:29:36.734 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656128876020768  [0;39m ------------- 开始 -------------
2025-07-03 22:29:36.734 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656128876020768  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:36.734 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656128876020768  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:36.734 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656128876020768  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:36.734 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656128876020768  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 22:29:36.741 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656128876020768  [0;39m 总行数：22
2025-07-03 22:29:36.742 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656128876020768  [0;39m 总页数：1
2025-07-03 22:29:36.743 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656128876020768  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 22:29:36.744 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656128876020768  [0;39m ------------- 结束 耗时：10 ms -------------
2025-07-03 22:29:42.467 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656129063879712  [0;39m ------------- 开始 -------------
2025-07-03 22:29:42.468 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656129063879712  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 22:29:42.468 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656129063879712  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 22:29:42.469 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656129063879712  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:42.469 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656129063945248  [0;39m ------------- 开始 -------------
2025-07-03 22:29:42.469 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656129063879712  [0;39m 请求参数: [{}]
2025-07-03 22:29:42.469 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656129063945248  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:42.469 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656129063945248  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:42.470 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656129063945248  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:42.470 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656129063945248  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 22:29:42.472 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656129063879712  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 22:29:42.473 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656129063879712  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-03 22:29:42.476 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656129063945248  [0;39m 总行数：22
2025-07-03 22:29:42.477 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656129063945248  [0;39m 总页数：5
2025-07-03 22:29:42.477 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656129063945248  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 22:29:42.478 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656129063945248  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-03 22:29:45.251 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656129155105824  [0;39m ------------- 开始 -------------
2025-07-03 22:29:45.252 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656129155105824  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 22:29:45.253 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656129155105824  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 22:29:45.253 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656129155105824  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:45.253 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656129155105824  [0;39m 请求参数: [{"name":""}]
2025-07-03 22:29:45.256 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656129155105824  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 22:29:45.256 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656129155105824  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-03 22:29:48.911 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656129275036704  [0;39m ------------- 开始 -------------
2025-07-03 22:29:48.911 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656129275036705  [0;39m ------------- 开始 -------------
2025-07-03 22:29:48.912 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656129275036704  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 22:29:48.912 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656129275036705  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 22:29:48.912 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656129275036704  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 22:29:48.912 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656129275036705  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 22:29:48.912 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656129275036704  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:48.912 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656129275036704  [0;39m 请求参数: [{}]
2025-07-03 22:29:48.912 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656129275036705  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 22:29:48.913 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656129275036705  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 22:29:48.917 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656129275036704  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 22:29:48.917 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656129275036704  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-03 22:29:48.919 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656129275036705  [0;39m 总行数：22
2025-07-03 22:29:48.920 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656129275036705  [0;39m 总页数：5
2025-07-03 22:29:48.922 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656129275036705  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 22:29:48.922 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656129275036705  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 22:30:40.211 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-03 22:30:40.217 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-03 23:10:31.481 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 15088 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:10:31.486 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:10:32.722 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:10:32.732 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:10:32.733 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:10:32.733 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:10:32.839 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:10:32.839 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1302 ms
2025-07-03 23:10:34.252 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:10:34.473 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:10:34.497 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:10:34.505 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.44 seconds (JVM running for 4.612)
2025-07-03 23:10:34.508 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:10:34.508 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:11:11.963 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-07-03 23:15:05.918 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 24680 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:15:05.922 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:15:06.995 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:15:07.003 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:15:07.004 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:15:07.004 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:15:07.087 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:15:07.087 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1121 ms
2025-07-03 23:15:08.550 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:15:08.751 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:15:08.770 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:15:08.778 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.249 seconds (JVM running for 4.25)
2025-07-03 23:15:08.781 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:15:08.781 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:15:44.133 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 23:15:44.133 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 23:15:44.134 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-07-03 23:15:44.205 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656219560477728  [0;39m ------------- 开始 -------------
2025-07-03 23:15:44.206 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656219560477728  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:15:44.207 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656219560477728  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:15:44.207 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656219560576032  [0;39m ------------- 开始 -------------
2025-07-03 23:15:44.207 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656219560477728  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:15:44.207 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656219560576032  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:15:44.208 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656219560576032  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:15:44.208 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656219560576032  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:15:44.290 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656219560576032  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:15:44.290 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656219560477728  [0;39m 请求参数: [{}]
2025-07-03 23:15:44.404 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656219560477728  [0;39m {dataSource-1} inited
2025-07-03 23:15:44.780 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656219560576032  [0;39m 总行数：22
2025-07-03 23:15:44.781 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656219560576032  [0;39m 总页数：1
2025-07-03 23:15:44.782 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656219560477728  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:15:44.782 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656219560477728  [0;39m ------------- 结束 耗时：578 ms -------------
2025-07-03 23:15:44.795 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656219560576032  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:15:44.795 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656219560576032  [0;39m ------------- 结束 耗时：588 ms -------------
2025-07-03 23:15:44.903 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656219583382560  [0;39m ------------- 开始 -------------
2025-07-03 23:15:44.903 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656219583382560  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:15:44.903 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656219583382560  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:15:44.903 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656219583382560  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:15:44.904 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656219583382560  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:15:44.913 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656219583382560  [0;39m 总行数：22
2025-07-03 23:15:44.913 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656219583382560  [0;39m 总页数：1
2025-07-03 23:15:44.914 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656219583382560  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:15:44.915 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656219583382560  [0;39m ------------- 结束 耗时：12 ms -------------
2025-07-03 23:17:19.823 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-03 23:17:19.829 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-03 23:17:27.643 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 24728 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:17:27.646 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:17:28.778 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:17:28.786 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:17:28.787 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:17:28.787 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:17:28.862 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:17:28.863 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1177 ms
2025-07-03 23:17:30.206 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:17:30.412 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:17:30.429 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:17:30.437 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.154 seconds (JVM running for 4.152)
2025-07-03 23:17:30.440 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:17:30.440 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:18:03.309 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-07-03 23:22:38.887 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 3512 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:22:38.892 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:22:40.112 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:22:40.120 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:22:40.121 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:22:40.122 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:22:40.207 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:22:40.207 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1263 ms
2025-07-03 23:22:41.681 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:22:41.900 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:22:41.916 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:22:41.925 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.446 seconds (JVM running for 4.546)
2025-07-03 23:22:41.928 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:22:41.929 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:22:49.290 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 23:22:49.291 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 23:22:49.293 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-03 23:22:49.347 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656233491563552  [0;39m ------------- 开始 -------------
2025-07-03 23:22:49.347 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656233491465248  [0;39m ------------- 开始 -------------
2025-07-03 23:22:49.348 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656233491563552  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:22:49.348 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656233491465248  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:22:49.348 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656233491563552  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:22:49.348 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656233491465248  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:22:49.349 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656233491563552  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:22:49.349 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656233491465248  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:22:49.423 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656233491465248  [0;39m 请求参数: [{}]
2025-07-03 23:22:49.423 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656233491563552  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:22:49.538 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656233491465248  [0;39m {dataSource-1} inited
2025-07-03 23:22:49.822 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656233491563552  [0;39m 总行数：22
2025-07-03 23:22:49.823 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656233491563552  [0;39m 总页数：1
2025-07-03 23:22:49.825 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656233491465248  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:22:49.826 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656233491465248  [0;39m ------------- 结束 耗时：481 ms -------------
2025-07-03 23:22:49.836 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656233491563552  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:22:49.836 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656233491563552  [0;39m ------------- 结束 耗时：489 ms -------------
2025-07-03 23:22:49.935 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656233510831136  [0;39m ------------- 开始 -------------
2025-07-03 23:22:49.935 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656233510831136  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:22:49.936 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656233510831136  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:22:49.937 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656233510831136  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:22:49.938 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656233510831136  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:22:49.949 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656233510831136  [0;39m 总行数：22
2025-07-03 23:22:49.949 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656233510831136  [0;39m 总页数：1
2025-07-03 23:22:49.951 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656233510831136  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:22:49.952 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656233510831136  [0;39m ------------- 结束 耗时：17 ms -------------
2025-07-03 23:25:40.338 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-03 23:25:40.344 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-03 23:26:35.178 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 24528 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:26:35.178 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:26:36.257 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:26:36.263 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:26:36.263 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:26:36.263 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:26:36.365 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:26:36.365 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1137 ms
2025-07-03 23:26:37.770 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:26:37.984 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:26:38.000 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:26:38.009 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.19 seconds (JVM running for 4.237)
2025-07-03 23:26:38.012 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:26:38.012 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:26:57.534 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 23:26:57.535 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 23:26:57.537 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-03 23:26:57.596 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656241626121248  [0;39m ------------- 开始 -------------
2025-07-03 23:26:57.597 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656241626121248  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:26:57.597 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656241626121248  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:26:57.598 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656241626121248  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:26:57.599 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656241626285088  [0;39m ------------- 开始 -------------
2025-07-03 23:26:57.599 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656241626285088  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:26:57.599 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656241626285088  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:26:57.600 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656241626285088  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:26:57.661 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656241626121248  [0;39m 请求参数: [{}]
2025-07-03 23:26:57.661 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656241626285088  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:26:57.766 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656241626121248  [0;39m {dataSource-1} inited
2025-07-03 23:26:58.102 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656241626285088  [0;39m 总行数：22
2025-07-03 23:26:58.102 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656241626285088  [0;39m 总页数：1
2025-07-03 23:26:58.102 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656241626121248  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:26:58.102 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656241626121248  [0;39m ------------- 结束 耗时：508 ms -------------
2025-07-03 23:26:58.118 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656241626285088  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:26:58.118 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656241626285088  [0;39m ------------- 结束 耗时：519 ms -------------
2025-07-03 23:26:58.223 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656241646732320  [0;39m ------------- 开始 -------------
2025-07-03 23:26:58.223 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656241646732320  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:26:58.223 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656241646732320  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:26:58.223 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656241646732320  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:26:58.227 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656241646732320  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:26:58.239 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656241646732320  [0;39m 总行数：22
2025-07-03 23:26:58.240 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656241646732320  [0;39m 总页数：1
2025-07-03 23:26:58.242 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656241646732320  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:26:58.243 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656241646732320  [0;39m ------------- 结束 耗时：20 ms -------------
2025-07-03 23:27:07.833 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656241961632800  [0;39m ------------- 开始 -------------
2025-07-03 23:27:07.834 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656241961665568  [0;39m ------------- 开始 -------------
2025-07-03 23:27:07.834 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656241961632800  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:27:07.834 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656241961665568  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:27:07.834 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656241961632800  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:27:07.834 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656241961665568  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:27:07.835 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656241961632800  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:07.835 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656241961665568  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:07.835 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656241961632800  [0;39m 请求参数: [{}]
2025-07-03 23:27:07.835 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656241961665568  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:27:07.845 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656241961665568  [0;39m 总行数：22
2025-07-03 23:27:07.846 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656241961632800  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:27:07.846 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656241961665568  [0;39m 总页数：5
2025-07-03 23:27:07.846 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656241961632800  [0;39m ------------- 结束 耗时：13 ms -------------
2025-07-03 23:27:07.847 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656241961665568  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:27:07.848 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656241961665568  [0;39m ------------- 结束 耗时：14 ms -------------
2025-07-03 23:27:12.139 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656242102731808  [0;39m ------------- 开始 -------------
2025-07-03 23:27:12.140 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656242102731808  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:27:12.140 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656242102731808  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:27:12.140 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656242102731808  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:12.140 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656242102731808  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:27:12.147 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656242102731808  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:27:12.147 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656242102731808  [0;39m ------------- 结束 耗时：8 ms -------------
2025-07-03 23:27:20.799 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656242386502688  [0;39m ------------- 开始 -------------
2025-07-03 23:27:20.800 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656242386502688  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:27:20.812 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656242386502688  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:27:20.812 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656242386502688  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:20.813 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656242386502688  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:27:20.817 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656242386502688  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:27:20.818 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656242386502688  [0;39m ------------- 结束 耗时：19 ms -------------
2025-07-03 23:27:21.558 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656242411373600  [0;39m ------------- 开始 -------------
2025-07-03 23:27:21.558 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656242411373601  [0;39m ------------- 开始 -------------
2025-07-03 23:27:21.559 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656242411373601  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:27:21.559 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656242411373600  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:27:21.559 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656242411373601  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:27:21.559 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656242411373600  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:27:21.559 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656242411373601  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:21.560 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656242411373601  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:27:21.560 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656242411373600  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:21.560 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656242411373600  [0;39m 请求参数: [{}]
2025-07-03 23:27:21.565 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656242411373600  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:27:21.565 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656242411373600  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 23:27:21.567 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656242411373601  [0;39m 总行数：22
2025-07-03 23:27:21.568 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656242411373601  [0;39m 总页数：5
2025-07-03 23:27:21.568 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656242411373601  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:27:21.569 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656242411373601  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 23:27:27.716 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656242613158944  [0;39m ------------- 开始 -------------
2025-07-03 23:27:27.717 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656242613191712  [0;39m ------------- 开始 -------------
2025-07-03 23:27:27.717 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656242613158944  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:27:27.717 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656242613191712  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:27:27.718 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656242613158944  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:27:27.718 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656242613191712  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:27:27.718 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656242613158944  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:27.718 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656242613191712  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:27.719 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656242613191712  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:27:27.719 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656242613158944  [0;39m 请求参数: [{}]
2025-07-03 23:27:27.724 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656242613158944  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:27:27.725 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656242613158944  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-03 23:27:27.731 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656242613191712  [0;39m 总行数：22
2025-07-03 23:27:27.732 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656242613191712  [0;39m 总页数：1
2025-07-03 23:27:27.734 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656242613191712  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:27:27.735 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656242613191712  [0;39m ------------- 结束 耗时：18 ms -------------
2025-07-03 23:27:27.776 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656242615125024  [0;39m ------------- 开始 -------------
2025-07-03 23:27:27.776 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656242615125024  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:27:27.776 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656242615125024  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:27:27.776 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656242615125024  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:27.776 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656242615125024  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:27:27.787 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656242615125024  [0;39m 总行数：22
2025-07-03 23:27:27.787 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656242615125024  [0;39m 总页数：1
2025-07-03 23:27:27.789 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656242615125024  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:27:27.789 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656242615125024  [0;39m ------------- 结束 耗时：13 ms -------------
2025-07-03 23:27:51.748 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656243400639520  [0;39m ------------- 开始 -------------
2025-07-03 23:27:51.749 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656243400639520  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:27:51.750 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656243400639520  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:27:51.750 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656243400639520  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:27:51.750 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656243400639520  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:27:51.754 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656243400639520  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:27:51.755 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656243400639520  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 23:28:10.407 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656244012057632  [0;39m ------------- 开始 -------------
2025-07-03 23:28:10.407 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656244012057632  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:28:10.408 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656244012057632  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:28:10.408 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656244012057632  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:28:10.409 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656244012057632  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:28:10.412 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656244012057632  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:28:10.412 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656244012057632  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-03 23:28:11.020 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656244032144416  [0;39m ------------- 开始 -------------
2025-07-03 23:28:11.021 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656244032144416  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:28:11.021 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656244032144416  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:28:11.021 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656244032144416  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:28:11.021 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656244032144416  [0;39m 请求参数: [{}]
2025-07-03 23:28:11.021 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656244032177184  [0;39m ------------- 开始 -------------
2025-07-03 23:28:11.022 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656244032177184  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:28:11.022 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656244032177184  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:28:11.022 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656244032177184  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:28:11.022 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656244032177184  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:28:11.024 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656244032144416  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:28:11.025 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656244032144416  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-03 23:28:11.027 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656244032177184  [0;39m 总行数：22
2025-07-03 23:28:11.027 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656244032177184  [0;39m 总页数：5
2025-07-03 23:28:11.027 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656244032177184  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:28:11.027 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656244032177184  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-03 23:28:11.587 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656244050691104  [0;39m ------------- 开始 -------------
2025-07-03 23:28:11.587 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656244050691104  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:28:11.587 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656244050723872  [0;39m ------------- 开始 -------------
2025-07-03 23:28:11.587 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656244050691104  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:28:11.588 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656244050691104  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:28:11.587 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656244050723872  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:28:11.588 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656244050691104  [0;39m 请求参数: [{}]
2025-07-03 23:28:11.588 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656244050723872  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:28:11.588 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656244050723872  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:28:11.588 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656244050723872  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:28:11.592 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656244050691104  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:28:11.592 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656244050691104  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-03 23:28:11.597 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656244050723872  [0;39m 总行数：22
2025-07-03 23:28:11.598 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656244050723872  [0;39m 总页数：1
2025-07-03 23:28:11.599 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656244050723872  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:28:11.600 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656244050723872  [0;39m ------------- 结束 耗时：13 ms -------------
2025-07-03 23:28:11.642 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656244052526112  [0;39m ------------- 开始 -------------
2025-07-03 23:28:11.642 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656244052526112  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:28:11.642 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656244052526112  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:28:11.642 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656244052526112  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:28:11.642 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656244052526112  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:28:11.650 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656244052526112  [0;39m 总行数：22
2025-07-03 23:28:11.650 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656244052526112  [0;39m 总页数：1
2025-07-03 23:28:11.652 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656244052526112  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:28:11.653 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656244052526112  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 23:28:25.119 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656244494140448  [0;39m ------------- 开始 -------------
2025-07-03 23:28:25.119 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656244494140449  [0;39m ------------- 开始 -------------
2025-07-03 23:28:25.120 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656244494140448  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:28:25.120 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656244494140449  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:28:25.121 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656244494140448  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:28:25.121 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656244494140449  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:28:25.121 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656244494140448  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:28:25.121 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656244494140449  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:28:25.122 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656244494140449  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:28:25.122 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656244494140448  [0;39m 请求参数: [{}]
2025-07-03 23:28:25.126 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656244494140448  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:28:25.126 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656244494140448  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 23:28:25.128 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656244494140449  [0;39m 总行数：22
2025-07-03 23:28:25.128 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656244494140449  [0;39m 总页数：5
2025-07-03 23:28:25.129 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656244494140449  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:28:25.129 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656244494140449  [0;39m ------------- 结束 耗时：10 ms -------------
2025-07-03 23:28:29.499 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656244637664288  [0;39m ------------- 开始 -------------
2025-07-03 23:28:29.500 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656244637664288  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:28:29.500 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656244637664288  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:28:29.501 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656244637664288  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:28:29.501 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656244637664288  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:28:29.504 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656244637664288  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:28:29.504 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656244637664288  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-03 23:29:01.262 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-03 23:29:01.262 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-03 23:30:08.951 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 23696 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:30:08.955 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:30:10.442 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:30:10.453 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:30:10.454 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:30:10.454 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:30:10.568 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:30:10.568 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1561 ms
2025-07-03 23:30:12.220 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:30:12.426 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:30:12.443 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:30:12.452 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.026 seconds (JVM running for 5.253)
2025-07-03 23:30:12.455 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:30:12.456 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:30:22.299 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 23:30:22.300 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 23:30:22.301 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-03 23:30:22.355 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656248335664160  [0;39m ------------- 开始 -------------
2025-07-03 23:30:22.356 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656248335664160  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:30:22.356 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656248335762464  [0;39m ------------- 开始 -------------
2025-07-03 23:30:22.356 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656248335664160  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:30:22.357 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656248335762464  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:30:22.357 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656248335664160  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:22.357 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656248335762464  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:30:22.358 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656248335762464  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:22.422 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656248335664160  [0;39m 请求参数: [{}]
2025-07-03 23:30:22.422 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656248335762464  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:30:22.537 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656248335664160  [0;39m {dataSource-1} inited
2025-07-03 23:30:22.850 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656248335762464  [0;39m 总行数：22
2025-07-03 23:30:22.850 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656248335762464  [0;39m 总页数：1
2025-07-03 23:30:22.850 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656248335664160  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:30:22.850 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656248335664160  [0;39m ------------- 结束 耗时：497 ms -------------
2025-07-03 23:30:22.865 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656248335762464  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:30:22.865 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656248335762464  [0;39m ------------- 结束 耗时：509 ms -------------
2025-07-03 23:30:22.965 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656248355718176  [0;39m ------------- 开始 -------------
2025-07-03 23:30:22.966 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656248355718176  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:30:22.966 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656248355718176  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:30:22.966 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656248355718176  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:22.967 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656248355718176  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:30:22.979 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656248355718176  [0;39m 总行数：22
2025-07-03 23:30:22.980 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656248355718176  [0;39m 总页数：1
2025-07-03 23:30:22.981 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656248355718176  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:30:22.983 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656248355718176  [0;39m ------------- 结束 耗时：18 ms -------------
2025-07-03 23:30:33.437 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656248698864672  [0;39m ------------- 开始 -------------
2025-07-03 23:30:33.437 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656248698864673  [0;39m ------------- 开始 -------------
2025-07-03 23:30:33.438 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656248698864673  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:30:33.438 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656248698864672  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:30:33.438 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656248698864673  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:30:33.438 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656248698864672  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:30:33.439 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656248698864673  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:33.439 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656248698864672  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:33.439 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656248698864672  [0;39m 请求参数: [{}]
2025-07-03 23:30:33.439 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656248698864673  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:30:33.450 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656248698864673  [0;39m 总行数：22
2025-07-03 23:30:33.451 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656248698864672  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:30:33.451 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656248698864672  [0;39m ------------- 结束 耗时：14 ms -------------
2025-07-03 23:30:33.451 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656248698864673  [0;39m 总页数：5
2025-07-03 23:30:33.453 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656248698864673  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:30:33.453 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656248698864673  [0;39m ------------- 结束 耗时：16 ms -------------
2025-07-03 23:30:35.121 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656248754045985  [0;39m ------------- 开始 -------------
2025-07-03 23:30:35.121 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656248754045984  [0;39m ------------- 开始 -------------
2025-07-03 23:30:35.122 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656248754045985  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:30:35.122 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656248754045984  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:30:35.122 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656248754045985  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:30:35.122 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656248754045984  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:30:35.123 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656248754045984  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:35.123 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656248754045985  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:35.124 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656248754045984  [0;39m 请求参数: [{}]
2025-07-03 23:30:35.124 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656248754045985  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:30:35.129 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656248754045984  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:30:35.129 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656248754045984  [0;39m ------------- 结束 耗时：8 ms -------------
2025-07-03 23:30:35.133 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656248754045985  [0;39m 总行数：22
2025-07-03 23:30:35.134 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656248754045985  [0;39m 总页数：1
2025-07-03 23:30:35.135 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656248754045985  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:30:35.135 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656248754045985  [0;39m ------------- 结束 耗时：14 ms -------------
2025-07-03 23:30:35.173 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656248755749920  [0;39m ------------- 开始 -------------
2025-07-03 23:30:35.175 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656248755749920  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:30:35.175 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656248755749920  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:30:35.175 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656248755749920  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:35.176 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656248755749920  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:30:35.185 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656248755749920  [0;39m 总行数：22
2025-07-03 23:30:35.186 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656248755749920  [0;39m 总页数：1
2025-07-03 23:30:35.187 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656248755749920  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:30:35.188 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656248755749920  [0;39m ------------- 结束 耗时：15 ms -------------
2025-07-03 23:30:59.183 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656249542476833  [0;39m ------------- 开始 -------------
2025-07-03 23:30:59.182 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656249542476832  [0;39m ------------- 开始 -------------
2025-07-03 23:30:59.183 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656249542476833  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:30:59.184 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656249542476833  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:30:59.183 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656249542476832  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:30:59.184 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656249542476833  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:59.184 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656249542476832  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:30:59.184 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656249542476832  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:30:59.184 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656249542476833  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:30:59.185 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656249542476832  [0;39m 请求参数: [{}]
2025-07-03 23:30:59.188 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656249542476832  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:30:59.189 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656249542476832  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 23:30:59.193 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656249542476833  [0;39m 总行数：22
2025-07-03 23:30:59.194 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656249542476833  [0;39m 总页数：5
2025-07-03 23:30:59.194 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656249542476833  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:30:59.194 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656249542476833  [0;39m ------------- 结束 耗时：12 ms -------------
2025-07-03 23:31:04.339 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656249711461408  [0;39m ------------- 开始 -------------
2025-07-03 23:31:04.340 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656249711461408  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:31:04.340 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656249711461408  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:31:04.341 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656249711461408  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:31:04.341 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656249711461408  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:31:04.349 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656249711461408  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:31:04.349 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656249711461408  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 23:34:46.551 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-03 23:34:46.551 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-03 23:37:55.385 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 14916 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:37:55.389 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:37:56.898 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:37:56.909 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:37:56.910 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:37:56.910 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:37:57.012 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:37:57.012 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1567 ms
2025-07-03 23:37:58.667 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:37:58.919 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:37:58.937 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:37:58.946 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.984 seconds (JVM running for 5.076)
2025-07-03 23:37:58.949 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:37:58.951 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:38:28.635 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 23:38:28.635 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 23:38:28.637 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-03 23:38:28.716 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656264272741408  [0;39m ------------- 开始 -------------
2025-07-03 23:38:28.717 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656264272741408  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:38:28.718 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656264272741408  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:38:28.718 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656264272872480  [0;39m ------------- 开始 -------------
2025-07-03 23:38:28.719 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656264272872480  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:38:28.719 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656264272741408  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:38:28.719 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656264272872480  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:38:28.719 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656264272872480  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:38:28.794 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656264272872480  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:38:28.794 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656264272741408  [0;39m 请求参数: [{}]
2025-07-03 23:38:28.909 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656264272741408  [0;39m {dataSource-1} inited
2025-07-03 23:38:29.200 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656264272872480  [0;39m 总行数：22
2025-07-03 23:38:29.201 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656264272872480  [0;39m 总页数：1
2025-07-03 23:38:29.204 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656264272741408  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:38:29.204 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656264272741408  [0;39m ------------- 结束 耗时：490 ms -------------
2025-07-03 23:38:29.226 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656264272872480  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:38:29.226 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656264272872480  [0;39m ------------- 结束 耗时：508 ms -------------
2025-07-03 23:38:29.345 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656264293418016  [0;39m ------------- 开始 -------------
2025-07-03 23:38:29.345 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656264293418016  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:38:29.346 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656264293418016  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:38:29.346 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656264293418016  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:38:29.346 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656264293418016  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:38:29.354 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656264293418016  [0;39m 总行数：22
2025-07-03 23:38:29.354 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656264293418016  [0;39m 总页数：1
2025-07-03 23:38:29.356 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656264293418016  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:38:29.356 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656264293418016  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 23:39:31.614 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-03 23:39:31.622 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-03 23:42:18.606 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 23456 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:42:18.609 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:42:19.803 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:42:19.811 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:42:19.812 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:42:19.813 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:42:19.896 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:42:19.896 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1246 ms
2025-07-03 23:42:21.294 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:42:21.494 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:42:21.510 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:42:21.519 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.267 seconds (JVM running for 4.278)
2025-07-03 23:42:21.521 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:42:21.522 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:42:58.144 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 23:42:58.145 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 23:42:58.146 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-03 23:42:58.205 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656273103356960  [0;39m ------------- 开始 -------------
2025-07-03 23:42:58.206 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656273103455264  [0;39m ------------- 开始 -------------
2025-07-03 23:42:58.206 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656273103356960  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:42:58.206 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656273103455264  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:42:58.207 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656273103356960  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:42:58.207 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656273103455264  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:42:58.207 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656273103356960  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:42:58.207 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656273103455264  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:42:58.282 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656273103356960  [0;39m 请求参数: [{}]
2025-07-03 23:42:58.282 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656273103455264  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:42:58.375 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656273103356960  [0;39m {dataSource-1} inited
2025-07-03 23:42:58.655 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656273103455264  [0;39m 总行数：22
2025-07-03 23:42:58.655 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656273103455264  [0;39m 总页数：1
2025-07-03 23:42:58.658 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656273103356960  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:42:58.658 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656273103356960  [0;39m ------------- 结束 耗时：455 ms -------------
2025-07-03 23:42:58.670 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656273103455264  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:42:58.671 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656273103455264  [0;39m ------------- 结束 耗时：465 ms -------------
2025-07-03 23:42:58.777 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656273122165792  [0;39m ------------- 开始 -------------
2025-07-03 23:42:58.778 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656273122165792  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:42:58.778 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656273122165792  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:42:58.778 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656273122165792  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:42:58.778 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656273122165792  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:42:58.789 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656273122165792  [0;39m 总行数：22
2025-07-03 23:42:58.789 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656273122165792  [0;39m 总页数：1
2025-07-03 23:42:58.791 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656273122165792  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:42:58.792 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656273122165792  [0;39m ------------- 结束 耗时：15 ms -------------
2025-07-03 23:44:00.761 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656275153257504  [0;39m ------------- 开始 -------------
2025-07-03 23:44:00.762 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656275153257504  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:44:00.762 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656275153257504  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:44:00.762 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656275153257504  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:44:00.763 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656275153257504  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:44:00.778 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4656275153257504  [0;39m discard long time none received connection. , jdbcUrl : *********************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 61983
2025-07-03 23:44:00.781 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4656275153257504  [0;39m discard long time none received connection. , jdbcUrl : *********************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 62147
2025-07-03 23:44:00.808 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656275153257504  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:44:00.808 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656275153257504  [0;39m ------------- 结束 耗时：47 ms -------------
2025-07-03 23:44:06.502 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656275341378592  [0;39m ------------- 开始 -------------
2025-07-03 23:44:06.503 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656275341378592  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:44:06.503 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656275341411360  [0;39m ------------- 开始 -------------
2025-07-03 23:44:06.504 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656275341378592  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:44:06.504 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656275341411360  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:44:06.504 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656275341378592  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:44:06.504 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656275341411360  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:44:06.505 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656275341411360  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:44:06.505 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656275341378592  [0;39m 请求参数: [{}]
2025-07-03 23:44:06.505 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656275341411360  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:44:06.511 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656275341378592  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:44:06.512 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656275341378592  [0;39m ------------- 结束 耗时：10 ms -------------
2025-07-03 23:44:06.519 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656275341411360  [0;39m 总行数：22
2025-07-03 23:44:06.519 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656275341411360  [0;39m 总页数：5
2025-07-03 23:44:06.521 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656275341411360  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:44:06.521 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656275341411360  [0;39m ------------- 结束 耗时：18 ms -------------
2025-07-03 23:44:09.589 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656275442533408  [0;39m ------------- 开始 -------------
2025-07-03 23:44:09.590 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656275442566176  [0;39m ------------- 开始 -------------
2025-07-03 23:44:09.590 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656275442533408  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:44:09.591 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656275442533408  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:44:09.591 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656275442566176  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:44:09.591 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656275442566176  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:44:09.591 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656275442533408  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:44:09.592 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656275442566176  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:44:09.592 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656275442533408  [0;39m 请求参数: [{}]
2025-07-03 23:44:09.592 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656275442566176  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:44:09.597 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656275442533408  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:44:09.597 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656275442533408  [0;39m ------------- 结束 耗时：8 ms -------------
2025-07-03 23:44:09.601 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656275442566176  [0;39m 总行数：22
2025-07-03 23:44:09.602 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656275442566176  [0;39m 总页数：1
2025-07-03 23:44:09.611 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656275442566176  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:44:09.611 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656275442566176  [0;39m ------------- 结束 耗时：21 ms -------------
2025-07-03 23:44:09.647 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656275444433952  [0;39m ------------- 开始 -------------
2025-07-03 23:44:09.647 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656275444433952  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:44:09.647 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656275444433952  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:44:09.647 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656275444433952  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:44:09.647 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656275444433952  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:44:09.659 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656275444433952  [0;39m 总行数：22
2025-07-03 23:44:09.659 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656275444433952  [0;39m 总页数：1
2025-07-03 23:44:09.661 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656275444433952  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:44:09.661 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656275444433952  [0;39m ------------- 结束 耗时：14 ms -------------
2025-07-03 23:44:14.687 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656275609584672  [0;39m ------------- 开始 -------------
2025-07-03 23:44:14.687 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656275609584673  [0;39m ------------- 开始 -------------
2025-07-03 23:44:14.687 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656275609584672  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:44:14.687 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656275609584673  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:44:14.689 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656275609584672  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:44:14.689 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656275609584673  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:44:14.690 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656275609584672  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:44:14.690 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656275609584673  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:44:14.690 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656275609584673  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:44:14.690 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656275609584672  [0;39m 请求参数: [{}]
2025-07-03 23:44:14.695 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656275609584672  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:44:14.696 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656275609584672  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-03 23:44:14.701 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656275609584673  [0;39m 总行数：22
2025-07-03 23:44:14.702 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656275609584673  [0;39m 总页数：5
2025-07-03 23:44:14.703 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656275609584673  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:44:14.703 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656275609584673  [0;39m ------------- 结束 耗时：16 ms -------------
2025-07-03 23:44:17.275 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656275694388256  [0;39m ------------- 开始 -------------
2025-07-03 23:44:17.276 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656275694388256  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:44:17.276 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656275694388256  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:44:17.277 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656275694388256  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:44:17.278 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656275694388256  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:44:17.282 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656275694388256  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:44:17.282 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656275694388256  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 23:49:59.741 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656286916314144  [0;39m ------------- 开始 -------------
2025-07-03 23:49:59.741 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656286916314144  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:49:59.742 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656286916314144  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:49:59.742 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656286916314144  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:49:59.743 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656286916314144  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:49:59.745 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4656286916314144  [0;39m discard long time none received connection. , jdbcUrl : *********************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 342465
2025-07-03 23:49:59.747 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4656286916314144  [0;39m discard long time none received connection. , jdbcUrl : *********************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 345054
2025-07-03 23:49:59.773 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656286916314144  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:49:59.774 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656286916314144  [0;39m ------------- 结束 耗时：33 ms -------------
2025-07-03 23:51:11.341 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656289262502944  [0;39m ------------- 开始 -------------
2025-07-03 23:51:11.342 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656289262502944  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:51:11.342 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656289262502944  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:51:11.342 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656289262502944  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:51:11.343 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656289262502944  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:51:11.345 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4656289262502944  [0;39m discard long time none received connection. , jdbcUrl : *********************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 71572
2025-07-03 23:51:11.372 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656289262502944  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:51:11.373 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656289262502944  [0;39m ------------- 结束 耗时：32 ms -------------
2025-07-03 23:53:27.503 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-03 23:53:27.507 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-03 23:53:34.650 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 1256 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:53:34.653 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:53:36.182 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:53:36.197 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:53:36.198 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:53:36.199 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:53:36.346 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:53:36.347 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1643 ms
2025-07-03 23:53:38.084 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:53:38.355 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:53:38.375 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:53:38.385 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.188 seconds (JVM running for 5.25)
2025-07-03 23:53:38.390 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:53:38.390 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:53:52.042 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 23:53:52.043 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 23:53:52.044 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-03 23:53:52.113 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656294530614304  [0;39m ------------- 开始 -------------
2025-07-03 23:53:52.114 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656294530614304  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:53:52.115 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656294530745376  [0;39m ------------- 开始 -------------
2025-07-03 23:53:52.115 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656294530614304  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:53:52.116 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656294530745376  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:53:52.116 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656294530614304  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:53:52.116 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656294530745376  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:53:52.117 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656294530745376  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:53:52.186 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656294530614304  [0;39m 请求参数: [{}]
2025-07-03 23:53:52.186 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656294530745376  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:53:52.304 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656294530614304  [0;39m {dataSource-1} inited
2025-07-03 23:53:52.627 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656294530745376  [0;39m 总行数：22
2025-07-03 23:53:52.627 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656294530745376  [0;39m 总页数：1
2025-07-03 23:53:52.641 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656294530614304  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:53:52.641 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656294530614304  [0;39m ------------- 结束 耗时：530 ms -------------
2025-07-03 23:53:52.659 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656294530745376  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:53:52.660 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656294530745376  [0;39m ------------- 结束 耗时：545 ms -------------
2025-07-03 23:53:52.763 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656294551979040  [0;39m ------------- 开始 -------------
2025-07-03 23:53:52.763 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656294551979040  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:53:52.764 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656294551979040  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:53:52.764 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656294551979040  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:53:52.764 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656294551979040  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:53:52.776 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656294551979040  [0;39m 总行数：22
2025-07-03 23:53:52.777 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656294551979040  [0;39m 总页数：1
2025-07-03 23:53:52.779 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656294551979040  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:53:52.780 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656294551979040  [0;39m ------------- 结束 耗时：16 ms -------------
2025-07-03 23:54:00.704 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656294812189728  [0;39m ------------- 开始 -------------
2025-07-03 23:54:00.704 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656294812189729  [0;39m ------------- 开始 -------------
2025-07-03 23:54:00.705 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656294812189728  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:54:00.705 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656294812189729  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:54:00.705 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656294812189728  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:54:00.705 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656294812189729  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:54:00.705 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656294812189728  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:54:00.705 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656294812189729  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:54:00.705 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656294812189728  [0;39m 请求参数: [{}]
2025-07-03 23:54:00.705 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656294812189729  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:54:00.714 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656294812189729  [0;39m 总行数：22
2025-07-03 23:54:00.714 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656294812189729  [0;39m 总页数：5
2025-07-03 23:54:00.714 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656294812189729  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:54:00.714 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656294812189729  [0;39m ------------- 结束 耗时：10 ms -------------
2025-07-03 23:54:00.714 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656294812189728  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:54:00.714 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656294812189728  [0;39m ------------- 结束 耗时：10 ms -------------
2025-07-03 23:54:44.363 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-03 23:54:44.369 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-03 23:54:48.966 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 28056 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:54:48.968 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:54:50.083 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:54:50.091 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:54:50.092 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:54:50.092 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:54:50.187 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:54:50.187 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1175 ms
2025-07-03 23:54:51.599 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:54:51.807 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:54:51.822 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:54:51.831 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.249 seconds (JVM running for 4.253)
2025-07-03 23:54:51.833 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:54:51.834 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:55:11.720 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 23:55:11.720 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 23:55:11.722 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-03 23:55:11.789 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656297141470240  [0;39m ------------- 开始 -------------
2025-07-03 23:55:11.791 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656297141470240  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:55:11.791 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656297141470240  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:55:11.791 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656297141568544  [0;39m ------------- 开始 -------------
2025-07-03 23:55:11.792 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656297141568544  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:55:11.792 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656297141470240  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:55:11.792 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656297141568544  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:55:11.793 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656297141568544  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:55:11.854 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656297141568544  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:55:11.854 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656297141470240  [0;39m 请求参数: [{}]
2025-07-03 23:55:11.958 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656297141470240  [0;39m {dataSource-1} inited
2025-07-03 23:55:12.269 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656297141568544  [0;39m 总行数：22
2025-07-03 23:55:12.269 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656297141568544  [0;39m 总页数：1
2025-07-03 23:55:12.269 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656297141470240  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:55:12.273 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656297141470240  [0;39m ------------- 结束 耗时：485 ms -------------
2025-07-03 23:55:12.287 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656297141568544  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:55:12.287 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656297141568544  [0;39m ------------- 结束 耗时：496 ms -------------
2025-07-03 23:55:12.397 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656297161425952  [0;39m ------------- 开始 -------------
2025-07-03 23:55:12.397 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656297161425952  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:55:12.397 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656297161425952  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:55:12.397 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656297161425952  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:55:12.402 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656297161425952  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:55:12.414 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656297161425952  [0;39m 总行数：22
2025-07-03 23:55:12.415 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656297161425952  [0;39m 总页数：1
2025-07-03 23:55:12.417 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656297161425952  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:55:12.417 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656297161425952  [0;39m ------------- 结束 耗时：20 ms -------------
2025-07-03 23:55:16.179 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656297285354529  [0;39m ------------- 开始 -------------
2025-07-03 23:55:16.179 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656297285354528  [0;39m ------------- 开始 -------------
2025-07-03 23:55:16.180 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656297285354528  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:55:16.180 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656297285354529  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:55:16.180 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656297285354529  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:55:16.180 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656297285354528  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:55:16.181 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656297285354529  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:55:16.181 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656297285354528  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:55:16.182 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656297285354529  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:55:16.182 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656297285354528  [0;39m 请求参数: [{}]
2025-07-03 23:55:16.190 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656297285354529  [0;39m 总行数：22
2025-07-03 23:55:16.190 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656297285354529  [0;39m 总页数：5
2025-07-03 23:55:16.190 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656297285354529  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:55:16.190 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656297285354528  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:55:16.190 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656297285354529  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 23:55:16.190 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656297285354528  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 23:55:33.455 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-03 23:55:33.455 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-03 23:55:55.720 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 17892 (D:\idea\wiki\wiki\target\classes started by fls in D:\idea\wiki)
2025-07-03 23:55:55.724 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-03 23:55:57.222 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-03 23:55:57.233 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-03 23:55:57.234 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-03 23:55:57.234 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-03 23:55:57.329 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-03 23:55:57.329 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1544 ms
2025-07-03 23:55:58.993 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-03 23:55:59.264 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-03 23:55:59.282 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-03 23:55:59.291 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.082 seconds (JVM running for 5.322)
2025-07-03 23:55:59.295 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-03 23:55:59.296 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-03 23:56:12.452 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-03 23:56:12.453 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-03 23:56:12.454 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-03 23:56:12.573 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299133207584  [0;39m ------------- 开始 -------------
2025-07-03 23:56:12.575 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299133207584  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:56:12.575 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299133207584  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:56:12.576 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299133207584  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:12.576 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299133371424  [0;39m ------------- 开始 -------------
2025-07-03 23:56:12.577 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299133371424  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:56:12.577 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299133371424  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:56:12.578 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299133371424  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:12.658 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299133371424  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:56:12.658 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299133207584  [0;39m 请求参数: [{}]
2025-07-03 23:56:12.754 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4656299133207584  [0;39m {dataSource-1} inited
2025-07-03 23:56:13.093 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656299133371424  [0;39m 总行数：22
2025-07-03 23:56:13.093 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656299133371424  [0;39m 总页数：1
2025-07-03 23:56:13.093 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299133207584  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:56:13.093 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299133207584  [0;39m ------------- 结束 耗时：523 ms -------------
2025-07-03 23:56:13.115 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299133371424  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:56:13.115 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299133371424  [0;39m ------------- 结束 耗时：539 ms -------------
2025-07-03 23:56:13.217 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299154375712  [0;39m ------------- 开始 -------------
2025-07-03 23:56:13.218 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299154375712  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:56:13.218 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299154375712  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:56:13.218 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299154375712  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:13.218 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299154375712  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:56:13.231 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656299154375712  [0;39m 总行数：22
2025-07-03 23:56:13.231 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656299154375712  [0;39m 总页数：1
2025-07-03 23:56:13.235 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299154375712  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:56:13.235 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299154375712  [0;39m ------------- 结束 耗时：18 ms -------------
2025-07-03 23:56:15.500 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299229185056  [0;39m ------------- 开始 -------------
2025-07-03 23:56:15.500 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299229185057  [0;39m ------------- 开始 -------------
2025-07-03 23:56:15.501 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299229185056  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:56:15.501 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299229185057  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:56:15.501 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299229185056  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:56:15.502 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299229185056  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:15.501 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299229185057  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:56:15.502 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299229185056  [0;39m 请求参数: [{}]
2025-07-03 23:56:15.502 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299229185057  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:15.502 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299229185057  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:56:15.509 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299229185056  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:56:15.509 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656299229185057  [0;39m 总行数：22
2025-07-03 23:56:15.509 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299229185056  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-03 23:56:15.509 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656299229185057  [0;39m 总页数：5
2025-07-03 23:56:15.509 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299229185057  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:56:15.509 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299229185057  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-03 23:56:18.002 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299311170592  [0;39m ------------- 开始 -------------
2025-07-03 23:56:18.003 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299311170592  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-07-03 23:56:18.003 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299311170592  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-07-03 23:56:18.003 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299311170592  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:18.004 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299311170592  [0;39m 请求参数: [{"name":""}]
2025-07-03 23:56:18.011 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299311170592  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1}],"success":true}
2025-07-03 23:56:18.011 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299311170592  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-03 23:56:33.861 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299830838304  [0;39m ------------- 开始 -------------
2025-07-03 23:56:33.862 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299830838304  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:56:33.861 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299830838305  [0;39m ------------- 开始 -------------
2025-07-03 23:56:33.862 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299830838304  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:56:33.862 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299830838305  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:56:33.863 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299830838304  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:33.863 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299830838305  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:56:33.864 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299830838304  [0;39m 请求参数: [{}]
2025-07-03 23:56:33.864 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299830838305  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:33.864 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299830838305  [0;39m 请求参数: [{"page":1,"size":5}]
2025-07-03 23:56:33.867 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299830838304  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:56:33.868 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299830838304  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 23:56:33.870 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656299830838305  [0;39m 总行数：22
2025-07-03 23:56:33.871 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656299830838305  [0;39m 总页数：5
2025-07-03 23:56:33.872 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299830838305  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"}],"total":22},"success":true}
2025-07-03 23:56:33.872 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299830838305  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-03 23:56:36.013 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299901355040  [0;39m ------------- 开始 -------------
2025-07-03 23:56:36.013 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299901355041  [0;39m ------------- 开始 -------------
2025-07-03 23:56:36.013 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299901355040  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-03 23:56:36.014 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299901355041  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:56:36.014 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299901355040  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-03 23:56:36.014 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299901355040  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:36.014 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299901355041  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:56:36.015 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299901355041  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:36.015 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299901355040  [0;39m 请求参数: [{}]
2025-07-03 23:56:36.015 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299901355041  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:56:36.019 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299901355040  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-03 23:56:36.020 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299901355040  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-03 23:56:36.024 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656299901355041  [0;39m 总行数：22
2025-07-03 23:56:36.025 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656299901355041  [0;39m 总页数：1
2025-07-03 23:56:36.027 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299901355041  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:56:36.027 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299901355041  [0;39m ------------- 结束 耗时：14 ms -------------
2025-07-03 23:56:36.073 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4656299903321120  [0;39m ------------- 开始 -------------
2025-07-03 23:56:36.073 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4656299903321120  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-03 23:56:36.073 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4656299903321120  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-03 23:56:36.073 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4656299903321120  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-03 23:56:36.073 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4656299903321120  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-03 23:56:36.085 INFO  com.gec.wiki.service.EbookService                 :72   [32m4656299903321120  [0;39m 总行数：22
2025-07-03 23:56:36.085 INFO  com.gec.wiki.service.EbookService                 :73   [32m4656299903321120  [0;39m 总页数：1
2025-07-03 23:56:36.087 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4656299903321120  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/cover3.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/cover5.png","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/cover4.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","id":6,"name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎皮鲨鱼","docCount":0,"id":595151136462344192,"name":"虎鲸","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"虎虎","docCount":0,"id":595151271078531072,"name":"鲸鱼","viewCount":0,"voteCount":0},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":11,"cover":"/image/cover1.png","description":"最大的鱼","id":595151271078531074,"name":"鲸鱼"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531075,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover1.png","description":"7778","id":595151271078531076,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover2.png","description":"7778","id":595151271078531077,"name":"鲸"},{"category1Id":211,"category2Id":3,"cover":"/image/cover4.png","description":"7778","id":595151271078531078,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531079,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531080,"name":"鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531081,"name":"大鲸"},{"category1Id":211,"category2Id":2233,"cover":"/image/cover2.png","description":"7778","id":595151271078531082,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888","id":595529588424183808,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"77788889999","id":595529628232323072,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看","id":595529675342745600,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"777888578看看1","id":595531949385322496,"name":"大鲸1"},{"category1Id":211,"category2Id":22,"cover":"/image/cover2.png","description":"回家","id":595531949385322500,"name":"大鲸1"}],"total":22},"success":true}
2025-07-03 23:56:36.087 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4656299903321120  [0;39m ------------- 结束 耗时：14 ms -------------
