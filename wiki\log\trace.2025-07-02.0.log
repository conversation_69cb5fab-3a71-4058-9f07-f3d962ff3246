2025-07-02 08:47:46.676 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651681928840224  [0;39m ------------- 开始 -------------
2025-07-02 08:47:46.679 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651681928840224  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:46.680 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651681928840224  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:46.681 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651681928840224  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:46.682 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651681928840224  [0;39m 请求参数: [{"category2Id":101,"page":1,"size":1000}]
2025-07-02 08:47:46.698 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651681929561120  [0;39m ------------- 开始 -------------
2025-07-02 08:47:46.699 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651681929561120  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:46.699 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651681929561120  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:46.699 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651681929561120  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:46.699 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651681929561120  [0;39m 请求参数: [{"category2Id":102,"page":1,"size":1000}]
2025-07-02 08:47:46.706 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651681928840224  [0;39m 总行数：1
2025-07-02 08:47:46.707 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651681928840224  [0;39m 总页数：1
2025-07-02 08:47:46.707 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651681928840224  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":1},"success":true}
2025-07-02 08:47:46.708 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651681928840224  [0;39m ------------- 结束 耗时：32 ms -------------
2025-07-02 08:47:46.710 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651681929561120  [0;39m 总行数：1
2025-07-02 08:47:46.710 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651681929561120  [0;39m 总页数：1
2025-07-02 08:47:46.710 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651681929561120  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2}],"total":1},"success":true}
2025-07-02 08:47:46.711 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651681929561120  [0;39m ------------- 结束 耗时：13 ms -------------
2025-07-02 08:47:47.643 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651681960526880  [0;39m ------------- 开始 -------------
2025-07-02 08:47:47.644 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651681960526880  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:47.644 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651681960526880  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:47.644 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651681960526880  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:47.644 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651681960526880  [0;39m 请求参数: [{"category2Id":212,"page":1,"size":1000}]
2025-07-02 08:47:47.648 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651681960526880  [0;39m 总行数：1
2025-07-02 08:47:47.648 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651681960526880  [0;39m 总页数：1
2025-07-02 08:47:47.648 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651681960526880  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3}],"total":1},"success":true}
2025-07-02 08:47:47.648 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651681960526880  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 08:47:48.091 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651681975206944  [0;39m ------------- 开始 -------------
2025-07-02 08:47:48.092 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651681975206944  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:48.092 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651681975206944  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:48.092 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651681975206944  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:48.092 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651681975206944  [0;39m 请求参数: [{"category2Id":213,"page":1,"size":1000}]
2025-07-02 08:47:48.096 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651681975206944  [0;39m 总行数：4
2025-07-02 08:47:48.096 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651681975206944  [0;39m 总页数：1
2025-07-02 08:47:48.096 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651681975206944  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":4},"success":true}
2025-07-02 08:47:48.097 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651681975206944  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-02 08:47:48.559 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651681990542368  [0;39m ------------- 开始 -------------
2025-07-02 08:47:48.559 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651681990542368  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:48.559 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651681990542368  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:48.559 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651681990542368  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:48.559 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651681990542368  [0;39m 请求参数: [{"category2Id":212,"page":1,"size":1000}]
2025-07-02 08:47:48.563 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651681990542368  [0;39m 总行数：1
2025-07-02 08:47:48.563 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651681990542368  [0;39m 总页数：1
2025-07-02 08:47:48.564 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651681990542368  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3}],"total":1},"success":true}
2025-07-02 08:47:48.564 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651681990542368  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 08:47:48.850 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682000077856  [0;39m ------------- 开始 -------------
2025-07-02 08:47:48.850 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682000077856  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:48.851 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682000077856  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:48.851 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682000077856  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:48.851 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682000077856  [0;39m 请求参数: [{"category2Id":213,"page":1,"size":1000}]
2025-07-02 08:47:48.856 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682000077856  [0;39m 总行数：4
2025-07-02 08:47:48.856 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682000077856  [0;39m 总页数：1
2025-07-02 08:47:48.857 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682000077856  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":4},"success":true}
2025-07-02 08:47:48.857 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682000077856  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-02 08:47:49.170 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682010563616  [0;39m ------------- 开始 -------------
2025-07-02 08:47:49.171 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682010563616  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:49.171 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682010563616  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:49.171 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682010563616  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:49.171 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682010563616  [0;39m 请求参数: [{"category2Id":212,"page":1,"size":1000}]
2025-07-02 08:47:49.174 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682010563616  [0;39m 总行数：1
2025-07-02 08:47:49.175 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682010563616  [0;39m 总页数：1
2025-07-02 08:47:49.175 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682010563616  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3}],"total":1},"success":true}
2025-07-02 08:47:49.175 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682010563616  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 08:47:50.404 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682050999328  [0;39m ------------- 开始 -------------
2025-07-02 08:47:50.404 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682050999328  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:50.404 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682050999328  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:50.404 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682050999328  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:50.404 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682050999328  [0;39m 请求参数: [{"category2Id":201,"page":1,"size":1000}]
2025-07-02 08:47:50.407 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682050999328  [0;39m 总行数：0
2025-07-02 08:47:50.407 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682050999328  [0;39m 总页数：0
2025-07-02 08:47:50.407 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682050999328  [0;39m 返回结果: {"content":{"list":[],"total":0},"success":true}
2025-07-02 08:47:50.407 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682050999328  [0;39m ------------- 结束 耗时：3 ms -------------
2025-07-02 08:47:50.731 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682061714464  [0;39m ------------- 开始 -------------
2025-07-02 08:47:50.732 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682061714464  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:50.732 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682061714464  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:50.732 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682061714464  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:50.732 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682061714464  [0;39m 请求参数: [{"category2Id":202,"page":1,"size":1000}]
2025-07-02 08:47:50.735 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682061714464  [0;39m 总行数：0
2025-07-02 08:47:50.735 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682061714464  [0;39m 总页数：0
2025-07-02 08:47:50.735 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682061714464  [0;39m 返回结果: {"content":{"list":[],"total":0},"success":true}
2025-07-02 08:47:50.735 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682061714464  [0;39m ------------- 结束 耗时：4 ms -------------
2025-07-02 08:47:51.061 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682072527904  [0;39m ------------- 开始 -------------
2025-07-02 08:47:51.061 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682072527904  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:51.061 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682072527904  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:51.061 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682072527904  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:51.061 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682072527904  [0;39m 请求参数: [{"category2Id":4649394845811744,"page":1,"size":1000}]
2025-07-02 08:47:51.064 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682072527904  [0;39m 总行数：0
2025-07-02 08:47:51.064 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682072527904  [0;39m 总页数：0
2025-07-02 08:47:51.064 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682072527904  [0;39m 返回结果: {"content":{"list":[],"total":0},"success":true}
2025-07-02 08:47:51.064 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682072527904  [0;39m ------------- 结束 耗时：3 ms -------------
2025-07-02 08:47:52.213 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682110243872  [0;39m ------------- 开始 -------------
2025-07-02 08:47:52.213 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682110243872  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:52.213 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682110243872  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:52.213 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682110243872  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:52.213 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682110243872  [0;39m 请求参数: [{"category2Id":212,"page":1,"size":1000}]
2025-07-02 08:47:52.217 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682110243872  [0;39m 总行数：1
2025-07-02 08:47:52.217 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682110243872  [0;39m 总页数：1
2025-07-02 08:47:52.217 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682110243872  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3}],"total":1},"success":true}
2025-07-02 08:47:52.217 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682110243872  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 08:47:52.523 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682120434720  [0;39m ------------- 开始 -------------
2025-07-02 08:47:52.523 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682120434720  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:52.523 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682120434720  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:52.523 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682120434720  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:52.523 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682120434720  [0;39m 请求参数: [{"category2Id":213,"page":1,"size":1000}]
2025-07-02 08:47:52.527 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682120434720  [0;39m 总行数：4
2025-07-02 08:47:52.527 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682120434720  [0;39m 总页数：1
2025-07-02 08:47:52.527 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682120434720  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":4},"success":true}
2025-07-02 08:47:52.527 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682120434720  [0;39m ------------- 结束 耗时：4 ms -------------
2025-07-02 08:47:54.567 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682187412512  [0;39m ------------- 开始 -------------
2025-07-02 08:47:54.567 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682187412512  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 08:47:54.567 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682187412512  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 08:47:54.567 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682187412512  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:54.568 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682187412512  [0;39m 请求参数: [{}]
2025-07-02 08:47:54.573 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682187609120  [0;39m ------------- 开始 -------------
2025-07-02 08:47:54.573 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682187609120  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:54.573 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682187609120  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:54.573 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682187412512  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 08:47:54.573 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682187609120  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:54.573 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682187412512  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-02 08:47:54.574 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682187609120  [0;39m 请求参数: [{"page":1,"size":2}]
2025-07-02 08:47:54.577 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682187609120  [0;39m 总行数：7
2025-07-02 08:47:54.577 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682187609120  [0;39m 总页数：4
2025-07-02 08:47:54.577 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682187609120  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-07-02 08:47:54.577 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682187609120  [0;39m ------------- 结束 耗时：4 ms -------------
2025-07-02 08:47:55.327 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682212316192  [0;39m ------------- 开始 -------------
2025-07-02 08:47:55.328 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682212316192  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 08:47:55.328 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682212316192  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 08:47:55.328 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682212316192  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:55.328 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682212316192  [0;39m 请求参数: [{}]
2025-07-02 08:47:55.332 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682212316192  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 08:47:55.332 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682212316192  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 08:47:55.636 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682222441504  [0;39m ------------- 开始 -------------
2025-07-02 08:47:55.636 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682222441505  [0;39m ------------- 开始 -------------
2025-07-02 08:47:55.636 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682222441504  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 08:47:55.637 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682222441504  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 08:47:55.637 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682222441504  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:55.637 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682222441505  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:55.638 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682222441505  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:55.638 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682222441505  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:55.638 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682222441505  [0;39m 请求参数: [{"page":1,"size":2}]
2025-07-02 08:47:55.637 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682222441504  [0;39m 请求参数: [{}]
2025-07-02 08:47:55.641 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682222441504  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 08:47:55.642 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682222441504  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-02 08:47:55.642 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682222441505  [0;39m 总行数：7
2025-07-02 08:47:55.642 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682222441505  [0;39m 总页数：4
2025-07-02 08:47:55.643 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682222441505  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-07-02 08:47:55.643 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682222441505  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-02 08:47:56.212 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682241315872  [0;39m ------------- 开始 -------------
2025-07-02 08:47:56.213 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682241315872  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 08:47:56.213 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682241315872  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 08:47:56.213 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682241315872  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:56.213 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682241315872  [0;39m 请求参数: [{}]
2025-07-02 08:47:56.213 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682241315872  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 08:47:56.213 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682241315872  [0;39m ------------- 结束 耗时：1 ms -------------
2025-07-02 08:47:57.108 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682270676000  [0;39m ------------- 开始 -------------
2025-07-02 08:47:57.108 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651682270676001  [0;39m ------------- 开始 -------------
2025-07-02 08:47:57.109 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682270676000  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 08:47:57.109 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651682270676001  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 08:47:57.109 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682270676000  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 08:47:57.109 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682270676000  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:57.109 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651682270676001  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 08:47:57.109 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651682270676001  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 08:47:57.109 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682270676000  [0;39m 请求参数: [{}]
2025-07-02 08:47:57.109 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651682270676001  [0;39m 请求参数: [{"page":1,"size":2}]
2025-07-02 08:47:57.112 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682270676000  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 08:47:57.112 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682270676000  [0;39m ------------- 结束 耗时：4 ms -------------
2025-07-02 08:47:57.112 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651682270676001  [0;39m 总行数：7
2025-07-02 08:47:57.112 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651682270676001  [0;39m 总页数：4
2025-07-02 08:47:57.112 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651682270676001  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-07-02 08:47:57.112 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651682270676001  [0;39m ------------- 结束 耗时：4 ms -------------
2025-07-02 09:34:22.696 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651773548823584  [0;39m ------------- 开始 -------------
2025-07-02 09:34:22.696 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651773548823585  [0;39m ------------- 开始 -------------
2025-07-02 09:34:22.696 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651773548823585  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:34:22.696 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651773548823584  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 09:34:22.697 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651773548823585  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:34:22.697 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651773548823584  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 09:34:22.697 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651773548823584  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:22.697 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651773548823585  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:22.697 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651773548823584  [0;39m 请求参数: [{}]
2025-07-02 09:34:22.697 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651773548823585  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-02 09:34:22.710 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651773548823584  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 09:34:22.711 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651773548823584  [0;39m ------------- 结束 耗时：15 ms -------------
2025-07-02 09:34:22.713 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651773548823585  [0;39m 总行数：7
2025-07-02 09:34:22.713 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651773548823585  [0;39m 总页数：1
2025-07-02 09:34:22.714 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651773548823585  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-07-02 09:34:22.714 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651773548823585  [0;39m ------------- 结束 耗时：18 ms -------------
2025-07-02 09:34:22.793 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651773552002080  [0;39m ------------- 开始 -------------
2025-07-02 09:34:22.794 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651773552002080  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:34:22.794 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651773552002080  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:34:22.794 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651773552002080  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:22.794 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651773552002080  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-02 09:34:22.798 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651773552002080  [0;39m 总行数：7
2025-07-02 09:34:22.798 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651773552002080  [0;39m 总页数：1
2025-07-02 09:34:22.799 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651773552002080  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-07-02 09:34:22.799 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651773552002080  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-02 09:34:26.183 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651773663085600  [0;39m ------------- 开始 -------------
2025-07-02 09:34:26.183 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651773663085600  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:34:26.183 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651773663085600  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:34:26.183 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651773663085600  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:26.183 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651773663085600  [0;39m 请求参数: [{"category2Id":213,"page":1,"size":1000}]
2025-07-02 09:34:26.187 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651773663085600  [0;39m 总行数：4
2025-07-02 09:34:26.187 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651773663085600  [0;39m 总页数：1
2025-07-02 09:34:26.187 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651773663085600  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":4},"success":true}
2025-07-02 09:34:26.187 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651773663085600  [0;39m ------------- 结束 耗时：4 ms -------------
2025-07-02 09:34:26.817 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651773683860512  [0;39m ------------- 开始 -------------
2025-07-02 09:34:26.817 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651773683860512  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:34:26.817 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651773683860512  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:34:26.818 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651773683860512  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:26.818 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651773683860512  [0;39m 请求参数: [{"category2Id":212,"page":1,"size":1000}]
2025-07-02 09:34:26.821 INFO  com.gec.wiki.service.EbookService                 :69   [32m4651773683860512  [0;39m 总行数：1
2025-07-02 09:34:26.821 INFO  com.gec.wiki.service.EbookService                 :70   [32m4651773683860512  [0;39m 总页数：1
2025-07-02 09:34:26.821 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651773683860512  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3}],"total":1},"success":true}
2025-07-02 09:34:26.821 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651773683860512  [0;39m ------------- 结束 耗时：4 ms -------------
2025-07-02 09:34:34.002 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-02 09:34:34.002 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-07-02 09:34:40.591 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 9464 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-07-02 09:34:40.593 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-07-02 09:34:41.591 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-07-02 09:34:41.598 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-07-02 09:34:41.598 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-07-02 09:34:41.598 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-02 09:34:41.663 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-07-02 09:34:41.664 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1036 ms
2025-07-02 09:34:42.209 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-07-02 09:34:42.358 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-07-02 09:34:42.371 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-07-02 09:34:42.378 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 2.076 seconds (JVM running for 2.927)
2025-07-02 09:34:42.379 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-07-02 09:34:42.380 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-07-02 09:34:45.928 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-02 09:34:45.928 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-07-02 09:34:45.929 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-07-02 09:34:45.958 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774311040032  [0;39m ------------- 开始 -------------
2025-07-02 09:34:45.958 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774311072800  [0;39m ------------- 开始 -------------
2025-07-02 09:34:45.958 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774311072800  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:34:45.958 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774311040032  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 09:34:45.958 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774311072800  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:34:45.958 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774311040032  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 09:34:45.959 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774311072800  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:45.959 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774311040032  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:45.997 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774311040032  [0;39m 请求参数: [{}]
2025-07-02 09:34:45.997 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774311072800  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-02 09:34:46.067 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4651774311040032  [0;39m {dataSource-1} inited
2025-07-02 09:34:46.205 INFO  com.gec.wiki.service.EbookService                 :72   [32m4651774311072800  [0;39m 总行数：7
2025-07-02 09:34:46.207 INFO  com.gec.wiki.service.EbookService                 :73   [32m4651774311072800  [0;39m 总页数：1
2025-07-02 09:34:46.214 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774311040032  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 09:34:46.215 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774311040032  [0;39m ------------- 结束 耗时：258 ms -------------
2025-07-02 09:34:46.218 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774311072800  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-07-02 09:34:46.219 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774311072800  [0;39m ------------- 结束 耗时：261 ms -------------
2025-07-02 09:34:46.276 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774321493024  [0;39m ------------- 开始 -------------
2025-07-02 09:34:46.277 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774321493024  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:34:46.277 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774321493024  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:34:46.277 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774321493024  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:46.277 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774321493024  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-02 09:34:46.283 INFO  com.gec.wiki.service.EbookService                 :72   [32m4651774321493024  [0;39m 总行数：7
2025-07-02 09:34:46.283 INFO  com.gec.wiki.service.EbookService                 :73   [32m4651774321493024  [0;39m 总页数：1
2025-07-02 09:34:46.284 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774321493024  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-07-02 09:34:46.284 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774321493024  [0;39m ------------- 结束 耗时：8 ms -------------
2025-07-02 09:34:47.772 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774370513952  [0;39m ------------- 开始 -------------
2025-07-02 09:34:47.772 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774370513952  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:34:47.772 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774370513952  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:34:47.773 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774370513952  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:47.773 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774370513952  [0;39m 请求参数: [{"category2Id":212,"page":1,"size":1000}]
2025-07-02 09:34:47.787 INFO  com.gec.wiki.service.EbookService                 :72   [32m4651774370513952  [0;39m 总行数：1
2025-07-02 09:34:47.787 INFO  com.gec.wiki.service.EbookService                 :73   [32m4651774370513952  [0;39m 总页数：1
2025-07-02 09:34:47.787 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774370513952  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3}],"total":1},"success":true}
2025-07-02 09:34:47.787 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774370513952  [0;39m ------------- 结束 耗时：15 ms -------------
2025-07-02 09:34:48.287 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774387389472  [0;39m ------------- 开始 -------------
2025-07-02 09:34:48.288 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774387389472  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:34:48.288 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774387389472  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:34:48.288 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774387389472  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:48.288 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774387389472  [0;39m 请求参数: [{"category2Id":213,"page":1,"size":1000}]
2025-07-02 09:34:48.295 INFO  com.gec.wiki.service.EbookService                 :72   [32m4651774387389472  [0;39m 总行数：4
2025-07-02 09:34:48.295 INFO  com.gec.wiki.service.EbookService                 :73   [32m4651774387389472  [0;39m 总页数：1
2025-07-02 09:34:48.297 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774387389472  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":4},"success":true}
2025-07-02 09:34:48.298 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774387389472  [0;39m ------------- 结束 耗时：11 ms -------------
2025-07-02 09:34:56.535 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774657659937  [0;39m ------------- 开始 -------------
2025-07-02 09:34:56.535 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774657659936  [0;39m ------------- 开始 -------------
2025-07-02 09:34:56.536 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774657659937  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:34:56.536 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774657659936  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 09:34:56.536 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774657659937  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:34:56.536 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774657659936  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 09:34:56.536 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774657659937  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:56.536 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774657659936  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:34:56.536 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774657659936  [0;39m 请求参数: [{}]
2025-07-02 09:34:56.536 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774657659937  [0;39m 请求参数: [{"page":1,"size":2}]
2025-07-02 09:34:56.544 INFO  com.gec.wiki.service.EbookService                 :72   [32m4651774657659937  [0;39m 总行数：7
2025-07-02 09:34:56.544 INFO  com.gec.wiki.service.EbookService                 :73   [32m4651774657659937  [0;39m 总页数：4
2025-07-02 09:34:56.544 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774657659936  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 09:34:56.544 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774657659936  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-02 09:34:56.544 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774657659937  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-07-02 09:34:56.544 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774657659937  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-02 09:35:00.064 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774773298209  [0;39m ------------- 开始 -------------
2025-07-02 09:35:00.064 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774773298208  [0;39m ------------- 开始 -------------
2025-07-02 09:35:00.064 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774773298208  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 09:35:00.064 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774773298209  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:35:00.065 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774773298208  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 09:35:00.065 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774773298209  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:35:00.065 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774773298209  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:35:00.065 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774773298208  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:35:00.065 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774773298209  [0;39m 请求参数: [{"page":1,"size":2}]
2025-07-02 09:35:00.065 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774773298208  [0;39m 请求参数: [{}]
2025-07-02 09:35:00.069 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774773298208  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 09:35:00.069 INFO  com.gec.wiki.service.EbookService                 :72   [32m4651774773298209  [0;39m 总行数：7
2025-07-02 09:35:00.069 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774773298208  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 09:35:00.069 INFO  com.gec.wiki.service.EbookService                 :73   [32m4651774773298209  [0;39m 总页数：4
2025-07-02 09:35:00.070 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774773298209  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-07-02 09:35:00.070 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774773298209  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-02 09:35:01.611 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774823990304  [0;39m ------------- 开始 -------------
2025-07-02 09:35:01.611 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774823990304  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 09:35:01.612 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774823990304  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 09:35:01.612 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774823990304  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:35:01.612 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774823990304  [0;39m 请求参数: [{}]
2025-07-02 09:35:01.617 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774823990304  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 09:35:01.617 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774823990304  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-02 09:35:02.103 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774840112161  [0;39m ------------- 开始 -------------
2025-07-02 09:35:02.103 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774840112160  [0;39m ------------- 开始 -------------
2025-07-02 09:35:02.103 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774840112160  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 09:35:02.104 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774840112160  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 09:35:02.103 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774840112161  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:35:02.104 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774840112160  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:35:02.104 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774840112161  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:35:02.104 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774840112161  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:35:02.104 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774840112160  [0;39m 请求参数: [{}]
2025-07-02 09:35:02.104 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774840112161  [0;39m 请求参数: [{"page":1,"size":2}]
2025-07-02 09:35:02.109 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774840112160  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 09:35:02.110 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774840112160  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-02 09:35:02.110 INFO  com.gec.wiki.service.EbookService                 :72   [32m4651774840112161  [0;39m 总行数：7
2025-07-02 09:35:02.110 INFO  com.gec.wiki.service.EbookService                 :73   [32m4651774840112161  [0;39m 总页数：4
2025-07-02 09:35:02.110 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774840112161  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-07-02 09:35:02.111 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774840112161  [0;39m ------------- 结束 耗时：8 ms -------------
2025-07-02 09:35:03.010 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774869832736  [0;39m ------------- 开始 -------------
2025-07-02 09:35:03.010 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774869832736  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 09:35:03.010 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774869832736  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 09:35:03.010 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774869832736  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:35:03.010 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774869832736  [0;39m 请求参数: [{}]
2025-07-02 09:35:03.014 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774869832736  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 09:35:03.015 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774869832736  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 09:35:03.400 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774882612257  [0;39m ------------- 开始 -------------
2025-07-02 09:35:03.401 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774882612257  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:35:03.400 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651774882612256  [0;39m ------------- 开始 -------------
2025-07-02 09:35:03.402 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774882612257  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:35:03.402 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651774882612256  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 09:35:03.403 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774882612257  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:35:03.403 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651774882612256  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 09:35:03.403 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651774882612256  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:35:03.403 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774882612257  [0;39m 请求参数: [{"page":1,"size":2}]
2025-07-02 09:35:03.403 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651774882612256  [0;39m 请求参数: [{}]
2025-07-02 09:35:03.407 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774882612256  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 09:35:03.407 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774882612256  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-02 09:35:03.408 INFO  com.gec.wiki.service.EbookService                 :72   [32m4651774882612257  [0;39m 总行数：7
2025-07-02 09:35:03.408 INFO  com.gec.wiki.service.EbookService                 :73   [32m4651774882612257  [0;39m 总页数：4
2025-07-02 09:35:03.409 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651774882612257  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-07-02 09:35:03.409 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651774882612257  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-02 09:35:12.226 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4651775171822624  [0;39m ------------- 开始 -------------
2025-07-02 09:35:12.227 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4651775171822624  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 09:35:12.227 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4651775171822624  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 09:35:12.227 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4651775171822624  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 09:35:12.227 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4651775171822624  [0;39m 请求参数: [{"name":"水","page":1,"size":2}]
2025-07-02 09:35:12.233 INFO  com.gec.wiki.service.EbookService                 :72   [32m4651775171822624  [0;39m 总行数：1
2025-07-02 09:35:12.233 INFO  com.gec.wiki.service.EbookService                 :73   [32m4651775171822624  [0;39m 总页数：1
2025-07-02 09:35:12.234 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4651775171822624  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3}],"total":1},"success":true}
2025-07-02 09:35:12.234 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4651775171822624  [0;39m ------------- 结束 耗时：8 ms -------------
2025-07-02 11:34:37.911 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652009976988704  [0;39m ------------- 开始 -------------
2025-07-02 11:34:37.913 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652009976988704  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 11:34:37.913 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652009976988704  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 11:34:37.913 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652009976988704  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:37.913 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652009976988704  [0;39m 请求参数: [{}]
2025-07-02 11:34:37.914 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652009977087008  [0;39m ------------- 开始 -------------
2025-07-02 11:34:37.914 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652009977087008  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 11:34:37.914 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652009977087008  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 11:34:37.914 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652009977087008  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:37.914 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652009977087008  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-02 11:34:37.938 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652009976988704  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 11:34:37.938 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652009976988704  [0;39m ------------- 结束 耗时：27 ms -------------
2025-07-02 11:34:37.938 INFO  com.gec.wiki.service.EbookService                 :72   [32m4652009977087008  [0;39m 总行数：7
2025-07-02 11:34:37.942 INFO  com.gec.wiki.service.EbookService                 :73   [32m4652009977087008  [0;39m 总页数：1
2025-07-02 11:34:37.943 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652009977087008  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-07-02 11:34:37.943 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652009977087008  [0;39m ------------- 结束 耗时：29 ms -------------
2025-07-02 11:34:37.971 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652009978954784  [0;39m ------------- 开始 -------------
2025-07-02 11:34:37.971 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652009978954784  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 11:34:37.971 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652009978954784  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 11:34:37.971 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652009978954784  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:37.971 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652009978954784  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-02 11:34:37.976 INFO  com.gec.wiki.service.EbookService                 :72   [32m4652009978954784  [0;39m 总行数：7
2025-07-02 11:34:37.976 INFO  com.gec.wiki.service.EbookService                 :73   [32m4652009978954784  [0;39m 总页数：1
2025-07-02 11:34:37.978 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652009978954784  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-07-02 11:34:37.978 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652009978954784  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-02 11:34:38.331 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652009990751264  [0;39m ------------- 开始 -------------
2025-07-02 11:34:38.330 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652009990718496  [0;39m ------------- 开始 -------------
2025-07-02 11:34:38.331 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652009990751264  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 11:34:38.331 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652009990718496  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 11:34:38.331 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652009990751264  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 11:34:38.331 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652009990718496  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 11:34:38.331 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652009990751264  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:38.331 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652009990718496  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:38.331 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652009990751264  [0;39m 请求参数: [{"page":1,"size":2}]
2025-07-02 11:34:38.331 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652009990718496  [0;39m 请求参数: [{}]
2025-07-02 11:34:38.337 INFO  com.gec.wiki.service.EbookService                 :72   [32m4652009990751264  [0;39m 总行数：7
2025-07-02 11:34:38.337 INFO  com.gec.wiki.service.EbookService                 :73   [32m4652009990751264  [0;39m 总页数：4
2025-07-02 11:34:38.338 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652009990751264  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-07-02 11:34:38.338 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652009990751264  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-02 11:34:38.339 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652009990718496  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 11:34:38.339 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652009990718496  [0;39m ------------- 结束 耗时：9 ms -------------
2025-07-02 11:34:45.571 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652010227991584  [0;39m ------------- 开始 -------------
2025-07-02 11:34:45.571 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652010227991584  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 11:34:45.572 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652010227991584  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 11:34:45.572 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652010227991584  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:45.572 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652010227991584  [0;39m 请求参数: [{}]
2025-07-02 11:34:45.576 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652010227991584  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 11:34:45.576 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652010227991584  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 11:34:47.194 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652010281174048  [0;39m ------------- 开始 -------------
2025-07-02 11:34:47.195 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652010281206816  [0;39m ------------- 开始 -------------
2025-07-02 11:34:47.195 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652010281174048  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 11:34:47.195 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652010281206816  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 11:34:47.195 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652010281174048  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 11:34:47.195 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652010281206816  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 11:34:47.195 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652010281174048  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:47.195 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652010281206816  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:47.195 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652010281174048  [0;39m 请求参数: [{}]
2025-07-02 11:34:47.195 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652010281206816  [0;39m 请求参数: [{"page":1,"size":2}]
2025-07-02 11:34:47.198 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652010281174048  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 11:34:47.199 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652010281174048  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 11:34:47.200 INFO  com.gec.wiki.service.EbookService                 :72   [32m4652010281206816  [0;39m 总行数：7
2025-07-02 11:34:47.200 INFO  com.gec.wiki.service.EbookService                 :73   [32m4652010281206816  [0;39m 总页数：4
2025-07-02 11:34:47.202 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652010281206816  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-07-02 11:34:47.202 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652010281206816  [0;39m ------------- 结束 耗时：7 ms -------------
2025-07-02 11:34:49.137 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652010344842272  [0;39m ------------- 开始 -------------
2025-07-02 11:34:49.138 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652010344875040  [0;39m ------------- 开始 -------------
2025-07-02 11:34:49.138 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652010344842272  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-07-02 11:34:49.138 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652010344875040  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 11:34:49.138 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652010344842272  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-07-02 11:34:49.138 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652010344875040  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 11:34:49.138 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652010344875040  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:49.138 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652010344842272  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:49.138 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652010344842272  [0;39m 请求参数: [{}]
2025-07-02 11:34:49.138 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652010344875040  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-02 11:34:49.142 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652010344842272  [0;39m 返回结果: {"content":[{"id":4649394845811744,"name":"1","parent":200,"sort":1},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":208,"name":"test2","parent":207,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":4649393048683552,"name":"12","parent":123,"sort":12},{"id":4649397965587488,"name":"12","parent":4649393563665440,"sort":12},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":4648977786242080,"name":"12","parent":132,"sort":123},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"success":true}
2025-07-02 11:34:49.143 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652010344842272  [0;39m ------------- 结束 耗时：6 ms -------------
2025-07-02 11:34:49.146 INFO  com.gec.wiki.service.EbookService                 :72   [32m4652010344875040  [0;39m 总行数：7
2025-07-02 11:34:49.146 INFO  com.gec.wiki.service.EbookService                 :73   [32m4652010344875040  [0;39m 总页数：1
2025-07-02 11:34:49.147 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652010344875040  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-07-02 11:34:49.148 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652010344875040  [0;39m ------------- 结束 耗时：10 ms -------------
2025-07-02 11:34:49.171 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4652010345956384  [0;39m ------------- 开始 -------------
2025-07-02 11:34:49.171 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4652010345956384  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-07-02 11:34:49.171 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4652010345956384  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-07-02 11:34:49.171 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4652010345956384  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-07-02 11:34:49.171 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4652010345956384  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-07-02 11:34:49.176 INFO  com.gec.wiki.service.EbookService                 :72   [32m4652010345956384  [0;39m 总行数：7
2025-07-02 11:34:49.176 INFO  com.gec.wiki.service.EbookService                 :73   [32m4652010345956384  [0;39m 总页数：1
2025-07-02 11:34:49.176 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4652010345956384  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-07-02 11:34:49.176 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4652010345956384  [0;39m ------------- 结束 耗时：5 ms -------------
2025-07-02 11:35:18.314 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-07-02 11:35:18.314 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
