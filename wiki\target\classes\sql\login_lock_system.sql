-- 系统设置表 - 用于存储管理员配置的登录错误锁定次数等系统参数
CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `setting_key` VARCHAR(100) NOT NULL UNIQUE COMMENT '设置键',
    `setting_value` VARCHAR(500) NOT NULL COMMENT '设置值',
    `setting_description` VARCHAR(200) COMMENT '设置描述',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 插入默认的登录错误锁定次数设置
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_description`) 
VALUES ('LOGIN_MAX_FAIL_COUNT', '5', '登录最大错误次数，超过后锁定30分钟') 
ON DUPLICATE KEY UPDATE 
`setting_value` = VALUES(`setting_value`), 
`setting_description` = VALUES(`setting_description`);

-- 用户登录锁定记录表
CREATE TABLE IF NOT EXISTS `user_login_lock` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `fail_count` INT DEFAULT 0 COMMENT '失败次数',
    `locked_until` DATETIME COMMENT '锁定到期时间',
    `last_fail_time` DATETIME COMMENT '最后失败时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY `idx_username` (`username`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_ip_address` (`ip_address`),
    KEY `idx_locked_until` (`locked_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录锁定记录表';

-- 添加外键约束（如果users表存在的话）
-- ALTER TABLE `user_login_lock` ADD CONSTRAINT `fk_user_login_lock_user_id` 
-- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
