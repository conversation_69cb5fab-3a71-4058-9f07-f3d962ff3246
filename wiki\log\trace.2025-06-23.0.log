2025-06-23 18:12:48.253 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 24724 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-23 18:12:48.258 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-23 18:12:49.102 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m <PERSON>cat initialized with port(s): 8080 (http)
2025-06-23 18:12:49.110 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-23 18:12:49.110 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-23 18:12:49.110 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-23 18:12:49.176 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-23 18:12:49.176 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 880 ms
2025-06-23 18:12:49.598 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-23 18:12:49.727 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-23 18:12:49.746 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-23 18:12:49.754 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.831 seconds (JVM running for 2.618)
2025-06-23 18:12:49.755 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-23 18:12:49.756 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-23 18:12:53.567 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 18:12:53.568 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-23 18:12:53.568 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-06-23 18:59:49.952 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-06-23 19:00:00.042 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 26716 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-23 19:00:00.043 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-23 19:00:00.705 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-23 19:00:00.713 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-23 19:00:00.713 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-23 19:00:00.713 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-23 19:00:00.774 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-23 19:00:00.774 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 702 ms
2025-06-23 19:00:01.153 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-23 19:00:01.253 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-23 19:00:01.268 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-23 19:00:01.276 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.944 seconds (JVM running for 2.572)
2025-06-23 19:00:01.277 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-23 19:00:01.278 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-23 19:00:06.980 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 19:00:06.981 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-23 19:00:06.981 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-06-23 19:00:28.871 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m                  [0;39m {dataSource-1} inited
2025-06-23 19:11:41.891 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-23 19:11:41.893 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-23 19:11:46.133 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 30496 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-23 19:11:46.135 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-23 19:11:46.839 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-23 19:11:46.845 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-23 19:11:46.846 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-23 19:11:46.846 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-23 19:11:46.906 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-23 19:11:46.906 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 736 ms
2025-06-23 19:11:47.317 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-23 19:11:47.440 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-23 19:11:47.454 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-23 19:11:47.463 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.893 seconds (JVM running for 2.975)
2025-06-23 19:11:47.465 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-23 19:11:47.465 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-23 19:12:08.309 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 19:12:08.310 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-23 19:12:08.310 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-06-23 19:12:42.867 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m                  [0;39m {dataSource-1} inited
2025-06-23 19:12:47.972 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-23 19:12:47.973 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-23 19:14:15.188 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 6868 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-23 19:14:15.192 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-23 19:14:15.905 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-23 19:14:15.911 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-23 19:14:15.912 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-23 19:14:15.912 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-23 19:14:15.973 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-23 19:14:15.974 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 738 ms
2025-06-23 19:14:16.375 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-23 19:14:16.495 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-23 19:14:16.512 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-23 19:14:16.519 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.68 seconds (JVM running for 2.356)
2025-06-23 19:14:16.521 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-23 19:14:16.521 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-23 19:15:30.324 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 19:15:30.324 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-23 19:15:30.325 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-23 19:15:39.246 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m                  [0;39m {dataSource-1} inited
2025-06-23 19:19:00.683 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-23 19:19:00.685 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-23 19:19:16.669 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 22808 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-23 19:19:16.671 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-23 19:19:17.278 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-23 19:19:17.288 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-23 19:19:17.289 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-23 19:19:17.289 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-23 19:19:17.347 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-23 19:19:17.347 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 643 ms
2025-06-23 19:19:17.696 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-23 19:19:17.799 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-23 19:19:17.817 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-23 19:19:17.824 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.426 seconds (JVM running for 1.926)
2025-06-23 19:19:17.825 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-23 19:19:17.825 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-23 19:19:19.668 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 19:19:19.669 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-23 19:19:19.670 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-23 19:19:19.772 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m                  [0;39m {dataSource-1} inited
2025-06-23 19:21:39.135 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-23 19:21:39.138 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
