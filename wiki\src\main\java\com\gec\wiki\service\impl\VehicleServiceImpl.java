package com.gec.wiki.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gec.wiki.pojo.Vehicle;
import com.gec.wiki.service.VehicleService;
import com.gec.wiki.mapper.VehicleMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
* @description 针对表【vehicle(车辆信息)】的数据库操作Service实现
*/
@Component
public class VehicleServiceImpl extends ServiceImpl<VehicleMapper, Vehicle>
    implements VehicleService{

    @Override
    public List<Vehicle> getByCustomerId(Long customerId) {
        QueryWrapper<Vehicle> wrapper = new QueryWrapper<>();
        wrapper.eq("customer_id", customerId);
        return this.list(wrapper);
    }
}
