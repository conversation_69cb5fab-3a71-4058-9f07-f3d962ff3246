<template>
  <div class="main-layout">
    <the-header></the-header>
    <div class="main-content">
      <router-view/>
    </div>
    <the-footer></the-footer>
  </div>
</template>
<script lang="ts">
import { UserOutlined, LaptopOutlined, NotificationOutlined } from '@ant-design/icons-vue';
import { defineComponent, ref } from 'vue';
import TheFooter from "@/components/the-footer.vue";
import TheHeader from "@/components/the-header.vue";

export default defineComponent({
  components: {
    TheHeader,
    TheFooter,
    UserOutlined,
    LaptopOutlined,
    NotificationOutlined,
  },
  setup() {
    return {
      selectedKeys1: ref<string[]>(['2']),
      selectedKeys2: ref<string[]>(['1']),
      openKeys: ref<string[]>(['sub1']),
    };
  },
});
</script>
<style>
html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
}

#app {
  min-height: 100vh;
}

.main-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 全局样式调整 */
.ant-layout-header {
  padding: 0 50px;
}

.ant-layout-footer {
  padding: 24px 50px;
}

/* 按钮动效 */
.ant-btn {
  transition: all 0.3s;
}

.ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 全局卡片样式 */
.ant-card {
  border-radius: 8px;
  transition: all 0.3s;
  overflow: hidden;
}

.ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 美化表单元素 */
.ant-input, .ant-select-selector, .ant-input-number, .ant-cascader-input, .ant-picker {
  border-radius: 4px !important;
}

/* 美化表格 */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
