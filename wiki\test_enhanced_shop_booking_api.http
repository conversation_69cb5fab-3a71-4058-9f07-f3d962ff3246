# 测试增强版维修店预约管理API

### 1. 获取维修店今日预约列表
GET http://localhost:8080/shop/booking/today
Content-Type: application/json

### 2. 获取维修店预约统计
GET http://localhost:8080/shop/booking/stats
Content-Type: application/json

### 3. 确认预约
PUT http://localhost:8080/shop/booking/1/confirm
Content-Type: application/json

### 4. 开始服务
PUT http://localhost:8080/shop/booking/1/start
Content-Type: application/json

### 5. 完成服务
PUT http://localhost:8080/shop/booking/1/complete
Content-Type: application/json

### 6. 婉拒预约（不提供原因）
PUT http://localhost:8080/shop/booking/2/reject
Content-Type: application/json

### 7. 婉拒预约（提供原因）
PUT http://localhost:8080/shop/booking/3/reject?reason=今日技师人手不足，建议改约明日
Content-Type: application/json

### 8. 通用状态更新 - 确认预约（状态1 -> 2）
PUT http://localhost:8080/shop/booking/4/status?status=2
Content-Type: application/json

### 9. 通用状态更新 - 开始服务（状态2 -> 3）
PUT http://localhost:8080/shop/booking/4/status?status=3
Content-Type: application/json

### 10. 通用状态更新 - 完成服务（状态3 -> 4）
PUT http://localhost:8080/shop/booking/4/status?status=4
Content-Type: application/json
