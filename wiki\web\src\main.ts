import { createApp } from 'vue';
import Antd from 'ant-design-vue';
import App from './App.vue';
import 'ant-design-vue/dist/antd.css';
import router from './router';
import store from './store';
import * as Icons from '@ant-design/icons-vue'
import axios from "axios";

// 导入全局样式
import './styles/theme.css';
import './styles/components.css';

axios.defaults.baseURL = process.env.VUE_APP_SERVER || 'http://localhost:8880'

const app = createApp(App);
app.use(store).use(router).use(Antd).mount('#app');

//全局使用图标
const icons:any = Icons;
for (const i in icons ){
    app.component(i,icons[i]);
}

/*
* axios拦截器
* */
axios.interceptors.request.use(function (config){
    console.log('请求参数：',config);

    // 添加token到请求头
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
},error => {
    return Promise.reject(error);
});
axios.interceptors.response.use(function (response){
    console.log('返回结果：',response);
    return response;
},error => {
    console.log('返回错误：',error);
    return Promise.reject(error);
})