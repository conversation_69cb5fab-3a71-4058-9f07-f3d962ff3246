<template>
  <div class="booking-container">
    <div class="booking-content">
      <div class="booking-header">
        <h1>预约服务</h1>
        <p>请填写预约信息，我们将为您安排专业的维修服务</p>
      </div>

      <a-card class="booking-form-card">
        <a-form
          :model="bookingForm"
          :rules="bookingRules"
          @finish="handleSubmit"
          layout="vertical"
          class="booking-form"
        >
          <!-- 服务选择 -->
          <a-form-item name="serviceId" label="选择服务">
            <a-select 
              v-model:value="bookingForm.serviceId" 
              placeholder="请选择服务项目"
              size="large"
              @change="onServiceChange"
            >
              <a-select-option 
                v-for="service in services" 
                :key="service.id" 
                :value="service.id"
              >
                <div class="service-option">
                  <span class="service-name">{{ service.name }}</span>
                  <span class="service-price">¥{{ service.price }}</span>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>

          <!-- 车辆选择 -->
          <a-form-item name="vehicleId" label="选择车辆">
            <a-select 
              v-model:value="bookingForm.vehicleId" 
              placeholder="请选择车辆"
              size="large"
            >
              <a-select-option 
                v-for="vehicle in vehicles" 
                :key="vehicle.id" 
                :value="vehicle.id"
              >
                {{ vehicle.licensePlate }} - {{ vehicle.brand }} {{ vehicle.model }}
              </a-select-option>
            </a-select>
            <div class="add-vehicle-link">
              <a @click="showAddVehicle">+ 添加新车辆</a>
            </div>
          </a-form-item>

          <!-- 预约日期和时间 -->
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item name="bookingDate" label="预约日期">
                <a-date-picker 
                  v-model:value="bookingForm.bookingDate"
                  placeholder="选择日期"
                  size="large"
                  style="width: 100%"
                  :disabled-date="disabledDate"
                  @change="onDateChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="bookingTime" label="预约时间">
                <a-select 
                  v-model:value="bookingForm.bookingTime" 
                  placeholder="选择时间"
                  size="large"
                  :loading="timeLoading"
                >
                  <a-select-option 
                    v-for="time in availableTimes" 
                    :key="time.value" 
                    :value="time.value"
                    :disabled="!time.available"
                  >
                    {{ time.label }}
                    <span v-if="!time.available" class="time-unavailable">(已预约)</span>
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 技师选择 -->
          <a-form-item name="technicianId" label="选择技师">
            <a-radio-group v-model:value="bookingForm.technicianId" size="large">
              <a-radio-button 
                v-for="technician in technicians" 
                :key="technician.id" 
                :value="technician.id"
                class="technician-option"
              >
                <div class="technician-info">
                  <a-avatar :src="technician.avatar" style="margin-right: 8px">
                    {{ technician.name.charAt(0) }}
                  </a-avatar>
                  <div>
                    <div class="technician-name">{{ technician.name }}</div>
                    <div class="technician-level">{{ getLevelText(technician.level) }}</div>
                  </div>
                </div>
              </a-radio-button>
            </a-radio-group>
          </a-form-item>

          <!-- 联系信息 -->
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item name="contactName" label="联系人姓名">
                <a-input 
                  v-model:value="bookingForm.contactName" 
                  placeholder="请输入联系人姓名"
                  size="large"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="contactPhone" label="联系电话">
                <a-input 
                  v-model:value="bookingForm.contactPhone" 
                  placeholder="请输入联系电话"
                  size="large"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 问题描述 -->
          <a-form-item name="problemDescription" label="问题描述">
            <a-textarea 
              v-model:value="bookingForm.problemDescription" 
              placeholder="请详细描述车辆问题，以便技师更好地为您服务"
              :rows="4"
              show-count
              :maxlength="500"
            />
          </a-form-item>

          <!-- 备注 -->
          <a-form-item name="remark" label="备注">
            <a-textarea 
              v-model:value="bookingForm.remark" 
              placeholder="其他需要说明的事项（可选）"
              :rows="3"
              show-count
              :maxlength="200"
            />
          </a-form-item>

          <!-- 服务费用 -->
          <div class="service-summary" v-if="selectedService">
            <a-card size="small" title="服务费用">
              <div class="fee-item">
                <span>{{ selectedService.name }}</span>
                <span class="fee">¥{{ selectedService.price }}</span>
              </div>
              <a-divider style="margin: 12px 0" />
              <div class="fee-total">
                <span>总计</span>
                <span class="total-fee">¥{{ selectedService.price }}</span>
              </div>
            </a-card>
          </div>

          <!-- 提交按钮 -->
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              :loading="loading"
              class="submit-button"
            >
              确认预约
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 添加车辆模态框 -->
    <a-modal
      v-model:visible="addVehicleVisible"
      title="添加车辆"
      @ok="handleAddVehicle"
      @cancel="addVehicleVisible = false"
    >
      <a-form :model="vehicleForm" layout="vertical">
        <a-form-item label="车牌号" required>
          <a-input v-model:value="vehicleForm.licensePlate" placeholder="请输入车牌号" />
        </a-form-item>
        <a-form-item label="品牌">
          <a-input v-model:value="vehicleForm.brand" placeholder="请输入车辆品牌" />
        </a-form-item>
        <a-form-item label="型号">
          <a-input v-model:value="vehicleForm.model" placeholder="请输入车辆型号" />
        </a-form-item>
        <a-form-item label="颜色">
          <a-input v-model:value="vehicleForm.color" placeholder="请输入车辆颜色" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import axios from 'axios';

// 定义类型接口
interface Service {
  id: number;
  name: string;
  price: number;
  description?: string;
  duration?: number;
}

interface Vehicle {
  id: number;
  licensePlate: string;
  brand: string;
  model: string;
}

interface Technician {
  id: number;
  name: string;
  level: number;
  avatar?: string;
}

interface TimeSlot {
  value: string;
  label: string;
  available: boolean;
}

interface BookingForm {
  serviceId: number | null;
  vehicleId: number | null;
  technicianId: number | null;
  contactName: string;
  contactPhone: string;
  bookingDate: any;
  bookingTime: string | null;
  problemDescription: string;
  remark: string;
}

export default defineComponent({
  name: 'Booking',
  setup() {
    const router = useRouter();
    const route = useRoute();
    const loading = ref(false);
    const timeLoading = ref(false);
    const addVehicleVisible = ref(false);

    const bookingForm = ref<BookingForm>({
      serviceId: null,
      vehicleId: null,
      technicianId: null,
      contactName: '',
      contactPhone: '',
      bookingDate: null,
      bookingTime: null,
      problemDescription: '',
      remark: ''
    });

    const vehicleForm = ref({
      licensePlate: '',
      brand: '',
      model: '',
      color: ''
    });

    const services = ref<Service[]>([]);
    const vehicles = ref<Vehicle[]>([]);
    const technicians = ref<Technician[]>([]);
    const availableTimes = ref<TimeSlot[]>([]);

    const selectedService = computed(() => {
      return services.value.find(service => service.id === bookingForm.value.serviceId);
    });

    const bookingRules = {
      serviceId: [
        { required: true, message: '请选择服务项目', trigger: 'change' }
      ],
      vehicleId: [
        { required: true, message: '请选择车辆', trigger: 'change' }
      ],
      contactName: [
        { required: true, message: '请输入联系人姓名', trigger: 'blur' }
      ],
      contactPhone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      bookingDate: [
        { required: true, message: '请选择预约日期', trigger: 'change' }
      ],
      bookingTime: [
        { required: true, message: '请选择预约时间', trigger: 'change' }
      ],
      problemDescription: [
        { required: true, message: '请描述车辆问题', trigger: 'blur' }
      ]
    };

    // 禁用过去的日期
    const disabledDate = (current: any) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return current && current < today;
    };

    const getLevelText = (level: number) => {
      const levels: { [key: number]: string } = { 1: '初级技师', 2: '中级技师', 3: '高级技师', 4: '专家技师' };
      return levels[level] || '技师';
    };

    const onServiceChange = () => {
      // 服务变更时清空技师选择
      bookingForm.value.technicianId = null;
      loadTechnicians();
    };

    const onDateChange = () => {
      // 日期变更时重新加载可用时间
      bookingForm.value.bookingTime = null;
      loadAvailableTimes();
    };

    const showAddVehicle = () => {
      addVehicleVisible.value = true;
    };

    const loadServices = async () => {
      try {
        const response = await axios.get('/service/getServiceListByPage', {
          params: { page: 1, size: 100 }
        });
        if (response.data.success) {
          services.value = response.data.content.list;
          
          // 如果URL参数中有serviceId，自动选择
          const serviceId = route.query.serviceId;
          if (serviceId && typeof serviceId === 'string') {
            const parsedId = parseInt(serviceId);
            if (!isNaN(parsedId)) {
              bookingForm.value.serviceId = parsedId;
              onServiceChange();
            }
          }
        }
      } catch (error) {
        message.error('加载服务列表失败');
      }
    };

    const loadVehicles = async () => {
      try {
        // TODO: 调用获取用户车辆列表API
        // 临时数据
        vehicles.value = [
          { id: 1, licensePlate: '京A12345', brand: '丰田', model: '凯美瑞' },
          { id: 2, licensePlate: '京B67890', brand: '本田', model: '雅阁' }
        ];
      } catch (error) {
        message.error('加载车辆列表失败');
      }
    };

    const loadTechnicians = async () => {
      try {
        // TODO: 根据服务类型加载合适的技师
        // 临时数据
        technicians.value = [
          { id: 1, name: '张师傅', level: 4, avatar: '' },
          { id: 2, name: '李师傅', level: 3, avatar: '' },
          { id: 3, name: '王师傅', level: 2, avatar: '' }
        ];
      } catch (error) {
        message.error('加载技师列表失败');
      }
    };

    const loadAvailableTimes = async () => {
      if (!bookingForm.value.bookingDate || !bookingForm.value.serviceId) {
        return;
      }

      timeLoading.value = true;
      try {
        const formatDate = (date: any): string => {
          if (!date) return '';
          if (date instanceof Date) {
            return date.toISOString().split('T')[0];
          }
          if (typeof date === 'string') {
            return date;
          }
          // Ant Design Vue日期对象处理
          if (date && typeof date === 'object' && date.format) {
            return date.format('YYYY-MM-DD');
          }
          return String(date);
        };
        
        const dateStr = formatDate(bookingForm.value.bookingDate);
          
        const response = await axios.get('/booking/available-times', {
          params: {
            date: dateStr,
            serviceId: bookingForm.value.serviceId
          }
        });

        if (response.data.success) {
          availableTimes.value = response.data.content;
        } else {
          // 临时数据
          availableTimes.value = [
            { value: '09:00', label: '09:00-10:00', available: true },
            { value: '10:00', label: '10:00-11:00', available: true },
            { value: '11:00', label: '11:00-12:00', available: false },
            { value: '14:00', label: '14:00-15:00', available: true },
            { value: '15:00', label: '15:00-16:00', available: true },
            { value: '16:00', label: '16:00-17:00', available: true }
          ];
        }
      } catch (error) {
        message.error('加载可用时间失败');
        availableTimes.value = [];
      } finally {
        timeLoading.value = false;
      }
    };

    const handleAddVehicle = async () => {
      // TODO: 调用添加车辆API
      message.success('车辆添加成功');
      addVehicleVisible.value = false;
      await loadVehicles();
    };

    const handleSubmit = async () => {
      if (!localStorage.getItem('token')) {
        message.warning('请先登录');
        router.push('/login');
        return;
      }

      loading.value = true;
      try {
        const formatDate = (date: any): string => {
          if (!date) return '';
          if (date instanceof Date) {
            return date.toISOString().split('T')[0];
          }
          if (typeof date === 'string') {
            return date;
          }
          // Ant Design Vue日期对象处理
          if (date && typeof date === 'object' && date.format) {
            return date.format('YYYY-MM-DD');
          }
          return String(date);
        };
        
        const dateStr = formatDate(bookingForm.value.bookingDate);
          
        const data = {
          ...bookingForm.value,
          bookingDate: dateStr,
          bookingTime: bookingForm.value.bookingTime
        };

        const response = await axios.post('/booking/create', data);
        
        if (response.data.success) {
          message.success('预约成功！我们将尽快联系您确认服务时间。');
          router.push('/');
        } else {
          message.error(response.data.message || '预约失败');
        }
      } catch (error) {
        message.error('预约失败，请检查网络连接');
      } finally {
        loading.value = false;
      }
    };

    // 从本地存储获取用户信息填充联系人
    const loadUserInfo = () => {
      const userInfoStr = localStorage.getItem('userInfo');
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          bookingForm.value.contactName = userInfo.realName || '';
          bookingForm.value.contactPhone = userInfo.phone || '';
        } catch (error) {
          console.error('Parse user info error:', error);
        }
      }
    };

    onMounted(() => {
      loadServices();
      loadVehicles();
      loadUserInfo();
    });

    return {
      bookingForm,
      vehicleForm,
      bookingRules,
      loading,
      timeLoading,
      addVehicleVisible,
      services,
      vehicles,
      technicians,
      availableTimes,
      selectedService,
      disabledDate,
      getLevelText,
      onServiceChange,
      onDateChange,
      showAddVehicle,
      handleAddVehicle,
      handleSubmit
    };
  }
});
</script>

<style scoped>
.booking-container {
  min-height: 100vh;
  background: #f0f2f5;
  padding: 24px;
}

.booking-content {
  max-width: 800px;
  margin: 0 auto;
}

.booking-header {
  text-align: center;
  margin-bottom: 32px;
}

.booking-header h1 {
  font-size: 32px;
  color: #333;
  margin-bottom: 8px;
}

.booking-header p {
  color: #666;
  font-size: 16px;
}

.booking-form-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.service-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-name {
  font-weight: 500;
}

.service-price {
  color: #ff4d4f;
  font-weight: bold;
}

.add-vehicle-link {
  margin-top: 8px;
}

.add-vehicle-link a {
  color: #1890ff;
  text-decoration: none;
}

.add-vehicle-link a:hover {
  text-decoration: underline;
}

.technician-option {
  height: auto !important;
  padding: 12px 16px !important;
  margin: 8px 8px 8px 0 !important;
  border-radius: 8px !important;
}

.technician-info {
  display: flex;
  align-items: center;
}

.technician-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.technician-level {
  font-size: 12px;
  color: #666;
}

.time-unavailable {
  color: #999;
  font-size: 12px;
}

.service-summary {
  margin: 24px 0;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.fee {
  color: #666;
}

.fee-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.total-fee {
  color: #ff4d4f;
  font-size: 18px;
}

.submit-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  border-radius: 8px;
}
</style>
