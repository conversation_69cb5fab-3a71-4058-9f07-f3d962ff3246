### 测试新的忘记密码功能（使用QQ邮箱授权码）

### 1. 验证用户身份信息
POST http://localhost:8880/auth/verify-user-info
Content-Type: application/json

{
  "realName": "车先生",
  "phone": "19978452934",
  "email": "<EMAIL>"
}

### 2. 使用授权码发送验证码（需要真实的QQ邮箱授权码）
POST http://localhost:8880/auth/send-verification-code-with-auth
Content-Type: application/json

{
  "email": "<EMAIL>",
  "userEmail": "<EMAIL>",
  "authCode": "your_16_digit_auth_code",
  "type": "forgot_password"
}

### 3. 验证验证码
POST http://localhost:8880/auth/verify-code-and-recover
Content-Type: application/json

{
  "email": "<EMAIL>",
  "verificationCode": "123456",
  "realName": "车先生",
  "phone": "19978452934"
}

### 4. 重置密码（用户自定义新密码）
POST http://localhost:8880/auth/reset-password
Content-Type: application/json

{
  "email": "<EMAIL>",
  "realName": "车先生",
  "phone": "19978452934",
  "newPassword": "myNewPassword123"
}

### 注意事项：
# 1. 替换 "<EMAIL>" 为您的真实QQ邮箱
# 2. 替换 "your_16_digit_auth_code" 为您的QQ邮箱SMTP授权码
# 3. 第三步中的验证码需要从您的邮箱中获取真实验证码

### 测试旧的发送验证码接口（对比用）
POST http://localhost:8880/auth/send-verification-code
Content-Type: application/json

{
  "email": "<EMAIL>",
  "type": "forgot_password"
}
