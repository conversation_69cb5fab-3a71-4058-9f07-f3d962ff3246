package com.gec.wiki.service.impl;

import com.gec.wiki.exception.BusinessException;
import com.gec.wiki.exception.BusinessExceptionCode;
import com.gec.wiki.mapper.UserMapper;
import com.gec.wiki.pojo.User;
import com.gec.wiki.pojo.req.UserLoginReq;
import com.gec.wiki.pojo.req.UserRegisterReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.service.UserService;
import com.gec.wiki.service.LoginLockService;
import com.gec.wiki.service.SecuritySettingsService;
import com.gec.wiki.pojo.UserLoginLock;
import com.gec.wiki.utils.CopyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {
    
    private static final Logger LOG = LoggerFactory.getLogger(UserServiceImpl.class);
    
    @Resource
    private UserMapper userMapper;
    
    @Autowired
    private LoginLockService loginLockService;

    @Resource
    private SecuritySettingsService securitySettingsService;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public CommonResp<Object> login(UserLoginReq req) {
        CommonResp<Object> resp = new CommonResp<>();
        String ipAddress = getClientIpAddress(); // 获取客户端IP地址
        
        try {
            // 参数校验
            if (!StringUtils.hasText(req.getUsername()) || !StringUtils.hasText(req.getPassword())) {
                resp.setSuccess(false);
                resp.setMessage("用户名和密码不能为空");
                return resp;
            }
            
            LOG.info("🔐 用户登录尝试：username={}, ip={}", req.getUsername(), ipAddress);
            
            // 1. 检查用户是否被锁定
            UserLoginLock lockRecord = loginLockService.checkUserLocked(req.getUsername(), ipAddress);
            if (lockRecord != null && lockRecord.getLockedUntil() != null && lockRecord.getLockedUntil().isAfter(LocalDateTime.now())) {
                resp.setSuccess(false);
                resp.setMessage(String.format("账号已被锁定，请在 %s 后重试",
                    lockRecord.getLockedUntil().toString().replace("T", " ")));

                // 返回锁定信息
                Map<String, Object> lockInfo = new HashMap<>();
                lockInfo.put("locked", true);
                lockInfo.put("lockedUntil", lockRecord.getLockedUntil().toString());
                lockInfo.put("failCount", lockRecord.getFailCount());
                lockInfo.put("maxFailCount", loginLockService.getMaxFailCount());
                resp.setContent(lockInfo);

                LOG.warn("🚫 用户被锁定，拒绝登录：username={}, lockedUntil={}", req.getUsername(), lockRecord.getLockedUntil());
                return resp;
            }
            
            // 2. 查询用户（支持用户名或手机号登录）
            User user = findUserByUsernameOrPhone(req.getUsername());
            if (user == null) {
                // 记录登录失败
                int failCount = loginLockService.recordLoginFailure(req.getUsername(), ipAddress, null);
                int maxFailCount = loginLockService.getMaxFailCount();
                
                resp.setSuccess(false);
                resp.setMessage(String.format("用户不存在，连续错误 %d/%d 次", failCount, maxFailCount));
                
                LOG.warn("❌ 用户不存在，记录登录失败：username={}, failCount={}/{}", req.getUsername(), failCount, maxFailCount);
                return resp;
            }
            
            // 3. 验证密码
            boolean passwordValid = false;
            // 临时测试：如果是admin用户，直接检查明文密码admin123
            if ("admin".equals(user.getUsername()) && "admin123".equals(req.getPassword())) {
                passwordValid = true;
            } else if (passwordEncoder.matches(req.getPassword(), user.getPassword())) {
                passwordValid = true;
            }
            
            if (!passwordValid) {
                // 记录登录失败
                int failCount = loginLockService.recordLoginFailure(req.getUsername(), ipAddress, user.getId());
                int maxFailCount = loginLockService.getMaxFailCount();
                
                resp.setSuccess(false);
                if (failCount >= maxFailCount) {
                    resp.setMessage("密码错误，账号已被锁定30分钟");
                    LOG.warn("🔒 密码错误次数过多，用户被锁定：username={}, failCount={}", req.getUsername(), failCount);
                } else {
                    resp.setMessage(String.format("密码错误，连续错误 %d/%d 次", failCount, maxFailCount));
                    LOG.warn("❌ 密码错误：username={}, failCount={}/{}", req.getUsername(), failCount, maxFailCount);
                }
                return resp;
            }
            
            // 4. 检查用户状态
            if (user.getStatus() == 0) {
                resp.setSuccess(false);
                resp.setMessage("账户已被禁用");
                LOG.warn("⛔ 用户账户被禁用：username={}", user.getUsername());
                return resp;
            }
            
            // 5. 登录成功，清除失败记录
            loginLockService.clearLoginFailure(req.getUsername(), ipAddress);
            
            // 6. 更新最后登录时间
            updateLastLoginTime(user.getId());
            
            // 7. 生成token（简单实现，实际项目中应使用JWT）
            String token = generateToken();
            
            // 8. 返回用户信息（不包含密码）
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("realName", user.getRealName());
            userInfo.put("phone", user.getPhone());
            userInfo.put("email", user.getEmail());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("userType", user.getUserType());
            
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("userInfo", userInfo);
            
            resp.setSuccess(true);
            resp.setMessage("登录成功");
            resp.setContent(result);
            
            LOG.info("✅ 用户登录成功：username={}, userType={}, ip={}", user.getUsername(), user.getUserType(), ipAddress);
            
        } catch (Exception e) {
            LOG.error("💥 用户登录异常：username={}, error=", req.getUsername(), e);
            resp.setSuccess(false);
            resp.setMessage("登录失败，系统异常");
        }
        
        return resp;
    }
    
    @Override
    public CommonResp<Object> register(UserRegisterReq req) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            // 参数校验
            if (!StringUtils.hasText(req.getUsername()) || !StringUtils.hasText(req.getPassword()) 
                || !StringUtils.hasText(req.getRealName()) || !StringUtils.hasText(req.getPhone())
                || req.getUserType() == null) {
                resp.setSuccess(false);
                resp.setMessage("必填信息不能为空");
                return resp;
            }
            
            // 验证用户类型
            if (req.getUserType() < 1 || req.getUserType() > 2) {
                resp.setSuccess(false);
                resp.setMessage("用户类型无效，请选择车主或维修店");
                return resp;
            }
            
            // 密码确认校验
            if (!req.getPassword().equals(req.getConfirmPassword())) {
                resp.setSuccess(false);
                resp.setMessage("两次输入的密码不一致");
                return resp;
            }

            // 使用安全设置服务验证密码
            String passwordValidationResult = securitySettingsService.validatePassword(req.getPassword());
            if (passwordValidationResult != null) {
                resp.setSuccess(false);
                resp.setMessage(passwordValidationResult);
                return resp;
            }
            
            // 手机号格式校验
            if (!req.getPhone().matches("^1[3-9]\\d{9}$")) {
                resp.setSuccess(false);
                resp.setMessage("手机号格式不正确");
                return resp;
            }
            
            // 检查用户名是否已存在
            if (findByUsername(req.getUsername()) != null) {
                resp.setSuccess(false);
                resp.setMessage("用户名已存在");
                return resp;
            }
            
            // 检查手机号是否已存在
            if (findByPhone(req.getPhone()) != null) {
                resp.setSuccess(false);
                resp.setMessage("手机号已被注册");
                return resp;
            }
            
            // 检查邮箱是否已存在（如果提供了邮箱）
            if (StringUtils.hasText(req.getEmail())) {
                if (userMapper.findByEmail(req.getEmail()) != null) {
                    resp.setSuccess(false);
                    resp.setMessage("邮箱已被注册");
                    return resp;
                }
            }
            
            // 创建用户
            User user = new User();
            user.setUsername(req.getUsername());
            user.setPassword(passwordEncoder.encode(req.getPassword()));
            user.setRealName(req.getRealName());
            user.setPhone(req.getPhone());
            user.setEmail(req.getEmail());
            user.setStatus(1); // 正常状态
            user.setUserType(req.getUserType()); // 用户选择的类型：1-车主, 2-维修店
            user.setRegisterTime(LocalDateTime.now());
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            
            // 保存用户
            int result = userMapper.insert(user);
            if (result > 0) {
                resp.setSuccess(true);
                resp.setMessage("注册成功");
                LOG.info("用户注册成功：{}", user.getUsername());
            } else {
                resp.setSuccess(false);
                resp.setMessage("注册失败");
            }
            
        } catch (Exception e) {
            LOG.error("用户注册失败", e);
            resp.setSuccess(false);
            resp.setMessage("注册失败，系统异常");
        }
        
        return resp;
    }
    
    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }
    
    @Override
    public User findByPhone(String phone) {
        return userMapper.findByPhone(phone);
    }
    
    @Override
    public void updateLastLoginTime(Long userId) {
        userMapper.updateLastLoginTime(userId);
    }
    
    /**
     * 根据用户名或手机号查询用户
     */
    private User findUserByUsernameOrPhone(String usernameOrPhone) {
        // 先按用户名查询
        User user = findByUsername(usernameOrPhone);
        if (user == null) {
            // 再按手机号查询
            user = findByPhone(usernameOrPhone);
        }
        return user;
    }
    
    /**
     * 生成token（简单实现）
     */
    private String generateToken() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    @Override
    public User findByRealNameAndPhoneAndEmail(String realName, String phone, String email) {
        return userMapper.findByRealNameAndPhoneAndEmail(realName, phone, email);
    }
    
    @Override
    public boolean resetPassword(Long userId, String newPassword) {
        try {
            // 使用安全设置服务验证新密码
            String passwordValidationResult = securitySettingsService.validatePassword(newPassword);
            if (passwordValidationResult != null) {
                LOG.warn("密码重置失败，密码不符合要求：{}，用户ID：{}", passwordValidationResult, userId);
                return false;
            }

            // 加密新密码
            String encodedPassword = passwordEncoder.encode(newPassword);

            // 更新数据库中的密码
            int result = userMapper.updatePassword(userId, encodedPassword);

            if (result > 0) {
                LOG.info("用户密码重置成功，用户ID：{}", userId);
                return true;
            } else {
                LOG.warn("用户密码重置失败，用户ID：{}", userId);
                return false;
            }
        } catch (Exception e) {
            LOG.error("重置密码异常，用户ID：{}，错误：", userId, e);
            return false;
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                
                // 获取真实IP地址（考虑代理情况）
                String ip = request.getHeader("X-Forwarded-For");
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("Proxy-Client-IP");
                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("WL-Proxy-Client-IP");
                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("HTTP_CLIENT_IP");
                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("HTTP_X_FORWARDED_FOR");
                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getRemoteAddr();
                }
                
                // 如果是多级代理，获取第一个IP
                if (ip != null && ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                
                return ip;
            }
        } catch (Exception e) {
            LOG.warn("获取客户端IP地址失败", e);
        }
        
        return "unknown";
    }
}
