<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gec.wiki.mapper.VehicleMapper">

    <resultMap id="BaseResultMap" type="com.gec.wiki.pojo.Vehicle">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="licensePlate" column="license_plate" jdbcType="VARCHAR"/>
            <result property="brand" column="brand" jdbcType="VARCHAR"/>
            <result property="model" column="model" jdbcType="VARCHAR"/>
            <result property="color" column="color" jdbcType="VARCHAR"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="engineNumber" column="engine_number" jdbcType="VARCHAR"/>
            <result property="vin" column="vin" jdbcType="VARCHAR"/>
            <result property="customerId" column="customer_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,license_plate,brand,model,color,
        year,engine_number,vin,customer_id,
        create_time,update_time
    </sql>
</mapper>
