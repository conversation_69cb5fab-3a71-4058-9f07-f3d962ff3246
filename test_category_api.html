<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-left: 4px solid #28a745;
            background-color: #d4edda;
        }
        .error {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .category-tree {
            margin-left: 20px;
        }
        .category-item {
            margin: 5px 0;
            padding: 5px;
            background: #e9ecef;
            border-radius: 3px;
        }
        .subcategory {
            margin-left: 20px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 汽车维修服务分类API测试</h1>
        
        <div class="test-section">
            <h3>📡 API连接测试</h3>
            <button onclick="testConnection()">测试后端连接</button>
            <div id="connectionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 分类数据获取测试</h3>
            <button onclick="testCategoryAPI()">获取分类数据</button>
            <div id="categoryResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🌳 分类数据结构展示</h3>
            <button onclick="showCategoryTree()">显示分类树</button>
            <div id="categoryTree" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 服务API测试</h3>
            <button onclick="testServiceAPI()">测试服务列表API</button>
            <div id="serviceResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8880';
        
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.textContent = '正在测试连接...';
            
            try {
                const response = await fetch(`${API_BASE}/category/allList`);
                if (response.ok) {
                    resultDiv.textContent = `✅ 连接成功！状态码: ${response.status}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ 连接失败！状态码: ${response.status}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 连接错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testCategoryAPI() {
            const resultDiv = document.getElementById('categoryResult');
            resultDiv.textContent = '正在获取分类数据...';
            
            try {
                const response = await fetch(`${API_BASE}/category/allList`);
                const data = await response.json();
                
                if (data.success) {
                    const categories = data.content;
                    resultDiv.textContent = `✅ 获取成功！共 ${categories.length} 个分类\n\n` + 
                        JSON.stringify(data, null, 2);
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ API返回失败: ${data.message}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 请求错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function showCategoryTree() {
            const resultDiv = document.getElementById('categoryTree');
            resultDiv.textContent = '正在构建分类树...';
            
            try {
                const response = await fetch(`${API_BASE}/category/allList`);
                const data = await response.json();
                
                if (data.success) {
                    const categories = data.content;
                    
                    // 构建分类树
                    const level1 = categories.filter(item => item.parent === 0);
                    
                    let treeHTML = '<div class="category-tree">';
                    level1.forEach(parent => {
                        treeHTML += `<div class="category-item">
                            📁 ${parent.name} (ID: ${parent.id})
                        </div>`;
                        
                        const children = categories.filter(item => item.parent === parent.id);
                        children.forEach(child => {
                            treeHTML += `<div class="category-item subcategory">
                                📄 ${child.name} (ID: ${child.id})
                            </div>`;
                        });
                    });
                    treeHTML += '</div>';
                    
                    resultDiv.innerHTML = `✅ 分类树构建成功！\n一级分类: ${level1.length} 个\n\n${treeHTML}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ 获取分类失败: ${data.message}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 构建分类树失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testServiceAPI() {
            const resultDiv = document.getElementById('serviceResult');
            resultDiv.textContent = '正在测试服务API...';
            
            try {
                const response = await fetch(`${API_BASE}/service/getServiceListByPage?page=1&size=5`);
                const data = await response.json();
                
                if (data.success) {
                    const services = data.content.list;
                    resultDiv.textContent = `✅ 服务API正常！共 ${data.content.total} 个服务，当前显示 ${services.length} 个\n\n` + 
                        JSON.stringify(data, null, 2);
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ 服务API返回失败: ${data.message}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 服务API请求错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
