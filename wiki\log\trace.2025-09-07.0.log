2025-09-07 14:01:55.461 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 21.0.7 on LAPTOP-4VB8OLQM with PID 24356 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki\wiki)
2025-09-07 14:01:55.468 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-07 14:01:56.255 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-07 14:01:56.271 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-07 14:01:56.287 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-09-07 14:01:57.203 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-07 14:01:57.219 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-07 14:01:57.219 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-07 14:01:57.219 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-07 14:01:57.330 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-07 14:01:57.330 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1799 ms
2025-09-07 14:01:58.756 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-07 14:01:59.026 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-07 14:01:59.050 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-07 14:01:59.059 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.025 seconds (JVM running for 4.533)
2025-09-07 14:01:59.059 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-07 14:01:59.059 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-07 14:03:49.402 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-07 14:03:49.402 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-07 14:03:49.402 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-07 14:03:49.436 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841990698959904  [0;39m ------------- 开始 -------------
2025-09-07 14:03:49.436 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841990698959904  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:03:49.449 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841990698959904  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:03:49.449 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841990698959904  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:03:49.498 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841990698959904  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:03:49.498 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841990698959904  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:03:49.514 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841990698959904  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:03:49.514 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841990698959904  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:03:49.737 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4841990698959904  [0;39m {dataSource-1} inited
2025-09-07 14:03:49.937 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841990698959904  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:03:49.978 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841990698959904  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:03:49.978 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841990698959904  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:03:49.994 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841990698959904  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:03:49.994 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841990698959904  [0;39m ------------- 结束 耗时：558 ms -------------
2025-09-07 14:04:16.684 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841991591822368  [0;39m ------------- 开始 -------------
2025-09-07 14:04:16.684 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841991591822368  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:04:16.684 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841991591822368  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:04:16.684 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841991591822368  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:04:16.684 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841991591822368  [0;39m 请求参数: [{"name":"Test","page":1,"size":10}]
2025-09-07 14:04:16.684 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841991591822368  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='Test', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:04:16.684 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841991591822368  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='Test', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:04:16.684 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :48   [32m4841991591822368  [0;39m 📝 添加服务名称模糊查询条件：Test
2025-09-07 14:04:16.692 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841991591822368  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:04:16.712 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841991591822368  [0;39m 📋 数据库查询结果：共 4 条记录，当前页 4 条
2025-09-07 14:04:16.726 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841991591822368  [0;39m ✅ 服务查询完成，返回 4 条记录
2025-09-07 14:04:16.726 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841991591822368  [0;39m 📊 查询结果：共 4 条记录
2025-09-07 14:04:16.726 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841991591822368  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"}],"total":4},"message":"查询成功","success":true}
2025-09-07 14:04:16.726 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841991591822368  [0;39m ------------- 结束 耗时：42 ms -------------
2025-09-07 14:06:11.836 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841995365123104  [0;39m ------------- 开始 -------------
2025-09-07 14:06:11.836 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841995365123105  [0;39m ------------- 开始 -------------
2025-09-07 14:06:11.837 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841995365123104  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:11.837 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841995365123105  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:11.837 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841995365123104  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:11.837 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841995365123105  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:11.837 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841995365123104  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:11.837 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841995365123105  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:11.837 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841995365123104  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-07 14:06:11.837 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841995365123105  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-07 14:06:11.837 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841995365123104  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=6}
2025-09-07 14:06:11.837 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841995365123105  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=1000}
2025-09-07 14:06:11.837 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841995365123104  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=6}
2025-09-07 14:06:11.837 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841995365123105  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=1000}
2025-09-07 14:06:11.837 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841995365123104  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-07 14:06:11.837 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841995365123105  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-07 14:06:11.868 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841995365123104  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 6 条
2025-09-07 14:06:11.868 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4841995365123105  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 115132
2025-09-07 14:06:11.884 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841995365123105  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:11.900 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841995365123104  [0;39m ✅ 服务查询完成，返回 6 条记录
2025-09-07 14:06:11.900 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841995365123104  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:11.915 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841995365123104  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:11.915 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841995365123104  [0;39m ------------- 结束 耗时：79 ms -------------
2025-09-07 14:06:11.935 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841995365123105  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:11.935 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841995365123105  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:11.935 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841995365123105  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:11.946 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841995365123105  [0;39m ------------- 结束 耗时：110 ms -------------
2025-09-07 14:06:26.390 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841995842028576  [0;39m ------------- 开始 -------------
2025-09-07 14:06:26.390 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841995842028576  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-07 14:06:26.390 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841995842028576  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-07 14:06:26.397 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841995842028576  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:26.397 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841995842028576  [0;39m 请求参数: [{"username":"111"}]
2025-09-07 14:06:26.508 INFO  com.gec.wiki.service.impl.UserServiceImpl         :98   [32m4841995842028576  [0;39m 用户登录成功：111
2025-09-07 14:06:26.508 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841995842028576  [0;39m 返回结果: {"content":{"userInfo":{"realName":"wei","phone":"19978452688","id":4,"userType":2,"email":"","username":"111"},"token":"ca0dd40793b44beca14bddf7861a7ad7"},"message":"登录成功","success":true}
2025-09-07 14:06:26.508 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841995842028576  [0;39m ------------- 结束 耗时：118 ms -------------
2025-09-07 14:06:30.255 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841995968676896  [0;39m ------------- 开始 -------------
2025-09-07 14:06:30.255 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841995968676896  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:30.255 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841995968676897  [0;39m ------------- 开始 -------------
2025-09-07 14:06:30.257 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841995968676896  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:30.257 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841995968676897  [0;39m 请求地址: http://localhost:8880/category/allList GET
2025-09-07 14:06:30.258 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841995968676897  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-09-07 14:06:30.258 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841995968676896  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:30.259 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841995968676897  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:30.259 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841995968676896  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:30.259 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841995968676896  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:30.260 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841995968676896  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:30.261 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841995968676896  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:30.263 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841995968676897  [0;39m 请求参数: [{}]
2025-09-07 14:06:30.269 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841995968676896  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:30.280 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841995968676897  [0;39m 返回结果: {"content":[{"id":100,"name":"常规保养","parent":0,"sort":1},{"id":101,"name":"机油保养","parent":100,"sort":1},{"id":201,"name":"发动机检修","parent":200,"sort":1},{"id":301,"name":"电瓶维护","parent":300,"sort":1},{"id":102,"name":"轮胎保养","parent":100,"sort":2},{"id":200,"name":"发动机维修","parent":0,"sort":2},{"id":202,"name":"冷却系统","parent":200,"sort":2},{"id":302,"name":"空调系统","parent":300,"sort":2},{"id":103,"name":"制动系统","parent":100,"sort":3},{"id":300,"name":"电气系统","parent":0,"sort":3}],"success":true}
2025-09-07 14:06:30.280 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841995968676897  [0;39m ------------- 结束 耗时：25 ms -------------
2025-09-07 14:06:30.322 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841995968676896  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:30.322 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841995968676896  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:30.322 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841995968676896  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:30.327 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841995968676896  [0;39m ------------- 结束 耗时：72 ms -------------
2025-09-07 14:06:33.347 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996069995552  [0;39m ------------- 开始 -------------
2025-09-07 14:06:33.347 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996069995552  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:33.348 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996069995552  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:33.348 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996069995552  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:33.348 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996069995552  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:33.349 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996069995552  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:33.349 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996069995552  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:33.351 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996069995552  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:33.364 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996069995552  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:33.389 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996069995552  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:33.389 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996069995552  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:33.389 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996069995552  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:33.389 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996069995552  [0;39m ------------- 结束 耗时：42 ms -------------
2025-09-07 14:06:35.079 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996126749728  [0;39m ------------- 开始 -------------
2025-09-07 14:06:35.079 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996126749728  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:35.080 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996126749728  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:35.080 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996126749728  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:35.081 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996126749728  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:35.081 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996126749728  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:35.082 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996126749728  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:35.082 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996126749728  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:35.095 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996126749728  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:35.119 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996126749728  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:35.119 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996126749728  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:35.119 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996126749728  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:35.119 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996126749728  [0;39m ------------- 结束 耗时：40 ms -------------
2025-09-07 14:06:38.144 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996227183648  [0;39m ------------- 开始 -------------
2025-09-07 14:06:38.145 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996227183648  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:38.145 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996227183648  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:38.146 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996227183648  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:38.146 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996227183648  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:38.147 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996227183648  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:38.147 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996227183648  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:38.148 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996227183648  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:38.164 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996227183648  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:38.188 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996227183648  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:38.188 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996227183648  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:38.188 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996227183648  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:38.203 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996227183648  [0;39m ------------- 结束 耗时：44 ms -------------
2025-09-07 14:06:52.211 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996688131104  [0;39m ------------- 开始 -------------
2025-09-07 14:06:52.212 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996688131104  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:52.212 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996688131104  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:52.212 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996688131104  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:52.213 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996688131104  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:52.213 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996688131104  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:52.213 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996688131104  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:52.214 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996688131104  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:52.222 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996688131104  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:52.239 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996688131104  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:52.239 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996688131104  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:52.239 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996688131104  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:52.239 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996688131104  [0;39m ------------- 结束 耗时：28 ms -------------
2025-09-07 14:06:52.974 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996713133088  [0;39m ------------- 开始 -------------
2025-09-07 14:06:52.975 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996713133088  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:52.975 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996713133088  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:52.975 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996713133088  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:52.976 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996713133088  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:52.976 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996713133088  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:52.977 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996713133088  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:52.977 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996713133088  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:52.987 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996713133088  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:53.007 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996713133088  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:53.007 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996713133088  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:53.007 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996713133088  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:53.007 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996713133088  [0;39m ------------- 结束 耗时：33 ms -------------
2025-09-07 14:06:53.161 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996719260704  [0;39m ------------- 开始 -------------
2025-09-07 14:06:53.162 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996719260704  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:53.162 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996719260704  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:53.163 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996719260704  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:53.163 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996719260704  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:53.163 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996719260704  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:53.164 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996719260704  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:53.164 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996719260704  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:53.174 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996719260704  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:53.191 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996719260704  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:53.191 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996719260704  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:53.191 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996719260704  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:53.191 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996719260704  [0;39m ------------- 结束 耗时：30 ms -------------
2025-09-07 14:06:53.810 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996740527136  [0;39m ------------- 开始 -------------
2025-09-07 14:06:53.811 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996740527136  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:53.811 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996740527136  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:53.811 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996740527136  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:53.812 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996740527136  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:53.812 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996740527136  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:53.812 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996740527136  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:53.813 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996740527136  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:53.821 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996740527136  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:53.843 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996740527136  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:53.843 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996740527136  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:53.843 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996740527136  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:53.843 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996740527136  [0;39m ------------- 结束 耗时：33 ms -------------
2025-09-07 14:06:54.226 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996754158624  [0;39m ------------- 开始 -------------
2025-09-07 14:06:54.227 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996754158624  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:54.227 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996754158624  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:54.227 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996754158624  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:54.228 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996754158624  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:54.228 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996754158624  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:54.228 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996754158624  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:54.228 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996754158624  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:54.238 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996754158624  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:54.264 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996754158624  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:54.264 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996754158624  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:54.264 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996754158624  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:54.264 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996754158624  [0;39m ------------- 结束 耗时：38 ms -------------
2025-09-07 14:06:58.260 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996886344736  [0;39m ------------- 开始 -------------
2025-09-07 14:06:58.261 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996886344736  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:58.262 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996886344736  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:58.262 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996886344736  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:58.262 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996886344736  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:58.263 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996886344736  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:58.263 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996886344736  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:58.264 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996886344736  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:58.279 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996886344736  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:58.306 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996886344736  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:58.306 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996886344736  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:58.306 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996886344736  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:58.306 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996886344736  [0;39m ------------- 结束 耗时：46 ms -------------
2025-09-07 14:06:59.684 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841996932973600  [0;39m ------------- 开始 -------------
2025-09-07 14:06:59.684 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841996932973600  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:06:59.685 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841996932973600  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:06:59.685 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841996932973600  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:06:59.685 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841996932973600  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:06:59.686 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841996932973600  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:59.686 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841996932973600  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:06:59.687 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841996932973600  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:06:59.696 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841996932973600  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:06:59.724 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841996932973600  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:06:59.724 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841996932973600  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:06:59.724 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841996932973600  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:06:59.724 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841996932973600  [0;39m ------------- 结束 耗时：41 ms -------------
2025-09-07 14:07:02.457 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841997023872032  [0;39m ------------- 开始 -------------
2025-09-07 14:07:02.458 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841997023872032  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:07:02.458 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841997023872032  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:07:02.459 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841997023872032  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:07:02.459 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841997023872032  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:07:02.459 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841997023872032  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:02.460 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841997023872032  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:02.460 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841997023872032  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:07:02.469 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841997023872032  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:07:02.494 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841997023872032  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:07:02.494 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841997023872032  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:07:02.494 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841997023872032  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:07:02.494 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841997023872032  [0;39m ------------- 结束 耗时：37 ms -------------
2025-09-07 14:07:02.712 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841997032227872  [0;39m ------------- 开始 -------------
2025-09-07 14:07:02.713 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841997032227872  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:07:02.713 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841997032227872  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:07:02.714 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841997032227872  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:07:02.714 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841997032227872  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:07:02.715 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841997032227872  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:02.715 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841997032227872  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:02.716 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841997032227872  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:07:02.725 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841997032227872  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:07:02.751 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841997032227872  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:07:02.751 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841997032227872  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:07:02.751 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841997032227872  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:07:02.751 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841997032227872  [0;39m ------------- 结束 耗时：39 ms -------------
2025-09-07 14:07:02.940 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841997039698976  [0;39m ------------- 开始 -------------
2025-09-07 14:07:02.941 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841997039698976  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:07:02.941 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841997039698976  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:07:02.942 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841997039698976  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:07:02.943 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841997039698976  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:07:02.943 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841997039698976  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:02.943 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841997039698976  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:02.944 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841997039698976  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:07:02.955 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841997039698976  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:07:02.974 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841997039698976  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:07:02.974 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841997039698976  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:07:02.974 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841997039698976  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:07:02.974 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841997039698976  [0;39m ------------- 结束 耗时：34 ms -------------
2025-09-07 14:07:06.388 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841997152683040  [0;39m ------------- 开始 -------------
2025-09-07 14:07:06.388 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841997152683040  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:07:06.388 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841997152683040  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:07:06.389 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841997152683040  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:07:06.390 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841997152683040  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:07:06.390 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841997152683040  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:06.390 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841997152683040  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:06.391 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841997152683040  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:07:06.401 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841997152683040  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:07:06.428 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841997152683040  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:07:06.428 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841997152683040  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:07:06.428 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841997152683040  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:07:06.432 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841997152683040  [0;39m ------------- 结束 耗时：44 ms -------------
2025-09-07 14:07:08.075 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841997207962656  [0;39m ------------- 开始 -------------
2025-09-07 14:07:08.076 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841997207962656  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:07:08.076 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841997207962656  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:07:08.076 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841997207962656  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:07:08.077 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841997207962656  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:07:08.078 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841997207962656  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:08.078 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841997207962656  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:08.078 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841997207962656  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:07:08.090 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841997207962656  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:07:08.105 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841997207962656  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:07:08.105 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841997207962656  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:07:08.105 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841997207962656  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:07:08.120 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841997207962656  [0;39m ------------- 结束 耗时：45 ms -------------
2025-09-07 14:07:10.309 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841997281166368  [0;39m ------------- 开始 -------------
2025-09-07 14:07:10.310 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841997281166368  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:07:10.310 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841997281166368  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:07:10.311 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841997281166368  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:07:10.311 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841997281166368  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:07:10.311 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4841997281166368  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:10.312 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4841997281166368  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:07:10.312 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4841997281166368  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:07:10.322 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4841997281166368  [0;39m 📋 数据库查询结果：共 10 条记录，当前页 10 条
2025-09-07 14:07:10.348 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4841997281166368  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:07:10.348 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4841997281166368  [0;39m 📊 查询结果：共 10 条记录
2025-09-07 14:07:10.354 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841997281166368  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:35:31"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":10},"message":"查询成功","success":true}
2025-09-07 14:07:10.354 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841997281166368  [0;39m ------------- 结束 耗时：45 ms -------------
2025-09-07 14:07:16.318 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841997478069280  [0;39m ------------- 开始 -------------
2025-09-07 14:07:16.318 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841997478069280  [0;39m 请求地址: http://localhost:8880/service/batchUpdateStatus POST
2025-09-07 14:07:16.318 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841997478069280  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.batchUpdateStatus
2025-09-07 14:07:16.318 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841997478069280  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:07:16.322 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841997478069280  [0;39m 请求参数: [{"ids":[5],"status":1}]
2025-09-07 14:07:16.322 INFO  com.gec.wiki.controller.ServiceController         :179  [32m4841997478069280  [0;39m 批量更新服务状态，参数：BatchUpdateStatusReq{ids=[5], status=1}
2025-09-07 14:07:16.337 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841997478069280  [0;39m 返回结果: {"message":"批量更新状态成功","success":true}
2025-09-07 14:07:16.337 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841997478069280  [0;39m ------------- 结束 耗时：19 ms -------------
2025-09-07 14:07:17.427 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841997514408992  [0;39m ------------- 开始 -------------
2025-09-07 14:07:17.427 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841997514408992  [0;39m 请求地址: http://localhost:8880/service/batchUpdateStatus POST
2025-09-07 14:07:17.427 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841997514408992  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.batchUpdateStatus
2025-09-07 14:07:17.427 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841997514408992  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:07:17.427 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841997514408992  [0;39m 请求参数: [{"ids":[5],"status":1}]
2025-09-07 14:07:17.427 INFO  com.gec.wiki.controller.ServiceController         :179  [32m4841997514408992  [0;39m 批量更新服务状态，参数：BatchUpdateStatusReq{ids=[5], status=1}
2025-09-07 14:07:17.436 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841997514408992  [0;39m 返回结果: {"message":"批量更新状态成功","success":true}
2025-09-07 14:07:17.436 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841997514408992  [0;39m ------------- 结束 耗时：9 ms -------------
2025-09-07 14:07:30.245 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4841997934429216  [0;39m ------------- 开始 -------------
2025-09-07 14:07:30.245 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4841997934429216  [0;39m 请求地址: http://localhost:8880/service/save POST
2025-09-07 14:07:30.245 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4841997934429216  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.save
2025-09-07 14:07:30.245 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4841997934429216  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:07:30.252 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4841997934429216  [0;39m 请求参数: [{"category1Id":100,"category2Id":101,"cover":"","description":"Test Description","duration":60,"name":"Test Service","price":99.99,"status":1}]
2025-09-07 14:07:30.252 INFO  com.gec.wiki.controller.ServiceController         :109  [32m4841997934429216  [0;39m 保存或更新服务，参数：ServiceSaveReq{id=null, name='Test Service', category1Id=100, category2Id=101, description='Test Description', content='null', cover='', images='null', price=99.99, originalPrice=null, duration=60, isRecommend=null, status=1}
2025-09-07 14:07:30.294 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4841997934429216  [0;39m 返回结果: {"message":"添加成功","success":true}
2025-09-07 14:07:30.294 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4841997934429216  [0;39m ------------- 结束 耗时：49 ms -------------
2025-09-07 14:08:47.196 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4842000455959584  [0;39m ------------- 开始 -------------
2025-09-07 14:08:47.196 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4842000455959584  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:08:47.197 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4842000455959584  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:08:47.197 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4842000455992352  [0;39m ------------- 开始 -------------
2025-09-07 14:08:47.197 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4842000455959584  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:08:47.198 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4842000455992352  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:08:47.198 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4842000455959584  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-07 14:08:47.198 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4842000455992352  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:08:47.198 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4842000455959584  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=6}
2025-09-07 14:08:47.199 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4842000455992352  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:08:47.199 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4842000455959584  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=6}
2025-09-07 14:08:47.199 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4842000455992352  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-07 14:08:47.199 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4842000455959584  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-07 14:08:47.200 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4842000455992352  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=1000}
2025-09-07 14:08:47.200 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4842000455992352  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=1000}
2025-09-07 14:08:47.200 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4842000455992352  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-07 14:08:47.202 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4842000455959584  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 76915
2025-09-07 14:08:47.204 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4842000455992352  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 136934
2025-09-07 14:08:47.217 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4842000455959584  [0;39m 📋 数据库查询结果：共 11 条记录，当前页 6 条
2025-09-07 14:08:47.222 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4842000455992352  [0;39m 📋 数据库查询结果：共 11 条记录，当前页 11 条
2025-09-07 14:08:47.241 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4842000455959584  [0;39m ✅ 服务查询完成，返回 6 条记录
2025-09-07 14:08:47.242 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4842000455959584  [0;39m 📊 查询结果：共 11 条记录
2025-09-07 14:08:47.242 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4842000455959584  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T14:07:30","description":"Test Description","duration":60,"id":4834096586359845,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T14:07:30"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"}],"total":11},"message":"查询成功","success":true}
2025-09-07 14:08:47.243 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4842000455959584  [0;39m ------------- 结束 耗时：47 ms -------------
2025-09-07 14:08:47.258 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4842000455992352  [0;39m ✅ 服务查询完成，返回 11 条记录
2025-09-07 14:08:47.259 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4842000455992352  [0;39m 📊 查询结果：共 11 条记录
2025-09-07 14:08:47.260 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4842000455992352  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T14:07:30","description":"Test Description","duration":60,"id":4834096586359845,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T14:07:30"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T14:07:17"},{"bookingCount":0,"category1Id":300,"category2Id":302,"categoryName":"空调系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"isRecommend":1,"name":"空调清洗","originalPrice":220.00,"price":180.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T21:37:25"}],"total":11},"message":"查询成功","success":true}
2025-09-07 14:08:47.261 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4842000455992352  [0;39m ------------- 结束 耗时：64 ms -------------
2025-09-07 14:08:50.492 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4842000563962912  [0;39m ------------- 开始 -------------
2025-09-07 14:08:50.492 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4842000563962913  [0;39m ------------- 开始 -------------
2025-09-07 14:08:50.493 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4842000563962913  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-07 14:08:50.493 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4842000563962912  [0;39m 请求地址: http://localhost:8880/category/allList GET
2025-09-07 14:08:50.494 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4842000563962913  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-07 14:08:50.494 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4842000563962912  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-09-07 14:08:50.494 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4842000563962913  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:08:50.494 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4842000563962912  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-07 14:08:50.494 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4842000563962913  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-07 14:08:50.494 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4842000563962912  [0;39m 请求参数: [{}]
2025-09-07 14:08:50.494 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4842000563962913  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:08:50.495 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4842000563962913  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-07 14:08:50.495 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4842000563962913  [0;39m 🔢 执行分页查询：页码=1, 页大小=10
2025-09-07 14:08:50.498 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4842000563962912  [0;39m 返回结果: {"content":[{"id":100,"name":"常规保养","parent":0,"sort":1},{"id":101,"name":"机油保养","parent":100,"sort":1},{"id":201,"name":"发动机检修","parent":200,"sort":1},{"id":301,"name":"电瓶维护","parent":300,"sort":1},{"id":102,"name":"轮胎保养","parent":100,"sort":2},{"id":200,"name":"发动机维修","parent":0,"sort":2},{"id":202,"name":"冷却系统","parent":200,"sort":2},{"id":302,"name":"空调系统","parent":300,"sort":2},{"id":103,"name":"制动系统","parent":100,"sort":3},{"id":300,"name":"电气系统","parent":0,"sort":3}],"success":true}
2025-09-07 14:08:50.498 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4842000563962912  [0;39m ------------- 结束 耗时：6 ms -------------
2025-09-07 14:08:50.504 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4842000563962913  [0;39m 📋 数据库查询结果：共 11 条记录，当前页 10 条
2025-09-07 14:08:50.533 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4842000563962913  [0;39m ✅ 服务查询完成，返回 10 条记录
2025-09-07 14:08:50.533 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4842000563962913  [0;39m 📊 查询结果：共 11 条记录
2025-09-07 14:08:50.533 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4842000563962913  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T14:07:30","description":"Test Description","duration":60,"id":4834096586359845,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T14:07:30"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-07T13:39:04","description":"Test Description","duration":60,"id":4834096586359844,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:17"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:50:12","description":"Test Description","duration":60,"id":4834096586359843,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:20"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:56","description":"Test Description","duration":60,"id":4834096586359842,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T13:56:22"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"cover":"","createTime":"2025-09-05T22:49:38","description":"Test Description","duration":60,"id":4834096586359841,"isRecommend":0,"name":"Test Service","originalPrice":0.00,"price":99.99,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:49:38"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"cover":"9d908036-c039-490d-86cd-85b46b28f06a_抹香鲸.jpg","createTime":"2025-09-04T19:08:40","description":"保养发动机","duration":60,"id":4834096586359840,"isRecommend":0,"name":"汽车保养","originalPrice":0.00,"price":200.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:59:05"},{"bookingCount":0,"category1Id":100,"category2Id":101,"categoryName":"机油保养","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"isRecommend":1,"name":"机油更换","originalPrice":180.00,"price":150.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":100,"category2Id":103,"categoryName":"制动系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"isRecommend":0,"name":"刹车片更换","originalPrice":350.00,"price":300.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-05T22:33:58"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"isRecommend":0,"name":"发动机大修","originalPrice":3000.00,"price":2500.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-03T19:55:58"},{"bookingCount":0,"category1Id":200,"category2Id":202,"categoryName":"冷却系统","completeCount":0,"createTime":"2025-09-03T19:55:58","description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"isRecommend":1,"name":"水箱清洗","originalPrice":150.00,"price":120.00,"ratingCount":0,"ratingScore":5.0,"status":1,"updateTime":"2025-09-07T14:07:17"}],"total":11},"message":"查询成功","success":true}
2025-09-07 14:08:50.533 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4842000563962913  [0;39m ------------- 结束 耗时：41 ms -------------
