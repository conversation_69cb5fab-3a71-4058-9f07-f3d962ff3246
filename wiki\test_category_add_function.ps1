# 分类手动输入功能测试脚本
# 测试新添加的一级分类和二级分类手动输入功能

Write-Host "=== 分类手动输入功能测试 ===" -ForegroundColor Green

# 配置
$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
}

Write-Host "`n🧪 测试1: 添加新的一级分类..." -ForegroundColor Yellow
$newCategory1 = @{
    name = "测试分类API_$(Get-Date -Format 'HHmmss')"
    parent = 0
    sort = 99
} | ConvertTo-Json

try {
    $response1 = Invoke-RestMethod -Uri "$baseUrl/category/save" -Method Post -Body $newCategory1 -Headers $headers
    Write-Host "添加一级分类响应: $($response1 | ConvertTo-Json)" -ForegroundColor Cyan
    
    if ($response1.success -eq $true) {
        Write-Host "✅ 一级分类添加成功: $($response1.message)" -ForegroundColor Green
        $category1Id = $response1.content.id
        Write-Host "📋 新分类ID: $category1Id" -ForegroundColor White
    } else {
        Write-Host "❌ 一级分类添加失败: $($response1.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 一级分类添加请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🧪 测试2: 添加新的二级分类..." -ForegroundColor Yellow
if ($category1Id) {
    $newCategory2 = @{
        name = "测试子分类API_$(Get-Date -Format 'HHmmss')"
        parent = $category1Id
        sort = 1
    } | ConvertTo-Json

    try {
        $response2 = Invoke-RestMethod -Uri "$baseUrl/category/save" -Method Post -Body $newCategory2 -Headers $headers
        Write-Host "添加二级分类响应: $($response2 | ConvertTo-Json)" -ForegroundColor Cyan
        
        if ($response2.success -eq $true) {
            Write-Host "✅ 二级分类添加成功: $($response2.message)" -ForegroundColor Green
        } else {
            Write-Host "❌ 二级分类添加失败: $($response2.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 二级分类添加请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️ 跳过二级分类测试，因为一级分类添加失败" -ForegroundColor Yellow
}

Write-Host "`n🧪 测试3: 重复分类名称测试..." -ForegroundColor Yellow
$duplicateCategory = @{
    name = "常规保养"  # 这应该是已存在的分类
    parent = 0
    sort = 1
} | ConvertTo-Json

try {
    $response3 = Invoke-RestMethod -Uri "$baseUrl/category/save" -Method Post -Body $duplicateCategory -Headers $headers
    Write-Host "重复分类测试响应: $($response3 | ConvertTo-Json)" -ForegroundColor Cyan
    
    if ($response3.success -eq $false) {
        Write-Host "✅ 重复检查正常工作: $($response3.message)" -ForegroundColor Green
    } else {
        Write-Host "❌ 重复检查可能有问题" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 重复分类测试请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 验证新添加的分类..." -ForegroundColor Yellow
try {
    $categoryList = Invoke-RestMethod -Uri "$baseUrl/category/allList" -Method Get -Headers $headers
    Write-Host "分类列表总数: $($categoryList.content.Count)" -ForegroundColor Cyan
    
    # 显示新添加的分类
    $newCategories = $categoryList.content | Where-Object { $_.name -like "*测试*API*" }
    if ($newCategories) {
        Write-Host "✅ 找到新添加的测试分类:" -ForegroundColor Green
        foreach ($cat in $newCategories) {
            Write-Host "  - ID: $($cat.id), 名称: $($cat.name), 父级: $($cat.parent)" -ForegroundColor White
        }
    } else {
        Write-Host "⚠️ 未找到新添加的测试分类" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 获取分类列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎯 前端功能测试说明..." -ForegroundColor Blue
Write-Host "请在浏览器中进行以下测试：" -ForegroundColor White
Write-Host "1. 进入管理员 → 服务管理 → 点击新增" -ForegroundColor Cyan
Write-Host "2. 在一级分类下拉框中输入：新测试分类" -ForegroundColor Cyan
Write-Host "3. 应该看到'➕ 添加新分类: 新测试分类'选项" -ForegroundColor Cyan
Write-Host "4. 点击该选项或按回车键" -ForegroundColor Cyan
Write-Host "5. 应该显示成功消息并自动选中新分类" -ForegroundColor Cyan
Write-Host "6. 在二级分类下拉框中输入：新子分类" -ForegroundColor Cyan
Write-Host "7. 重复上述步骤测试二级分类添加" -ForegroundColor Cyan

Write-Host "`n✨ 功能特点验证..." -ForegroundColor Blue
Write-Host "✅ 实时搜索：输入时自动过滤现有分类" -ForegroundColor White
Write-Host "✅ 重名检查：不允许同级分类重名" -ForegroundColor White
Write-Host "✅ 自动关联：二级分类自动与一级分类建立父子关系" -ForegroundColor White
Write-Host "✅ 即时反馈：添加成功后立即显示和选中" -ForegroundColor White
Write-Host "✅ 用户友好：清晰的操作提示和错误处理" -ForegroundColor White

Write-Host "`n🔧 调试信息..." -ForegroundColor Blue
Write-Host "在浏览器控制台中应该能看到：" -ForegroundColor White
Write-Host "- '🔍 一级分类搜索: [输入的文本]'" -ForegroundColor Cyan
Write-Host "- '➕ 添加一级分类: [分类名称]'" -ForegroundColor Cyan
Write-Host "- '✅ 分类添加成功'" -ForegroundColor Cyan

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果API测试通过，请继续在前端界面测试完整功能" -ForegroundColor White
