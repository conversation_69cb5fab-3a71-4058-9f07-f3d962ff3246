2025-09-10 16:19:20.498 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 21.0.7 on LAPTOP-4VB8OLQM with PID 26284 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki\wiki)
2025-09-10 16:19:20.498 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-10 16:19:21.208 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-10 16:19:21.208 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 16:19:21.261 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-09-10 16:19:22.285 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-10 16:19:22.297 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-10 16:19:22.297 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-10 16:19:22.297 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-10 16:19:22.437 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-10 16:19:22.437 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1883 ms
2025-09-10 16:19:23.942 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-10 16:19:24.242 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-10 16:19:24.242 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8880 is already in use
2025-09-10 16:19:24.258 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-10 16:19:24.258 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Pausing ProtocolHandler ["http-nio-8880"]
2025-09-10 16:19:24.258 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-09-10 16:19:24.422 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Stopping ProtocolHandler ["http-nio-8880"]
2025-09-10 16:19:24.422 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Destroying ProtocolHandler ["http-nio-8880"]
2025-09-10 16:19:24.422 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-09-10 16:19:24.457 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8880 was already in use.

Action:

Identify and stop the process that's listening on port 8880 or configure this application to listen on another port.

2025-09-10 16:20:47.665 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 21.0.7 on LAPTOP-4VB8OLQM with PID 26304 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki\wiki)
2025-09-10 16:20:47.671 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-10 16:20:48.293 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-10 16:20:48.293 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 16:20:48.326 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-09-10 16:20:49.014 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-10 16:20:49.030 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-10 16:20:49.030 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-10 16:20:49.030 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-10 16:20:49.129 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-10 16:20:49.129 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1409 ms
2025-09-10 16:20:50.317 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-10 16:20:50.588 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-10 16:20:50.622 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-10 16:20:50.622 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.347 seconds (JVM running for 3.795)
2025-09-10 16:20:50.622 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-10 16:20:50.622 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-10 16:22:17.889 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-10 16:22:17.889 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-10 16:22:17.889 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-10 16:22:17.966 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4850756418470944  [0;39m ------------- 开始 -------------
2025-09-10 16:22:17.966 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4850756418470944  [0;39m 请求地址: http://localhost:8880/vehicle/save POST
2025-09-10 16:22:17.966 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4850756418470944  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.save
2025-09-10 16:22:17.966 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4850756418470944  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-10 16:22:18.021 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4850756418470944  [0;39m 请求参数: [{"brand":"??","color":"??","engineNumber":"TEST123456","licensePlate":"?A12345","model":"???","userId":1,"vin":"VIN123456789","year":2022}]
2025-09-10 16:22:18.028 INFO  com.gec.wiki.controller.VehicleController         :67   [32m4850756418470944  [0;39m 收到的保存请求：ID = null, 车牌号 = ?A12345
2025-09-10 16:22:18.174 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4850756418470944  [0;39m {dataSource-1} inited
2025-09-10 16:22:18.361 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4850756418470944  [0;39m 返回结果: {"message":"添加成功","success":true}
2025-09-10 16:22:18.361 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4850756418470944  [0;39m ------------- 结束 耗时：395 ms -------------
2025-09-10 16:22:30.318 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4850756823221280  [0;39m ------------- 开始 -------------
2025-09-10 16:22:30.318 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4850756823221280  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-10 16:22:30.319 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4850756823221280  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-10 16:22:30.319 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4850756823221280  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-10 16:22:30.320 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4850756823221280  [0;39m 请求参数: [{"page":1,"size":10,"userId":1}]
2025-09-10 16:22:30.417 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4850756823221280  [0;39m 总行数：1
2025-09-10 16:22:30.417 INFO  com.gec.wiki.controller.VehicleController         :50   [32m4850756823221280  [0;39m 总页数：1
2025-09-10 16:22:30.424 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4850756823221280  [0;39m 返回结果: {"content":{"list":[{"brand":"??","color":"??","createTime":"2025-09-10T16:22:18","engineNumber":"TEST123456","id":4850756420502560,"licensePlate":"?A12345","model":"???","updateTime":"2025-09-10T16:22:18","userId":1,"vin":"VIN123456789","year":2022}],"total":1},"success":true}
2025-09-10 16:22:30.424 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4850756823221280  [0;39m ------------- 结束 耗时：106 ms -------------
2025-09-10 16:23:22.052 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4850758518440992  [0;39m ------------- 开始 -------------
2025-09-10 16:23:22.053 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4850758518440992  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-10 16:23:22.053 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4850758518440992  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-10 16:23:22.054 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4850758518440992  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-10 16:23:22.056 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4850758518440992  [0;39m 请求参数: [{"username":"test"}]
2025-09-10 16:23:22.059 INFO  com.gec.wiki.service.impl.UserServiceImpl         :65   [32m4850758518440992  [0;39m 🔐 用户登录尝试：username=test, ip=0:0:0:0:0:0:0:1
2025-09-10 16:23:22.059 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4850758518440992  [0;39m 🔒 检查用户锁定状态：username=test
2025-09-10 16:23:22.065 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :48   [32m4850758518440992  [0;39m ✅ 用户未被锁定，无锁定记录：username=test
2025-09-10 16:23:22.071 WARN  o.s.security.crypto.bcrypt.BCryptPasswordEncoder  :130  [32m4850758518440992  [0;39m Encoded password does not look like BCrypt
2025-09-10 16:23:22.071 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :78   [32m4850758518440992  [0;39m 📝 记录登录失败：username=test, userId=2
2025-09-10 16:23:22.078 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :95   [32m4850758518440992  [0;39m ➕ 创建新的登录失败记录：username=test, failCount=1
2025-09-10 16:23:22.085 WARN  com.gec.wiki.service.impl.UserServiceImpl         :120  [32m4850758518440992  [0;39m ❌ 密码错误：username=test, failCount=1/5
2025-09-10 16:23:22.085 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4850758518440992  [0;39m 返回结果: {"message":"密码错误，连续错误 1/5 次","success":false}
2025-09-10 16:23:22.092 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4850758518440992  [0;39m ------------- 结束 耗时：40 ms -------------
2025-09-10 16:24:49.553 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4850761385673760  [0;39m ------------- 开始 -------------
2025-09-10 16:24:49.554 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4850761385673760  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-10 16:24:49.555 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4850761385673760  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-10 16:24:49.555 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4850761385673760  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-10 16:24:49.555 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4850761385673760  [0;39m 请求参数: [{"username":"test"}]
2025-09-10 16:24:49.556 INFO  com.gec.wiki.service.impl.UserServiceImpl         :65   [32m4850761385673760  [0;39m 🔐 用户登录尝试：username=test, ip=0:0:0:0:0:0:0:1
2025-09-10 16:24:49.556 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4850761385673760  [0;39m 🔒 检查用户锁定状态：username=test
2025-09-10 16:24:49.572 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4850761385673760  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 87475
2025-09-10 16:24:49.580 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :65   [32m4850761385673760  [0;39m ✅ 用户未被锁定：username=test
2025-09-10 16:24:49.588 WARN  o.s.security.crypto.bcrypt.BCryptPasswordEncoder  :130  [32m4850761385673760  [0;39m Encoded password does not look like BCrypt
2025-09-10 16:24:49.588 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :78   [32m4850761385673760  [0;39m 📝 记录登录失败：username=test, userId=2
2025-09-10 16:24:49.594 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :111  [32m4850761385673760  [0;39m ⚠️ 更新登录失败记录：username=test, failCount=2/5
2025-09-10 16:24:49.608 WARN  com.gec.wiki.service.impl.UserServiceImpl         :120  [32m4850761385673760  [0;39m ❌ 密码错误：username=test, failCount=2/5
2025-09-10 16:24:49.608 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4850761385673760  [0;39m 返回结果: {"message":"密码错误，连续错误 2/5 次","success":false}
2025-09-10 16:24:49.608 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4850761385673760  [0;39m ------------- 结束 耗时：55 ms -------------
2025-09-10 16:25:47.889 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4850763297227808  [0;39m ------------- 开始 -------------
2025-09-10 16:25:47.890 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4850763297227808  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-10 16:25:47.890 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4850763297227808  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-10 16:25:47.890 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4850763297227808  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-10 16:25:47.891 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4850763297227808  [0;39m 请求参数: [{"username":"admin"}]
2025-09-10 16:25:47.891 INFO  com.gec.wiki.service.impl.UserServiceImpl         :65   [32m4850763297227808  [0;39m 🔐 用户登录尝试：username=admin, ip=0:0:0:0:0:0:0:1
2025-09-10 16:25:47.891 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4850763297227808  [0;39m 🔒 检查用户锁定状态：username=admin
2025-09-10 16:25:47.894 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :65   [32m4850763297227808  [0;39m ✅ 用户未被锁定：username=admin
2025-09-10 16:25:47.898 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :155  [32m4850763297227808  [0;39m 🧹 清除登录失败记录：username=admin
2025-09-10 16:25:47.901 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :168  [32m4850763297227808  [0;39m ✅ 成功清除登录失败记录：username=admin, 清除记录数=1
2025-09-10 16:25:47.907 INFO  com.gec.wiki.service.impl.UserServiceImpl         :160  [32m4850763297227808  [0;39m ✅ 用户登录成功：username=admin, userType=3, ip=0:0:0:0:0:0:0:1
2025-09-10 16:25:47.907 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4850763297227808  [0;39m 返回结果: {"content":{"userInfo":{"realName":"系统管理员","phone":"13800138000","id":1,"userType":3,"email":"<EMAIL>","username":"admin"},"token":"7107bfec1d5242559adf18b686dbabfe"},"message":"登录成功","success":true}
2025-09-10 16:25:47.907 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4850763297227808  [0;39m ------------- 结束 耗时：18 ms -------------
2025-09-10 16:26:58.142 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4850765599278112  [0;39m ------------- 开始 -------------
2025-09-10 16:26:58.142 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4850765599278112  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-10 16:26:58.145 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4850765599278112  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-10 16:26:58.146 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4850765599278112  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-10 16:26:58.146 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4850765599278112  [0;39m 请求参数: [{"username":"owner1"}]
2025-09-10 16:26:58.146 INFO  com.gec.wiki.service.impl.UserServiceImpl         :65   [32m4850765599278112  [0;39m 🔐 用户登录尝试：username=owner1, ip=0:0:0:0:0:0:0:1
2025-09-10 16:26:58.146 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4850765599278112  [0;39m 🔒 检查用户锁定状态：username=owner1
2025-09-10 16:26:58.146 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4850765599278112  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 70239
2025-09-10 16:26:58.153 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :48   [32m4850765599278112  [0;39m ✅ 用户未被锁定，无锁定记录：username=owner1
2025-09-10 16:26:58.153 WARN  o.s.security.crypto.bcrypt.BCryptPasswordEncoder  :130  [32m4850765599278112  [0;39m Encoded password does not look like BCrypt
2025-09-10 16:26:58.159 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :78   [32m4850765599278112  [0;39m 📝 记录登录失败：username=owner1, userId=8
2025-09-10 16:26:58.167 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :95   [32m4850765599278112  [0;39m ➕ 创建新的登录失败记录：username=owner1, failCount=1
2025-09-10 16:26:58.174 WARN  com.gec.wiki.service.impl.UserServiceImpl         :120  [32m4850765599278112  [0;39m ❌ 密码错误：username=owner1, failCount=1/5
2025-09-10 16:26:58.174 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4850765599278112  [0;39m 返回结果: {"message":"密码错误，连续错误 1/5 次","success":false}
2025-09-10 16:26:58.174 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4850765599278112  [0;39m ------------- 结束 耗时：32 ms -------------
