2025-09-03 19:18:15.470 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 21768 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 19:18:15.475 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 19:18:16.807 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 19:18:16.818 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 19:18:16.819 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 19:18:16.819 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 19:18:16.921 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 19:18:16.922 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1392 ms
2025-09-03 19:18:18.604 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 19:18:18.825 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 19:18:18.852 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 19:18:18.861 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.903 seconds (JVM running for 5.296)
2025-09-03 19:18:18.864 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 19:18:18.864 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 19:20:59.447 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-03 19:29:11.708 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 17720 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 19:29:11.711 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 19:29:12.944 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 19:29:12.956 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 19:29:12.957 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 19:29:12.958 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 19:29:13.117 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 19:29:13.117 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1311 ms
2025-09-03 19:29:14.859 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 19:29:15.037 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 19:29:15.052 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 19:29:15.060 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.702 seconds (JVM running for 4.73)
2025-09-03 19:29:15.063 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 19:29:15.063 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 19:39:36.165 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-03 19:39:58.625 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 22104 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 19:39:58.629 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 19:40:00.124 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 19:40:00.138 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 19:40:00.139 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 19:40:00.140 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 19:40:00.262 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 19:40:00.262 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1580 ms
2025-09-03 19:40:02.257 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 19:40:02.537 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 19:40:02.560 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 19:40:02.570 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.408 seconds (JVM running for 5.57)
2025-09-03 19:40:02.573 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 19:40:02.574 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 19:40:44.817 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-03 20:00:48.543 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 25628 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 20:00:48.545 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 20:00:49.589 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 20:00:49.596 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 20:00:49.597 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 20:00:49.597 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 20:00:49.681 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 20:00:49.681 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1098 ms
2025-09-03 20:00:51.182 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 20:00:51.397 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 20:00:51.417 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 20:00:51.427 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.206 seconds (JVM running for 4.22)
2025-09-03 20:00:51.431 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 20:00:51.431 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 20:02:00.945 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-03 20:29:34.885 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 20524 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 20:29:34.889 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 20:29:36.038 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 20:29:36.045 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 20:29:36.045 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 20:29:36.046 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 20:29:36.128 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 20:29:36.128 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1194 ms
2025-09-03 20:29:37.652 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 20:29:37.843 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 20:29:37.862 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 20:29:37.871 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.349 seconds (JVM running for 4.364)
2025-09-03 20:29:37.873 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 20:29:37.873 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 20:34:19.008 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 20:34:19.008 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-03 20:34:19.009 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-09-03 20:34:19.099 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831433821291552  [0;39m ------------- 开始 -------------
2025-09-03 20:34:19.100 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831433821291552  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 20:34:19.100 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831433821291552  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 20:34:19.101 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831433821291552  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:34:19.171 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831433821291552  [0;39m 请求参数: [{"username":"admin"}]
2025-09-03 20:34:19.253 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4831433821291552  [0;39m {dataSource-1} inited
2025-09-03 20:34:19.749 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831433821291552  [0;39m 返回结果: {"message":"密码错误","success":false}
2025-09-03 20:34:19.749 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831433821291552  [0;39m ------------- 结束 耗时：652 ms -------------
2025-09-03 20:36:02.373 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831437205439520  [0;39m ------------- 开始 -------------
2025-09-03 20:36:02.373 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831437205439520  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 20:36:02.374 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831437205439520  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 20:36:02.374 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831437205439520  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:36:02.374 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831437205439520  [0;39m 请求参数: [{"username":"test"}]
2025-09-03 20:36:02.388 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4831437205439520  [0;39m discard long time none received connection. , jdbcUrl : ****************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 102719
2025-09-03 20:36:02.425 WARN  o.s.security.crypto.bcrypt.BCryptPasswordEncoder  :130  [32m4831437205439520  [0;39m Encoded password does not look like BCrypt
2025-09-03 20:36:02.425 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831437205439520  [0;39m 返回结果: {"message":"密码错误","success":false}
2025-09-03 20:36:02.425 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831437205439520  [0;39m ------------- 结束 耗时：52 ms -------------
2025-09-03 20:41:46.714 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 25276 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 20:41:46.717 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 20:41:47.674 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 20:41:47.681 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 20:41:47.681 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 20:41:47.682 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 20:41:47.753 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 20:41:47.754 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 990 ms
2025-09-03 20:41:49.236 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 20:41:49.423 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 20:41:49.446 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 20:41:49.456 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.071 seconds (JVM running for 4.106)
2025-09-03 20:41:49.459 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 20:41:49.460 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 20:45:57.915 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 20:45:57.915 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-03 20:45:57.916 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-09-03 20:45:57.994 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831456722715680  [0;39m ------------- 开始 -------------
2025-09-03 20:45:57.994 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831456722715680  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 20:45:57.995 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831456722715680  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 20:45:57.996 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831456722715680  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:45:58.039 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831456722715680  [0;39m 请求参数: [{"username":"admin"}]
2025-09-03 20:45:58.091 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4831456722715680  [0;39m {dataSource-1} inited
2025-09-03 20:45:58.383 INFO  com.gec.wiki.service.impl.UserServiceImpl         :98   [32m4831456722715680  [0;39m 用户登录成功：admin
2025-09-03 20:45:58.388 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831456722715680  [0;39m 返回结果: {"content":{"userInfo":{"realName":"系统管理员","phone":"13800138000","id":1,"userType":3,"email":"<EMAIL>","username":"admin"},"token":"0dab214f355c44d8a58a2e6c13798e01"},"message":"登录成功","success":true}
2025-09-03 20:45:58.388 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831456722715680  [0;39m ------------- 结束 耗时：395 ms -------------
2025-09-03 20:49:56.594 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-03 20:49:56.599 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-03 20:50:30.062 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 27872 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 20:50:30.064 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 20:50:31.166 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 20:50:31.173 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 20:50:31.173 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 20:50:31.173 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 20:50:31.253 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 20:50:31.253 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1154 ms
2025-09-03 20:50:32.740 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 20:50:32.941 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 20:50:32.956 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 20:50:32.964 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.204 seconds (JVM running for 4.186)
2025-09-03 20:50:32.966 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 20:50:32.967 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 20:50:51.124 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 20:50:51.124 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-03 20:50:51.125 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-09-03 20:50:51.182 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831466329900064  [0;39m ------------- 开始 -------------
2025-09-03 20:50:51.182 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831466329900065  [0;39m ------------- 开始 -------------
2025-09-03 20:50:51.183 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831466329900065  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:50:51.183 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831466329900064  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:50:51.184 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831466329900065  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:50:51.184 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831466329900064  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:50:51.185 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831466329900064  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:50:51.184 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831466329900065  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:50:51.245 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831466329900064  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 20:50:51.245 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831466329900065  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 20:50:51.367 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4831466329900064  [0;39m {dataSource-1} inited
2025-09-03 20:50:51.635 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831466329900065  [0;39m 总行数：7
2025-09-03 20:50:51.635 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831466329900064  [0;39m 总行数：7
2025-09-03 20:50:51.636 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831466329900065  [0;39m 总页数：2
2025-09-03 20:50:51.636 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831466329900064  [0;39m 总页数：1
2025-09-03 20:50:51.650 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831466329900065  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:50:51.650 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831466329900064  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:50:51.651 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831466329900065  [0;39m ------------- 结束 耗时：470 ms -------------
2025-09-03 20:50:51.651 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831466329900064  [0;39m ------------- 结束 耗时：470 ms -------------
2025-09-03 20:51:00.340 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831466630022176  [0;39m ------------- 开始 -------------
2025-09-03 20:51:00.341 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831466630022176  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 20:51:00.341 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831466630022176  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 20:51:00.341 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831466630022176  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:51:00.343 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831466630022176  [0;39m 请求参数: [{"username":"11"}]
2025-09-03 20:51:00.354 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831466630022176  [0;39m 返回结果: {"message":"用户不存在","success":false}
2025-09-03 20:51:00.354 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831466630022176  [0;39m ------------- 结束 耗时：14 ms -------------
2025-09-03 20:51:16.848 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831467170956320  [0;39m ------------- 开始 -------------
2025-09-03 20:51:16.849 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831467170956320  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 20:51:16.849 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831467170956320  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 20:51:16.849 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831467170956320  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:51:16.850 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831467170956320  [0;39m 请求参数: [{"username":"admin"}]
2025-09-03 20:51:16.865 INFO  com.gec.wiki.service.impl.UserServiceImpl         :98   [32m4831467170956320  [0;39m 用户登录成功：admin
2025-09-03 20:51:16.866 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831467170956320  [0;39m 返回结果: {"content":{"userInfo":{"realName":"系统管理员","phone":"13800138000","id":1,"userType":3,"email":"<EMAIL>","username":"admin"},"token":"69b2e62db65846fd9a0d1c7185d4fcae"},"message":"登录成功","success":true}
2025-09-03 20:51:16.867 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831467170956320  [0;39m ------------- 结束 耗时：19 ms -------------
2025-09-03 20:51:16.939 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831467173938208  [0;39m ------------- 开始 -------------
2025-09-03 20:51:16.939 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831467173938209  [0;39m ------------- 开始 -------------
2025-09-03 20:51:16.939 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831467173938209  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:51:16.939 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831467173938208  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:51:16.940 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831467173938209  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:51:16.940 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831467173938208  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:51:16.940 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831467173938209  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:51:16.940 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831467173938208  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:51:16.941 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831467173938209  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 20:51:16.941 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831467173938208  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 20:51:16.952 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831467173938208  [0;39m 总行数：7
2025-09-03 20:51:16.953 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831467173938209  [0;39m 总行数：7
2025-09-03 20:51:16.953 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831467173938208  [0;39m 总页数：2
2025-09-03 20:51:16.953 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831467173938209  [0;39m 总页数：1
2025-09-03 20:51:16.960 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831467173938208  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:51:16.960 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831467173938209  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:51:16.961 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831467173938208  [0;39m ------------- 结束 耗时：22 ms -------------
2025-09-03 20:51:16.961 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831467173938209  [0;39m ------------- 结束 耗时：22 ms -------------
2025-09-03 20:51:26.496 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831467487101984  [0;39m ------------- 开始 -------------
2025-09-03 20:51:26.497 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831467487101984  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:51:26.497 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831467487101984  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:51:26.497 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831467487101984  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:51:26.498 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831467487101984  [0;39m 请求参数: [{"page":1,"size":100}]
2025-09-03 20:51:26.506 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831467487101984  [0;39m 总行数：7
2025-09-03 20:51:26.506 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831467487101984  [0;39m 总页数：1
2025-09-03 20:51:26.507 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831467487101984  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:51:26.508 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831467487101984  [0;39m ------------- 结束 耗时：12 ms -------------
2025-09-03 20:51:37.008 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831467831559200  [0;39m ------------- 开始 -------------
2025-09-03 20:51:37.009 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831467831559200  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:51:37.009 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831467831591968  [0;39m ------------- 开始 -------------
2025-09-03 20:51:37.009 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831467831559200  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:51:37.009 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831467831591968  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:51:37.009 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831467831559200  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:51:37.009 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831467831591968  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:51:37.010 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831467831591968  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:51:37.010 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831467831559200  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 20:51:37.010 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831467831591968  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 20:51:37.020 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831467831591968  [0;39m 总行数：7
2025-09-03 20:51:37.020 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831467831559200  [0;39m 总行数：7
2025-09-03 20:51:37.020 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831467831591968  [0;39m 总页数：1
2025-09-03 20:51:37.020 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831467831559200  [0;39m 总页数：2
2025-09-03 20:51:37.021 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831467831559200  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:51:37.021 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831467831591968  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:51:37.021 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831467831559200  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-03 20:51:37.021 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831467831591968  [0;39m ------------- 结束 耗时：12 ms -------------
2025-09-03 20:52:55.997 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831470419870752  [0;39m ------------- 开始 -------------
2025-09-03 20:52:55.997 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831470419870752  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 20:52:55.997 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831470419870752  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 20:52:55.998 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831470419870752  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:52:55.998 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831470419870752  [0;39m 请求参数: [{"username":"admin"}]
2025-09-03 20:52:56.011 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4831470419870752  [0;39m discard long time none received connection. , jdbcUrl : ****************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 78984
2025-09-03 20:52:56.013 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4831470419870752  [0;39m discard long time none received connection. , jdbcUrl : ****************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 78996
2025-09-03 20:52:56.045 INFO  com.gec.wiki.service.impl.UserServiceImpl         :98   [32m4831470419870752  [0;39m 用户登录成功：admin
2025-09-03 20:52:56.045 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831470419870752  [0;39m 返回结果: {"content":{"userInfo":{"realName":"系统管理员","phone":"13800138000","id":1,"userType":3,"email":"<EMAIL>","username":"admin"},"token":"fbc6f493d6e64e7292422a537a07198d"},"message":"登录成功","success":true}
2025-09-03 20:52:56.046 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831470419870752  [0;39m ------------- 结束 耗时：49 ms -------------
2025-09-03 20:52:56.107 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831470423475232  [0;39m ------------- 开始 -------------
2025-09-03 20:52:56.107 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831470423475233  [0;39m ------------- 开始 -------------
2025-09-03 20:52:56.107 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831470423475233  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:52:56.107 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831470423475232  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:52:56.108 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831470423475232  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:52:56.108 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831470423475233  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:52:56.108 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831470423475232  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:52:56.108 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831470423475233  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:52:56.108 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831470423475233  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 20:52:56.108 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831470423475232  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 20:52:56.117 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831470423475232  [0;39m 总行数：7
2025-09-03 20:52:56.118 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831470423475232  [0;39m 总页数：2
2025-09-03 20:52:56.119 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831470423475232  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:52:56.119 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831470423475232  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-03 20:52:56.122 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831470423475233  [0;39m 总行数：7
2025-09-03 20:52:56.122 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831470423475233  [0;39m 总页数：1
2025-09-03 20:52:56.123 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831470423475233  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:52:56.124 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831470423475233  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-03 20:55:25.780 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831475327960096  [0;39m ------------- 开始 -------------
2025-09-03 20:55:25.781 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831475327960096  [0;39m 请求地址: http://localhost:8880/auth/register POST
2025-09-03 20:55:25.781 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831475327960096  [0;39m 类名方法: com.gec.wiki.controller.AuthController.register
2025-09-03 20:55:25.781 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831475327960096  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:55:25.783 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831475327960096  [0;39m 请求参数: [{"confirmPassword":"12345678","email":"","phone":"19978452689","realName":"罗献宝","username":"罗献宝"}]
2025-09-03 20:55:25.787 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4831475327960096  [0;39m discard long time none received connection. , jdbcUrl : ****************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 149647
2025-09-03 20:55:25.788 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4831475327960096  [0;39m discard long time none received connection. , jdbcUrl : ****************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 149668
2025-09-03 20:55:25.900 INFO  com.gec.wiki.service.impl.UserServiceImpl         :184  [32m4831475327960096  [0;39m 用户注册成功：罗献宝
2025-09-03 20:55:25.900 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831475327960096  [0;39m 返回结果: {"message":"注册成功","success":true}
2025-09-03 20:55:25.900 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831475327960096  [0;39m ------------- 结束 耗时：120 ms -------------
2025-09-03 20:55:39.450 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831475775898656  [0;39m ------------- 开始 -------------
2025-09-03 20:55:39.450 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831475775898656  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 20:55:39.451 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831475775898656  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 20:55:39.451 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831475775898656  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:55:39.451 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831475775898656  [0;39m 请求参数: [{"username":"罗献宝"}]
2025-09-03 20:55:39.526 INFO  com.gec.wiki.service.impl.UserServiceImpl         :98   [32m4831475775898656  [0;39m 用户登录成功：罗献宝
2025-09-03 20:55:39.526 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831475775898656  [0;39m 返回结果: {"content":{"userInfo":{"realName":"罗献宝","phone":"19978452689","id":3,"userType":1,"email":"","username":"罗献宝"},"token":"79a5d3b56e4e49219fb136dbd62f0be1"},"message":"登录成功","success":true}
2025-09-03 20:55:39.526 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831475775898656  [0;39m ------------- 结束 耗时：76 ms -------------
2025-09-03 20:55:39.586 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831475780322336  [0;39m ------------- 开始 -------------
2025-09-03 20:55:39.586 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831475780322336  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:55:39.587 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831475780322336  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:55:39.587 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831475780387872  [0;39m ------------- 开始 -------------
2025-09-03 20:55:39.587 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831475780322336  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:55:39.587 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831475780387872  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 20:55:39.588 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831475780387872  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 20:55:39.588 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831475780322336  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 20:55:39.588 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831475780387872  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 20:55:39.589 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831475780387872  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 20:55:39.597 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831475780322336  [0;39m 总行数：7
2025-09-03 20:55:39.598 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831475780322336  [0;39m 总页数：1
2025-09-03 20:55:39.598 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831475780322336  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:55:39.598 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831475780322336  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-03 20:55:39.601 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831475780387872  [0;39m 总行数：7
2025-09-03 20:55:39.602 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831475780387872  [0;39m 总页数：2
2025-09-03 20:55:39.603 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831475780387872  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 20:55:39.603 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831475780387872  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-03 21:17:31.315 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 14712 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 21:17:31.317 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 21:17:32.225 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 21:17:32.231 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 21:17:32.232 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 21:17:32.232 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 21:17:32.396 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 21:17:32.397 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1048 ms
2025-09-03 21:17:34.054 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 21:17:34.240 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 21:17:34.255 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 21:17:34.263 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.367 seconds (JVM running for 4.296)
2025-09-03 21:17:34.265 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 21:17:34.265 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 21:18:32.498 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-03 21:19:49.250 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 25356 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 21:19:49.252 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 21:19:50.325 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 21:19:50.333 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 21:19:50.333 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 21:19:50.334 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 21:19:50.419 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 21:19:50.420 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1130 ms
2025-09-03 21:19:51.948 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 21:19:52.147 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 21:19:52.165 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 21:19:52.174 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.25 seconds (JVM running for 4.303)
2025-09-03 21:19:52.176 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 21:19:52.176 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 21:20:09.834 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 21:20:09.835 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-03 21:20:09.836 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-09-03 21:20:09.906 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831523959768097  [0;39m ------------- 开始 -------------
2025-09-03 21:20:09.906 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831523959768096  [0;39m ------------- 开始 -------------
2025-09-03 21:20:09.907 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831523959768097  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:20:09.907 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831523959768096  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:20:09.907 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831523959768097  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:20:09.907 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831523959768096  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:20:09.908 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831523959768096  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:20:09.908 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831523959768097  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:20:10.021 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831523959768096  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 21:20:10.021 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831523959768097  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 21:20:10.184 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4831523959768097  [0;39m {dataSource-1} inited
2025-09-03 21:20:10.496 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831523959768096  [0;39m 总行数：7
2025-09-03 21:20:10.496 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831523959768097  [0;39m 总行数：7
2025-09-03 21:20:10.496 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831523959768096  [0;39m 总页数：2
2025-09-03 21:20:10.496 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831523959768097  [0;39m 总页数：1
2025-09-03 21:20:10.513 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831523959768096  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:20:10.513 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831523959768097  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:20:10.513 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831523959768097  [0;39m ------------- 结束 耗时：608 ms -------------
2025-09-03 21:20:10.514 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831523959768096  [0;39m ------------- 结束 耗时：608 ms -------------
2025-09-03 21:20:45.250 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831525117953056  [0;39m ------------- 开始 -------------
2025-09-03 21:20:45.250 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831525117953056  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:20:45.251 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831525117953056  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:20:45.251 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831525117953056  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:20:45.251 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831525117953056  [0;39m 请求参数: [{"page":1,"size":100}]
2025-09-03 21:20:45.262 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831525117953056  [0;39m 总行数：7
2025-09-03 21:20:45.263 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831525117953056  [0;39m 总页数：1
2025-09-03 21:20:45.267 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831525117953056  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:20:45.267 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831525117953056  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-03 21:20:58.748 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831525560255520  [0;39m ------------- 开始 -------------
2025-09-03 21:20:58.749 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831525560255520  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:20:58.749 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831525560255520  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:20:58.749 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831525560255520  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:20:58.749 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831525560288288  [0;39m ------------- 开始 -------------
2025-09-03 21:20:58.750 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831525560255520  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 21:20:58.750 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831525560288288  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:20:58.750 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831525560288288  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:20:58.750 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831525560288288  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:20:58.751 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831525560288288  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 21:20:58.759 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831525560255520  [0;39m 总行数：7
2025-09-03 21:20:58.759 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831525560255520  [0;39m 总页数：2
2025-09-03 21:20:58.759 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831525560288288  [0;39m 总行数：7
2025-09-03 21:20:58.760 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831525560288288  [0;39m 总页数：1
2025-09-03 21:20:58.760 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831525560255520  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:20:58.761 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831525560255520  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-03 21:20:58.761 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831525560288288  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:20:58.761 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831525560288288  [0;39m ------------- 结束 耗时：12 ms -------------
2025-09-03 21:25:38.469 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-03 21:25:38.474 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-03 21:25:43.923 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 1444 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 21:25:43.927 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 21:25:45.067 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 21:25:45.073 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 21:25:45.074 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 21:25:45.074 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 21:25:45.150 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 21:25:45.151 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1169 ms
2025-09-03 21:25:46.871 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 21:25:47.071 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 21:25:47.092 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 21:25:47.101 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.621 seconds (JVM running for 4.803)
2025-09-03 21:25:47.104 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 21:25:47.105 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 21:26:04.602 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 21:26:04.602 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-03 21:26:04.603 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-03 21:26:04.658 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831535584281632  [0;39m ------------- 开始 -------------
2025-09-03 21:26:04.658 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831535584281633  [0;39m ------------- 开始 -------------
2025-09-03 21:26:04.659 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831535584281632  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:26:04.659 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831535584281633  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:26:04.660 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831535584281632  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:26:04.660 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831535584281633  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:26:04.660 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831535584281632  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:26:04.660 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831535584281633  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:26:04.715 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831535584281632  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 21:26:04.715 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831535584281633  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 21:26:04.844 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4831535584281632  [0;39m {dataSource-1} inited
2025-09-03 21:26:05.112 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831535584281632  [0;39m 总行数：7
2025-09-03 21:26:05.113 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831535584281632  [0;39m 总页数：2
2025-09-03 21:26:05.113 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831535584281633  [0;39m 总行数：7
2025-09-03 21:26:05.113 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831535584281633  [0;39m 总页数：1
2025-09-03 21:26:05.126 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831535584281632  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:26:05.126 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831535584281633  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:26:05.126 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831535584281632  [0;39m ------------- 结束 耗时：469 ms -------------
2025-09-03 21:26:05.126 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831535584281633  [0;39m ------------- 结束 耗时：469 ms -------------
2025-09-03 21:26:23.855 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831536213361696  [0;39m ------------- 开始 -------------
2025-09-03 21:26:23.856 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831536213361696  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 21:26:23.856 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831536213361696  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 21:26:23.857 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831536213361696  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:26:23.858 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831536213361696  [0;39m 请求参数: [{"username":"admin"}]
2025-09-03 21:26:23.872 INFO  com.gec.wiki.service.impl.UserServiceImpl         :98   [32m4831536213361696  [0;39m 用户登录成功：admin
2025-09-03 21:26:23.873 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831536213361696  [0;39m 返回结果: {"content":{"userInfo":{"realName":"系统管理员","phone":"13800138000","id":1,"userType":3,"email":"<EMAIL>","username":"admin"},"token":"9fffe86041ea4269b4811698b4b93c16"},"message":"登录成功","success":true}
2025-09-03 21:26:23.874 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831536213361696  [0;39m ------------- 结束 耗时：19 ms -------------
2025-09-03 21:27:39.911 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831538705564704  [0;39m ------------- 开始 -------------
2025-09-03 21:27:39.913 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831538705564704  [0;39m 请求地址: http://localhost:8880/auth/register POST
2025-09-03 21:27:39.913 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831538705564704  [0;39m 类名方法: com.gec.wiki.controller.AuthController.register
2025-09-03 21:27:39.914 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831538705564704  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:27:39.914 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831538705564704  [0;39m 请求参数: [{"confirmPassword":"12345678","email":"","phone":"19978452688","realName":"wei","userType":2,"username":"111"}]
2025-09-03 21:27:39.927 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4831538705564704  [0;39m discard long time none received connection. , jdbcUrl : ****************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 76047
2025-09-03 21:27:39.929 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4831538705564704  [0;39m discard long time none received connection. , jdbcUrl : ****************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 94820
2025-09-03 21:27:40.042 INFO  com.gec.wiki.service.impl.UserServiceImpl         :192  [32m4831538705564704  [0;39m 用户注册成功：111
2025-09-03 21:27:40.043 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831538705564704  [0;39m 返回结果: {"message":"注册成功","success":true}
2025-09-03 21:27:40.043 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831538705564704  [0;39m ------------- 结束 耗时：132 ms -------------
2025-09-03 21:27:50.513 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831539052971040  [0;39m ------------- 开始 -------------
2025-09-03 21:27:50.513 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831539052971040  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 21:27:50.513 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831539052971040  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 21:27:50.514 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831539052971040  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:27:50.514 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831539052971040  [0;39m 请求参数: [{"username":"11"}]
2025-09-03 21:27:50.519 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831539052971040  [0;39m 返回结果: {"message":"用户不存在","success":false}
2025-09-03 21:27:50.519 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831539052971040  [0;39m ------------- 结束 耗时：6 ms -------------
2025-09-03 21:27:54.170 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831539172803616  [0;39m ------------- 开始 -------------
2025-09-03 21:27:54.170 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831539172803616  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 21:27:54.170 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831539172803616  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 21:27:54.171 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831539172803616  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:27:54.172 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831539172803616  [0;39m 请求参数: [{"username":"111"}]
2025-09-03 21:27:54.244 INFO  com.gec.wiki.service.impl.UserServiceImpl         :98   [32m4831539172803616  [0;39m 用户登录成功：111
2025-09-03 21:27:54.245 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831539172803616  [0;39m 返回结果: {"content":{"userInfo":{"realName":"wei","phone":"19978452688","id":4,"userType":2,"email":"","username":"111"},"token":"a264a8c47ea946619d0b5d2b8aed4169"},"message":"登录成功","success":true}
2025-09-03 21:27:54.246 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831539172803616  [0;39m ------------- 结束 耗时：75 ms -------------
2025-09-03 21:28:03.236 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831539469878304  [0;39m ------------- 开始 -------------
2025-09-03 21:28:03.237 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831539469878304  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:28:03.237 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831539469878304  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:28:03.237 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831539469911072  [0;39m ------------- 开始 -------------
2025-09-03 21:28:03.238 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831539469878304  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:28:03.238 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831539469911072  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:28:03.238 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831539469911072  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:28:03.238 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831539469878304  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 21:28:03.238 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831539469911072  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:28:03.239 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831539469911072  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 21:28:03.250 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831539469878304  [0;39m 总行数：7
2025-09-03 21:28:03.251 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831539469878304  [0;39m 总页数：2
2025-09-03 21:28:03.256 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831539469878304  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:28:03.256 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831539469878304  [0;39m ------------- 结束 耗时：20 ms -------------
2025-09-03 21:28:03.257 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831539469911072  [0;39m 总行数：7
2025-09-03 21:28:03.258 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831539469911072  [0;39m 总页数：1
2025-09-03 21:28:03.259 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831539469911072  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:28:03.260 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831539469911072  [0;39m ------------- 结束 耗时：23 ms -------------
2025-09-03 21:28:05.667 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831539549537312  [0;39m ------------- 开始 -------------
2025-09-03 21:28:05.667 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831539549537312  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:28:05.668 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831539549537312  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:28:05.668 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831539549537312  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:28:05.668 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831539549537312  [0;39m 请求参数: [{"page":1,"size":8}]
2025-09-03 21:28:05.680 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831539549537312  [0;39m 总行数：7
2025-09-03 21:28:05.680 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831539549537312  [0;39m 总页数：1
2025-09-03 21:28:05.681 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831539549537312  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:28:05.682 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831539549537312  [0;39m ------------- 结束 耗时：15 ms -------------
2025-09-03 21:28:33.167 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831540450657312  [0;39m ------------- 开始 -------------
2025-09-03 21:28:33.167 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831540450657313  [0;39m ------------- 开始 -------------
2025-09-03 21:28:33.167 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831540450657312  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:28:33.168 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831540450657312  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:28:33.168 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831540450657313  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:28:33.168 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831540450657313  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:28:33.168 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831540450657312  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:28:33.168 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831540450657313  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:28:33.169 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831540450657312  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 21:28:33.169 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831540450657313  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 21:28:33.178 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831540450657313  [0;39m 总行数：7
2025-09-03 21:28:33.178 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831540450657313  [0;39m 总页数：1
2025-09-03 21:28:33.179 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831540450657312  [0;39m 总行数：7
2025-09-03 21:28:33.179 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831540450657312  [0;39m 总页数：2
2025-09-03 21:28:33.179 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831540450657313  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:28:33.180 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831540450657313  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-03 21:28:33.180 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831540450657312  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:28:33.180 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831540450657312  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-03 21:28:50.362 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831541014103072  [0;39m ------------- 开始 -------------
2025-09-03 21:28:50.363 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831541014103072  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:28:50.363 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831541014103072  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:28:50.363 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831541014103072  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:28:50.363 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831541014103072  [0;39m 请求参数: [{"page":1,"size":8}]
2025-09-03 21:28:50.370 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831541014103072  [0;39m 总行数：7
2025-09-03 21:28:50.370 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831541014103072  [0;39m 总页数：1
2025-09-03 21:28:50.371 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831541014103072  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:28:50.372 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831541014103072  [0;39m ------------- 结束 耗时：10 ms -------------
2025-09-03 21:29:19.580 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831541971518496  [0;39m ------------- 开始 -------------
2025-09-03 21:29:19.581 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831541971518496  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 21:29:19.582 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831541971518496  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 21:29:19.583 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831541971518496  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:29:19.583 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831541971518496  [0;39m 请求参数: [{"username":"111"}]
2025-09-03 21:29:19.657 INFO  com.gec.wiki.service.impl.UserServiceImpl         :98   [32m4831541971518496  [0;39m 用户登录成功：111
2025-09-03 21:29:19.658 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831541971518496  [0;39m 返回结果: {"content":{"userInfo":{"realName":"wei","phone":"19978452688","id":4,"userType":2,"email":"","username":"111"},"token":"d646d709af1b4081a85a13d3b92ed491"},"message":"登录成功","success":true}
2025-09-03 21:29:19.658 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831541971518496  [0;39m ------------- 结束 耗时：78 ms -------------
2025-09-03 21:31:27.372 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-03 21:31:27.375 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-03 21:53:58.736 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 3216 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-03 21:53:58.739 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-03 21:53:59.785 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-03 21:53:59.792 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-03 21:53:59.792 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-03 21:53:59.792 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-03 21:53:59.866 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-03 21:53:59.866 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1085 ms
2025-09-03 21:54:01.535 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-03 21:54:01.753 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-03 21:54:01.771 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-03 21:54:01.779 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.533 seconds (JVM running for 4.774)
2025-09-03 21:54:01.781 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-03 21:54:01.782 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-03 21:54:21.758 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 21:54:21.758 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-03 21:54:21.760 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-09-03 21:54:21.812 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831591196623904  [0;39m ------------- 开始 -------------
2025-09-03 21:54:21.812 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831591196623905  [0;39m ------------- 开始 -------------
2025-09-03 21:54:21.813 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831591196623904  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:54:21.813 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831591196623905  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:54:21.813 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831591196623904  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:54:21.813 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831591196623905  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:54:21.813 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831591196623904  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:54:21.813 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831591196623905  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:54:21.863 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831591196623905  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 21:54:21.863 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831591196623904  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 21:54:21.982 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4831591196623905  [0;39m {dataSource-1} inited
2025-09-03 21:54:22.252 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831591196623905  [0;39m 总行数：7
2025-09-03 21:54:22.252 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831591196623904  [0;39m 总行数：7
2025-09-03 21:54:22.253 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831591196623905  [0;39m 总页数：2
2025-09-03 21:54:22.253 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831591196623904  [0;39m 总页数：1
2025-09-03 21:54:22.267 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831591196623905  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:54:22.267 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831591196623904  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:54:22.268 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831591196623904  [0;39m ------------- 结束 耗时：457 ms -------------
2025-09-03 21:54:22.268 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831591196623905  [0;39m ------------- 结束 耗时：457 ms -------------
2025-09-03 21:54:25.961 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831591332611104  [0;39m ------------- 开始 -------------
2025-09-03 21:54:25.961 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831591332611105  [0;39m ------------- 开始 -------------
2025-09-03 21:54:25.961 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831591332611104  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:54:25.961 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831591332611105  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:54:25.962 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831591332611104  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:54:25.962 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831591332611105  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:54:25.962 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831591332611104  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:54:25.962 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831591332611105  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:54:25.962 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831591332611104  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 21:54:25.962 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831591332611105  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 21:54:25.975 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831591332611105  [0;39m 总行数：7
2025-09-03 21:54:25.975 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831591332611104  [0;39m 总行数：7
2025-09-03 21:54:25.975 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831591332611105  [0;39m 总页数：2
2025-09-03 21:54:25.975 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831591332611104  [0;39m 总页数：1
2025-09-03 21:54:25.981 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831591332611105  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:54:25.981 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831591332611104  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:54:25.982 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831591332611105  [0;39m ------------- 结束 耗时：21 ms -------------
2025-09-03 21:54:25.982 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831591332611104  [0;39m ------------- 结束 耗时：21 ms -------------
2025-09-03 21:55:27.875 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831593361409056  [0;39m ------------- 开始 -------------
2025-09-03 21:55:27.875 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831593361409056  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-03 21:55:27.876 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831593361409056  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-03 21:55:27.876 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831593361409056  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:55:27.877 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831593361409056  [0;39m 请求参数: [{"username":"罗献宝"}]
2025-09-03 21:55:27.894 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4831593361409056  [0;39m discard long time none received connection. , jdbcUrl : ****************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 61914
2025-09-03 21:55:27.896 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4831593361409056  [0;39m discard long time none received connection. , jdbcUrl : ****************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 61925
2025-09-03 21:55:28.005 INFO  com.gec.wiki.service.impl.UserServiceImpl         :98   [32m4831593361409056  [0;39m 用户登录成功：罗献宝
2025-09-03 21:55:28.006 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831593361409056  [0;39m 返回结果: {"content":{"userInfo":{"realName":"罗献宝","phone":"19978452689","id":3,"userType":1,"email":"","username":"罗献宝"},"token":"e7531679d2f64f19ba200a3720fc9e68"},"message":"登录成功","success":true}
2025-09-03 21:55:28.007 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831593361409056  [0;39m ------------- 结束 耗时：132 ms -------------
2025-09-03 21:55:28.077 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831593368028192  [0;39m ------------- 开始 -------------
2025-09-03 21:55:28.077 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831593368028192  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:55:28.078 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831593368028192  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:55:28.078 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831593368028192  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:55:28.079 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831593368028192  [0;39m 请求参数: [{"page":1,"size":4}]
2025-09-03 21:55:28.090 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831593368028192  [0;39m 总行数：7
2025-09-03 21:55:28.091 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831593368028192  [0;39m 总页数：2
2025-09-03 21:55:28.092 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831593368028192  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:55:28.092 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831593368028192  [0;39m ------------- 结束 耗时：15 ms -------------
2025-09-03 21:55:33.224 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831593536685088  [0;39m ------------- 开始 -------------
2025-09-03 21:55:33.225 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831593536685088  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:55:33.225 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831593536685088  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:55:33.225 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831593536685088  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:55:33.226 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831593536685088  [0;39m 请求参数: [{"page":1,"size":100}]
2025-09-03 21:55:33.234 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831593536685088  [0;39m 总行数：7
2025-09-03 21:55:33.234 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831593536685088  [0;39m 总页数：1
2025-09-03 21:55:33.235 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831593536685088  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:55:33.236 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831593536685088  [0;39m ------------- 结束 耗时：12 ms -------------
2025-09-03 21:56:12.240 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831594815161376  [0;39m ------------- 开始 -------------
2025-09-03 21:56:12.241 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831594815161376  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:56:12.241 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831594815161376  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:56:12.241 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831594815161376  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:56:12.242 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831594815161376  [0;39m 请求参数: [{"page":1,"size":8}]
2025-09-03 21:56:12.252 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831594815161376  [0;39m 总行数：7
2025-09-03 21:56:12.252 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831594815161376  [0;39m 总页数：1
2025-09-03 21:56:12.253 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831594815161376  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:56:12.254 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831594815161376  [0;39m ------------- 结束 耗时：14 ms -------------
2025-09-03 21:56:19.030 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831595037656096  [0;39m ------------- 开始 -------------
2025-09-03 21:56:19.034 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831595037656096  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:56:19.036 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831595037656096  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:56:19.039 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831595037656096  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:56:19.040 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831595037656096  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-03 21:56:19.039 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4831595037951008  [0;39m ------------- 开始 -------------
2025-09-03 21:56:19.041 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4831595037951008  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-03 21:56:19.041 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4831595037951008  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-03 21:56:19.042 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4831595037951008  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-03 21:56:19.042 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4831595037951008  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-03 21:56:19.062 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831595037656096  [0;39m 总行数：7
2025-09-03 21:56:19.063 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831595037656096  [0;39m 总页数：2
2025-09-03 21:56:19.066 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831595037656096  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:56:19.066 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831595037656096  [0;39m ------------- 结束 耗时：36 ms -------------
2025-09-03 21:56:19.076 INFO  com.gec.wiki.service.ServiceService               :72   [32m4831595037951008  [0;39m 总行数：7
2025-09-03 21:56:19.077 INFO  com.gec.wiki.service.ServiceService               :73   [32m4831595037951008  [0;39m 总页数：1
2025-09-03 21:56:19.079 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4831595037951008  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":100,"category2Id":101,"completeCount":0,"description":"更换发动机机油，保证发动机正常运行","duration":30,"id":1,"name":"机油更换","price":150.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":102,"completeCount":0,"description":"更换磨损轮胎，确保行车安全","duration":45,"id":2,"name":"轮胎更换","price":400.00,"ratingCount":0},{"bookingCount":0,"category1Id":100,"category2Id":103,"completeCount":0,"description":"更换磨损刹车片，保证制动效果","duration":60,"id":3,"name":"刹车片更换","price":300.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":201,"completeCount":0,"description":"发动机全面检修，解决动力不足问题","duration":480,"id":4,"name":"发动机大修","price":2500.00,"ratingCount":0},{"bookingCount":0,"category1Id":200,"category2Id":202,"completeCount":0,"description":"清洗水箱，保证发动机正常冷却","duration":40,"id":5,"name":"水箱清洗","price":120.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":301,"completeCount":0,"description":"检测电瓶性能，预防启动故障","duration":20,"id":6,"name":"电瓶检测","price":50.00,"ratingCount":0},{"bookingCount":0,"category1Id":300,"category2Id":302,"completeCount":0,"description":"清洗空调系统，改善车内空气质量","duration":50,"id":7,"name":"空调清洗","price":180.00,"ratingCount":0}],"total":7},"success":true}
2025-09-03 21:56:19.080 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4831595037951008  [0;39m ------------- 结束 耗时：41 ms -------------
2025-09-03 21:56:57.917 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-03 21:56:57.920 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
