<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gec.wiki.mapper.BookingMapper">

    <resultMap id="BaseResultMap" type="com.gec.wiki.pojo.Booking">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="booking_no" jdbcType="VARCHAR" property="bookingNo" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
        <result column="service_id" jdbcType="BIGINT" property="serviceId" />
        <result column="technician_id" jdbcType="BIGINT" property="technicianId" />
        <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
        <result column="service_price" jdbcType="DECIMAL" property="servicePrice" />
        <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
        <result column="booking_date" jdbcType="DATE" property="bookingDate" />
        <result column="booking_time" jdbcType="TIME" property="bookingTime" />
        <result column="estimated_duration" jdbcType="INTEGER" property="estimatedDuration" />
        <result column="problem_description" jdbcType="LONGVARCHAR" property="problemDescription" />
        <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, booking_no, user_id, vehicle_id, service_id, technician_id, service_name, 
        service_price, contact_name, contact_phone, booking_date, booking_time, 
        estimated_duration, problem_description, remark, status, create_time, update_time
    </sql>
    
    <sql id="Base_Column_List_With_Alias">
        b.id, b.booking_no, b.user_id, b.vehicle_id, b.service_id, b.technician_id, b.service_name, 
        b.service_price, b.contact_name, b.contact_phone, b.booking_date, b.booking_time, 
        b.estimated_duration, b.problem_description, b.remark, b.status, b.create_time, b.update_time
    </sql>

    <select id="findByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM booking
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
    </select>

    <select id="findBookedTimesByDateAndTechnician" resultType="java.time.LocalTime">
        SELECT booking_time
        FROM booking
        WHERE booking_date = #{date} 
          AND technician_id = #{technicianId}
          AND status IN (1, 2, 3)
    </select>

    <select id="findBookedTimesByDate" resultType="java.time.LocalTime">
        SELECT booking_time
        FROM booking
        WHERE booking_date = #{date}
          AND status IN (1, 2, 3)
    </select>

    <select id="checkTimeAvailable" resultType="int">
        SELECT COUNT(*)
        FROM booking
        WHERE booking_date = #{date}
          AND booking_time = #{time}
          AND technician_id = #{technicianId}
          AND status IN (1, 2, 3)
    </select>

    <!-- 获取维修店今日预约列表 -->
    <select id="findTodayBookingsByShopId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List_With_Alias" />
        FROM booking b
        INNER JOIN service s ON b.service_id = s.id
        WHERE s.shop_id = #{shopId}
          AND b.booking_date = #{today}
          AND b.status != 5
        ORDER BY b.booking_time ASC
    </select>

    <!-- 获取维修店预约统计 -->
    <select id="getBookingStatsByShopId" resultType="map">
        SELECT
            SUM(CASE WHEN b.status = 1 THEN 1 ELSE 0 END) AS pendingOrders,
            SUM(CASE WHEN b.status IN (2, 3) THEN 1 ELSE 0 END) AS processingOrders,
            SUM(CASE WHEN b.status = 4 THEN 1 ELSE 0 END) AS completedOrders,
            COALESCE(SUM(CASE
                WHEN b.status = 4
                AND b.booking_date &gt;= DATE_FORMAT(NOW(), '%Y-%m-01')
                AND b.booking_date &lt; DATE_ADD(DATE_FORMAT(NOW(), '%Y-%m-01'), INTERVAL 1 MONTH)
                THEN b.service_price
                ELSE 0
            END), 0) AS monthlyRevenue
        FROM booking b
        INNER JOIN service s ON b.service_id = s.id
        WHERE s.shop_id = #{shopId}
    </select>

    <!-- 查询维修店的预约列表（分页） -->
    <select id="findBookingsForShop" resultType="map">
        SELECT 
            b.id,
            b.booking_no,
            b.user_id,
            b.vehicle_id,
            b.service_id,
            b.technician_id,
            b.service_name,
            b.service_price,
            b.contact_name,
            b.contact_phone,
            b.booking_date,
            b.booking_time,
            b.estimated_duration,
            b.problem_description,
            b.remark,
            b.status,
            b.create_time,
            b.update_time,
            v.license_plate,
            v.brand,
            v.model,
            v.color,
            v.year
        FROM booking b
        INNER JOIN service s ON b.service_id = s.id
        LEFT JOIN vehicle v ON b.vehicle_id = v.id
        WHERE s.shop_id = #{shopId}
        <if test="status != null">
            AND b.status = #{status}
        </if>
        ORDER BY b.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计维修店的预约总数 -->
    <select id="countBookingsForShop" resultType="int">
        SELECT COUNT(*)
        FROM booking b
        INNER JOIN service s ON b.service_id = s.id
        WHERE s.shop_id = #{shopId}
        <if test="status != null">
            AND b.status = #{status}
        </if>
    </select>

</mapper>
