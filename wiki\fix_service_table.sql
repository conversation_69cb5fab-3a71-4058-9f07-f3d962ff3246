-- 修复service表，添加shop_id字段
-- 请在数据库管理工具中执行以下SQL语句

USE car_service;

-- 添加shop_id字段
ALTER TABLE service 
ADD COLUMN shop_id BIGINT NULL COMMENT '维修店ID(空表示平台服务)' 
AFTER status;

-- 为shop_id字段添加索引以提高查询性能
CREATE INDEX idx_service_shop_id ON service(shop_id);

-- 验证字段已添加
DESCRIBE service;

-- 可选：为现有数据设置默认的shop_id（根据需要修改）
-- UPDATE service SET shop_id = 1 WHERE shop_id IS NULL;

SELECT 'shop_id字段添加成功！' AS message;
