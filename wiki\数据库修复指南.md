# 数据库表结构修复指南

## 问题描述

您的数据库中的`booking`表缺少`booking_time`字段，导致预约功能无法正常工作。

**错误信息**：
```
Unknown column 'booking_time' in 'where clause'
```

## 解决方案

我为您准备了三种修复方案，请根据您的情况选择：

### 方案一：快速修复（推荐）

使用 `quick_fix_booking.sql` 脚本：

1. **备份数据**（重要！）
   ```sql
   mysqldump -u root -p car_service booking > booking_backup.sql
   ```

2. **执行修复脚本**
   ```bash
   mysql -u root -p car_service < quick_fix_booking.sql
   ```

### 方案二：安全修复

使用 `fix_booking_table_safe.sql` 脚本，它会检查字段是否存在再添加：

```bash
mysql -u root -p car_service < fix_booking_table_safe.sql
```

### 方案三：重新创建数据库

如果上述方法都不行，使用完整的数据库脚本重新创建：

1. **备份所有数据**
   ```bash
   mysqldump -u root -p car_service > full_backup.sql
   ```

2. **使用v2版本的数据库脚本**
   ```bash
   mysql -u root -p < src/main/resources/sql/car_service_v2.sql
   ```

## 验证修复

修复后，请检查表结构：

```sql
USE car_service;
DESCRIBE booking;
```

您应该看到以下字段：
- `id`
- `booking_no`
- `user_id`
- `vehicle_id`
- `service_id`
- `technician_id`
- `service_name`
- `service_price`
- `contact_name`
- `contact_phone`
- `booking_date`
- `booking_time` ← 这个字段必须存在
- `estimated_duration`
- `problem_description`
- `remark`
- `status`
- `create_time`
- `update_time`

## 测试预约功能

修复后，请：

1. 重启您的Spring Boot应用
2. 尝试在前端提交预约
3. 检查是否还有错误

## 常见问题

**Q: 如果执行脚本时出现"字段已存在"错误怎么办？**
A: 这是正常的，说明某些字段已经存在。您可以忽略这些错误，或使用方案二的安全脚本。

**Q: 如何确认修复是否成功？**
A: 执行 `DESCRIBE booking;` 命令，确保 `booking_time` 字段存在且类型为 `TIME`。

**Q: 会丢失数据吗？**
A: 方案一和二不会丢失数据，只是添加缺失的字段。方案三需要先备份数据。
