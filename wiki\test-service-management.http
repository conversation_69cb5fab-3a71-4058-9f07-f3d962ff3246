### 服务管理功能测试

### 1. 分页查询服务列表
GET http://localhost:8880/service/getServiceListByPage?page=1&size=10
Accept: application/json

### 2. 根据条件查询服务列表
GET http://localhost:8880/service/getServiceListByPage?page=1&size=10&name=机油&category2Id=101&status=1
Accept: application/json

### 3. 添加新服务
POST http://localhost:8880/service/save
Content-Type: application/json

{
  "name": "机油更换服务",
  "category2Id": 101,
  "description": "更换全合成机油，延长发动机寿命",
  "price": 150.00,
  "duration": 60,
  "status": 1
}

### 4. 更新服务信息
POST http://localhost:8880/service/save
Content-Type: application/json

{
  "id": 1,
  "name": "高级机油更换服务",
  "category2Id": 101,
  "description": "更换高品质全合成机油，延长发动机寿命",
  "price": 180.00,
  "duration": 60,
  "status": 1
}

### 5. 根据ID获取服务详情
GET http://localhost:8880/service/getServiceById?id=1
Accept: application/json

### 6. 批量更新服务状态
POST http://localhost:8880/service/batchUpdateStatus
Content-Type: application/json

{
  "ids": [1, 2],
  "status": 0
}

### 7. 批量删除服务
POST http://localhost:8880/service/batchDelete
Content-Type: application/json

{
  "ids": [3, 4]
}

### 8. 删除单个服务
GET http://localhost:8880/service/remove?id=5
Accept: application/json

### 9. 获取所有服务列表
GET http://localhost:8880/service/getAllServiceList
Accept: application/json

### 10. 获取分类列表（用于下拉选择）
GET http://localhost:8880/category/allList
Accept: application/json
