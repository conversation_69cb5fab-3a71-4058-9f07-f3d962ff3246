-- 维修店服务管理测试数据
-- 注意：执行前请先运行 add_shop_id_to_service_table.sql 添加 shop_id 字段

-- 清理现有服务数据（可选，谨慎使用）
-- DELETE FROM service WHERE shop_id IS NOT NULL;

-- 插入维修店1的服务数据
INSERT INTO service (id, name, category1_id, category2_id, description, content, cover, price, original_price, duration, is_recommend, status, shop_id, create_time, update_time) VALUES 
-- 引擎维修服务
(1001, '发动机全面检修', 1, 101, '专业发动机检修，解决各种动力问题', '包含发动机拆解检查、活塞环更换、气门调整、正时链条检查等全套服务。使用原厂配件，提供6个月质保。', '/image/engine-repair.jpg', 1200.00, 1500.00, 180, 1, 1, 1, NOW(), NOW()),
(1002, '机油更换保养', 1, 102, '使用优质机油，延长发动机寿命', '更换优质机油和机滤，检查发动机运行状态，清洁发动机舱。推荐每5000公里更换一次。', '/image/oil-change.jpg', 180.00, 220.00, 30, 1, 1, 1, NOW(), NOW()),
(1003, '火花塞更换', 1, 103, '提升点火效率，改善燃油经济性', '更换原厂或品牌火花塞，检查点火系统，调整点火正时。适用于各种车型。', NULL, 120.00, 150.00, 45, 0, 1, 1, NOW(), NOW()),

-- 制动系统服务
(1004, '刹车片更换', 2, 201, '安全第一，及时更换磨损刹车片', '检查刹车片磨损情况，更换高品质刹车片，调整刹车间隙，确保制动效果。', '/image/brake-pad.jpg', 280.00, 350.00, 60, 1, 1, 1, NOW(), NOW()),
(1005, '制动液更换', 2, 203, '保持制动系统良好状态', '更换DOT4制动液，排除系统空气，检查制动管路，确保制动安全。', NULL, 80.00, 100.00, 30, 0, 1, 1, NOW(), NOW()),

-- 轮胎服务
(1006, '轮胎更换套餐', 3, 301, '品牌轮胎，安全出行保障', '提供多品牌轮胎选择，专业安装，免费动平衡和四轮定位检查。', '/image/tire-service.jpg', 800.00, 1000.00, 90, 1, 1, 1, NOW(), NOW()),
(1007, '四轮定位', 3, 303, '纠正轮胎异常磨损，提升驾驶体验', '使用专业设备进行四轮定位调整，解决跑偏、轮胎偏磨等问题。', NULL, 150.00, 180.00, 60, 0, 1, 1, NOW(), NOW());

-- 插入维修店2的服务数据  
INSERT INTO service (id, name, category1_id, category2_id, description, content, cover, price, original_price, duration, is_recommend, status, shop_id, create_time, update_time) VALUES 
-- 电气系统服务
(2001, '汽车电瓶更换', 4, 401, '解决启动困难，电力不足问题', '检测电瓶状态，更换高性能免维护电瓶，清洁电极接头，检查发电系统。', '/image/battery-service.jpg', 450.00, 500.00, 45, 1, 1, 2, NOW(), NOW()),
(2002, '车灯升级改装', 4, 404, '提升夜间行车安全', '升级LED大灯、雾灯，调整灯光角度，符合交通法规要求。', '/image/light-upgrade.jpg', 600.00, 800.00, 120, 1, 1, 2, NOW(), NOW()),

-- 空调系统服务
(2003, '空调系统检修', 5, 501, '夏日清凉，冬日温暖', '检查空调制冷效果，清洁蒸发器，更换空调滤芯，添加制冷剂。', '/image/ac-service.jpg', 200.00, 250.00, 75, 1, 1, 2, NOW(), NOW()),
(2004, '空调滤芯更换', 5, 503, '改善车内空气质量', '更换高效空调滤芯，去除异味，净化车内空气，建议每年更换。', NULL, 60.00, 80.00, 20, 0, 1, 2, NOW(), NOW()),

-- 车身美容服务
(2005, '精致洗车服务', 7, 701, '呵护爱车，焕然一新', '包含外观清洗、内饰清洁、轮毂清洁、玻璃清洁等全方位清洁服务。', '/image/car-wash.jpg', 50.00, 80.00, 45, 1, 1, 2, NOW(), NOW()),
(2006, '车漆打蜡抛光', 7, 702, '保护车漆，恢复光泽', '深度清洁车漆，去除细小划痕，打蜡抛光，形成保护膜。', '/image/wax-polish.jpg', 300.00, 400.00, 120, 1, 1, 2, NOW(), NOW());

-- 更新服务统计数据（模拟一些预约和完成记录）
UPDATE service SET 
    booking_count = FLOOR(RAND() * 50) + 10,
    complete_count = FLOOR(RAND() * 40) + 5,
    rating_count = FLOOR(RAND() * 30) + 5,
    rating_score = ROUND(4.0 + RAND() * 1.0, 1)
WHERE shop_id IN (1, 2);

-- 查询验证数据
SELECT 
    s.id,
    s.name as 服务名称,
    c1.name as 一级分类,
    c2.name as 二级分类,
    s.price as 价格,
    s.duration as 时长,
    CASE WHEN s.status = 1 THEN '上架' ELSE '下架' END as 状态,
    s.shop_id as 维修店ID,
    s.booking_count as 预约次数,
    s.rating_score as 评分
FROM service s
LEFT JOIN category c1 ON s.category1_id = c1.id
LEFT JOIN category c2 ON s.category2_id = c2.id
WHERE s.shop_id IS NOT NULL
ORDER BY s.shop_id, s.id;
