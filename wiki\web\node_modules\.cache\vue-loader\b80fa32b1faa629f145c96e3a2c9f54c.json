{"remainingRequest": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\owner\\VehicleManagement.vue?vue&type=template&id=5d44c52e&scoped=true&ts=true", "dependencies": [{"path": "D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\owner\\VehicleManagement.vue", "mtime": 1757492245381}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\ts-loader\\index.js", "mtime": 1750678172000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1750678172000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1750678172000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\owner\\VehicleManagement.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACf,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAChF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACjB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5D,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACrB,CAAC,CAAC,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtD,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACtB,CAAC,CAAC,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAC,CAAC,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7D,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC/C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACpB,CAAC,CAAC,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1D,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACf,CAAC;cACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClB,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACf,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACjB,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;MACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;QACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAET,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/JavaCar/wiki/wiki/web/src/views/owner/VehicleManagement.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"vehicle-management\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h1>\r\n            <CarOutlined />\r\n            我的车辆\r\n          </h1>\r\n          <p>管理您的爱车信息，享受更好的维修服务</p>\r\n          <div class=\"vehicle-stats\">\r\n            <div class=\"stat-item\">\r\n              <span class=\"stat-number\">{{ vehicles.length }}</span>\r\n              <span class=\"stat-label\">辆车辆</span>\r\n            </div>\r\n            <div class=\"stat-divider\"></div>\r\n            <div class=\"stat-item\">\r\n              <span class=\"stat-number\">{{ totalMileage.toLocaleString() }}</span>\r\n              <span class=\"stat-label\">总里程(km)</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"header-actions\">\r\n          <a-button type=\"primary\" size=\"large\" @click=\"showAddModal\">\r\n            <PlusOutlined />\r\n            添加车辆\r\n          </a-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 车辆列表 -->\r\n    <div class=\"vehicles-container\">\r\n      <div v-if=\"vehicles.length === 0\" class=\"empty-state\">\r\n        <div class=\"empty-illustration\">\r\n          <CarOutlined />\r\n        </div>\r\n        <h2>还没有添加车辆</h2>\r\n        <p>添加您的第一辆车，开始专属的汽车服务之旅</p>\r\n        <a-button type=\"primary\" size=\"large\" @click=\"showAddModal\">\r\n          <PlusOutlined />\r\n          立即添加车辆\r\n        </a-button>\r\n      </div>\r\n\r\n      <div v-else class=\"vehicles-grid\">\r\n        <div \r\n          v-for=\"vehicle in vehicles\" \r\n          :key=\"vehicle.id\"\r\n          class=\"vehicle-card\"\r\n        >\r\n          <div class=\"vehicle-header\">\r\n            <div class=\"vehicle-image\">\r\n              <img :src=\"vehicle.image || '/image/default-service.png'\" :alt=\"vehicle.brand\" />\r\n              <div class=\"image-overlay\">\r\n                <a-button size=\"small\" type=\"text\" class=\"change-image-btn\" @click=\"changeVehicleImage(vehicle.id)\">\r\n                  <CameraOutlined />\r\n                  更换图片\r\n                </a-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"vehicle-content\">\r\n            <div class=\"vehicle-title\">\r\n              <h3>{{ vehicle.brand }} {{ vehicle.model }}</h3>\r\n              <div class=\"vehicle-year\">{{ vehicle.year }}年款</div>\r\n            </div>\r\n            \r\n            <div class=\"license-plate\">\r\n              <div class=\"plate-container\">\r\n                <span class=\"plate-prefix\">车牌号</span>\r\n                <span class=\"plate-number\">{{ vehicle.plateNumber }}</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"vehicle-details\">\r\n              <div class=\"detail-grid\">\r\n                <div class=\"detail-item\">\r\n                  <div class=\"detail-icon color\">\r\n                    <BgColorsOutlined />\r\n                  </div>\r\n                  <div class=\"detail-info\">\r\n                    <span class=\"detail-label\">颜色</span>\r\n                    <span class=\"detail-value\">{{ vehicle.color }}</span>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"detail-item\">\r\n                  <div class=\"detail-icon mileage\">\r\n                    <DashboardOutlined />\r\n                  </div>\r\n                  <div class=\"detail-info\">\r\n                    <span class=\"detail-label\">里程</span>\r\n                    <span class=\"detail-value\">{{ vehicle.mileage ? vehicle.mileage.toLocaleString() : '0' }} km</span>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"detail-item\" v-if=\"vehicle.engineNumber\">\r\n                  <div class=\"detail-icon engine\">\r\n                    <SettingOutlined />\r\n                  </div>\r\n                  <div class=\"detail-info\">\r\n                    <span class=\"detail-label\">发动机号</span>\r\n                    <span class=\"detail-value\">{{ vehicle.engineNumber }}</span>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"detail-item\" v-if=\"vehicle.vinNumber\">\r\n                  <div class=\"detail-icon vin\">\r\n                    <BarcodeOutlined />\r\n                  </div>\r\n                  <div class=\"detail-info\">\r\n                    <span class=\"detail-label\">车架号</span>\r\n                    <span class=\"detail-value\">{{ vehicle.vinNumber }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"vehicle-actions\">\r\n              <a-button type=\"primary\" ghost @click=\"editVehicle(vehicle.id)\">\r\n                <EditOutlined />\r\n                编辑\r\n              </a-button>\r\n              <a-button @click=\"viewHistory(vehicle.id)\">\r\n                <HistoryOutlined />\r\n                维保记录\r\n              </a-button>\r\n              <a-dropdown>\r\n                <template #overlay>\r\n                  <a-menu>\r\n                    <a-menu-item key=\"1\" @click=\"duplicateVehicle(vehicle.id)\">\r\n                      <CopyOutlined />\r\n                      复制车辆\r\n                    </a-menu-item>\r\n                    <a-menu-divider />\r\n                    <a-menu-item key=\"2\" danger @click=\"deleteVehicle(vehicle.id)\">\r\n                      <DeleteOutlined />\r\n                      删除车辆\r\n                    </a-menu-item>\r\n                  </a-menu>\r\n                </template>\r\n                <a-button>\r\n                  <MoreOutlined />\r\n                </a-button>\r\n              </a-dropdown>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加/编辑车辆弹窗 -->\r\n    <a-modal\r\n      v-model:open=\"modalVisible\"\r\n      :title=\"isEdit ? '编辑车辆' : '添加车辆'\"\r\n      @ok=\"handleSubmit\"\r\n      @cancel=\"handleCancel\"\r\n    >\r\n      <a-form\r\n        :model=\"vehicleForm\"\r\n        :rules=\"formRules\"\r\n        ref=\"formRef\"\r\n        layout=\"vertical\"\r\n      >\r\n        <a-form-item label=\"品牌\" name=\"brand\">\r\n          <a-input v-model:value=\"vehicleForm.brand\" placeholder=\"请输入车辆品牌\" />\r\n        </a-form-item>\r\n        <a-form-item label=\"型号\" name=\"model\">\r\n          <a-input v-model:value=\"vehicleForm.model\" placeholder=\"请输入车辆型号\" />\r\n        </a-form-item>\r\n        <a-form-item label=\"车牌号\" name=\"plateNumber\">\r\n          <a-input v-model:value=\"vehicleForm.plateNumber\" placeholder=\"请输入车牌号\" />\r\n        </a-form-item>\r\n        <a-form-item label=\"年份\" name=\"year\">\r\n          <a-input-number v-model:value=\"vehicleForm.year\" :min=\"1990\" :max=\"2024\" style=\"width: 100%\" />\r\n        </a-form-item>\r\n        <a-form-item label=\"颜色\" name=\"color\">\r\n          <a-input v-model:value=\"vehicleForm.color\" placeholder=\"请输入车辆颜色\" />\r\n        </a-form-item>\r\n        <a-form-item label=\"发动机号\" name=\"engineNumber\">\r\n          <a-input v-model:value=\"vehicleForm.engineNumber\" placeholder=\"请输入发动机号\" />\r\n        </a-form-item>\r\n        <a-form-item label=\"车架号\" name=\"vinNumber\">\r\n          <a-input v-model:value=\"vehicleForm.vinNumber\" placeholder=\"请输入车架号\" />\r\n        </a-form-item>\r\n      </a-form>\r\n    </a-modal>\r\n\r\n    <!-- 隐藏的文件输入用于图片上传 -->\r\n    <input \r\n      ref=\"fileInputRef\" \r\n      type=\"file\" \r\n      accept=\"image/*\" \r\n      style=\"display: none;\" \r\n      @change=\"handleImageUpload\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { defineComponent, ref, computed, onMounted } from 'vue';\r\nimport { \r\n  PlusOutlined, \r\n  EditOutlined, \r\n  DeleteOutlined,\r\n  CarOutlined,\r\n  CameraOutlined,\r\n  BgColorsOutlined,\r\n  DashboardOutlined,\r\n  SettingOutlined,\r\n  BarcodeOutlined,\r\n  HistoryOutlined,\r\n  CopyOutlined,\r\n  MoreOutlined\r\n} from '@ant-design/icons-vue';\r\nimport { message, Modal } from 'ant-design-vue';\r\nimport axios from 'axios';\r\nimport { getCurrentUser } from '@/utils/auth';\r\n\r\nexport default defineComponent({\r\n  name: 'VehicleManagement',\r\n  components: {\r\n    PlusOutlined,\r\n    EditOutlined,\r\n    DeleteOutlined,\r\n    CarOutlined,\r\n    CameraOutlined,\r\n    BgColorsOutlined,\r\n    DashboardOutlined,\r\n    SettingOutlined,\r\n    BarcodeOutlined,\r\n    HistoryOutlined,\r\n    CopyOutlined,\r\n    MoreOutlined\r\n  },\r\n  setup() {\r\n    const vehicles = ref<any[]>([]);\r\n    const modalVisible = ref(false);\r\n    const isEdit = ref(false);\r\n    const formRef = ref();\r\n    const fileInputRef = ref();\r\n    const currentVehicleId = ref<number | null>(null);\r\n    \r\n    // 计算总里程\r\n    const totalMileage = computed(() => {\r\n      return vehicles.value.reduce((total, vehicle) => {\r\n        return total + (vehicle.mileage || 0);\r\n      }, 0);\r\n    });\r\n\r\n    const vehicleForm = ref({\r\n      id: null,\r\n      brand: '',\r\n      model: '',\r\n      plateNumber: '',\r\n      year: new Date().getFullYear(),\r\n      color: '',\r\n      engineNumber: '',\r\n      vinNumber: ''\r\n    });\r\n\r\n    const formRules = {\r\n      brand: [{ required: true, message: '请输入车辆品牌', trigger: 'blur' }],\r\n      model: [{ required: true, message: '请输入车辆型号', trigger: 'blur' }],\r\n      plateNumber: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],\r\n      year: [{ required: true, message: '请选择年份', trigger: 'blur' }],\r\n      color: [{ required: true, message: '请输入车辆颜色', trigger: 'blur' }]\r\n    };\r\n\r\n    const showAddModal = () => {\r\n      isEdit.value = false;\r\n      vehicleForm.value = {\r\n        id: null,\r\n        brand: '',\r\n        model: '',\r\n        plateNumber: '',\r\n        year: new Date().getFullYear(),\r\n        color: '',\r\n        engineNumber: '',\r\n        vinNumber: ''\r\n      };\r\n      modalVisible.value = true;\r\n    };\r\n\r\n    const editVehicle = (id: number) => {\r\n      isEdit.value = true;\r\n      const vehicle = vehicles.value.find((v: any) => v.id === id);\r\n      if (vehicle) {\r\n        vehicleForm.value = { ...vehicle };\r\n      }\r\n      modalVisible.value = true;\r\n    };\r\n\r\n    const deleteVehicle = (id: number) => {\r\n      Modal.confirm({\r\n        title: '确认删除',\r\n        content: '确定要删除这辆车辆吗？',\r\n        async onOk() {\r\n          try {\r\n            const response = await axios.get('/vehicle/remove', {\r\n              params: { id }\r\n            });\r\n            \r\n            if (response.data.success !== false) {\r\n              message.success(response.data.message || '车辆删除成功');\r\n              // 重新加载车辆列表\r\n              await loadVehicles();\r\n            } else {\r\n              message.error(response.data.message || '删除失败');\r\n            }\r\n          } catch (error) {\r\n            console.error('删除车辆失败:', error);\r\n            message.error('删除车辆失败');\r\n          }\r\n        }\r\n      });\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n      try {\r\n        await formRef.value.validate();\r\n        \r\n        // 从当前登录用户会话中获取用户ID\r\n        const currentUser = getCurrentUser();\r\n        if (!currentUser) {\r\n          message.error('用户未登录，请重新登录');\r\n          return;\r\n        }\r\n        const userId = currentUser.id;\r\n        \r\n        // 转换前端数据格式到后端格式\r\n        const saveData = {\r\n          id: isEdit.value ? vehicleForm.value.id : null,\r\n          licensePlate: vehicleForm.value.plateNumber, // 前端字段是plateNumber\r\n          brand: vehicleForm.value.brand,\r\n          model: vehicleForm.value.model,\r\n          color: vehicleForm.value.color,\r\n          year: vehicleForm.value.year,\r\n          engineNumber: vehicleForm.value.engineNumber,\r\n          vin: vehicleForm.value.vinNumber, // 前端字段是vinNumber\r\n          userId: userId  // 使用userId字段\r\n        };\r\n        \r\n        const response = await axios.post('/vehicle/save', saveData);\r\n        \r\n        if (response.data.success !== false) {\r\n          message.success(response.data.message || (isEdit.value ? '车辆更新成功' : '车辆添加成功'));\r\n          modalVisible.value = false;\r\n          // 重新加载车辆列表\r\n          await loadVehicles();\r\n        } else {\r\n          message.error(response.data.message || '操作失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('保存车辆失败:', error);\r\n        message.error('保存车辆失败');\r\n      }\r\n    };\r\n\r\n    const handleCancel = () => {\r\n      modalVisible.value = false;\r\n    };\r\n\r\n    const viewHistory = (vehicleId: number) => {\r\n      message.info(`查看车辆 ${vehicleId} 的维保记录`);\r\n    };\r\n\r\n    const duplicateVehicle = (vehicleId: number) => {\r\n      const vehicle = vehicles.value.find(v => v.id === vehicleId);\r\n      if (vehicle) {\r\n        const newVehicle = {\r\n          ...vehicle,\r\n          id: Date.now(),\r\n          plateNumber: vehicle.plateNumber + '_副本'\r\n        };\r\n        vehicles.value.push(newVehicle);\r\n        message.success('车辆复制成功');\r\n      }\r\n    };\r\n\r\n    const changeVehicleImage = (vehicleId: number) => {\r\n      currentVehicleId.value = vehicleId;\r\n      if (fileInputRef.value) {\r\n        fileInputRef.value.click();\r\n      }\r\n    };\r\n\r\n    const handleImageUpload = async (event: Event) => {\r\n      const target = event.target as HTMLInputElement;\r\n      const file = target.files && target.files[0];\r\n      \r\n      if (!file || !currentVehicleId.value) {\r\n        return;\r\n      }\r\n\r\n      // 验证文件类型\r\n      if (!file.type.startsWith('image/')) {\r\n        message.error('请选择图片文件');\r\n        return;\r\n      }\r\n\r\n      // 验证文件大小（限制为5MB）\r\n      const maxSize = 5 * 1024 * 1024;\r\n      if (file.size > maxSize) {\r\n        message.error('图片大小不能超过5MB');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        // 创建FormData对象\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n        formData.append('vehicleId', currentVehicleId.value.toString());\r\n\r\n        // TODO: 这里应该调用真实的图片上传API\r\n        // const response = await axios.post('/api/upload/vehicle-image', formData);\r\n        \r\n        // 暂时模拟上传成功，使用本地预览\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          const imageUrl = e.target && e.target.result as string;\r\n          // 更新车辆图片\r\n          const vehicleIndex = vehicles.value.findIndex(v => v.id === currentVehicleId.value);\r\n          if (vehicleIndex !== -1) {\r\n            vehicles.value[vehicleIndex].image = imageUrl;\r\n          }\r\n          message.success('图片上传成功');\r\n        };\r\n        reader.readAsDataURL(file);\r\n        \r\n      } catch (error) {\r\n        console.error('图片上传失败:', error);\r\n        message.error('图片上传失败');\r\n      } finally {\r\n        // 清空文件输入\r\n        target.value = '';\r\n        currentVehicleId.value = null;\r\n      }\r\n    };\r\n\r\n    const loadVehicles = async () => {\r\n      try {\r\n        // 从当前登录用户会话中获取用户ID\r\n        const currentUser = getCurrentUser();\r\n        if (!currentUser) {\r\n          message.error('用户未登录，请重新登录');\r\n          vehicles.value = [];\r\n          return;\r\n        }\r\n        const userId = currentUser.id;\r\n        \r\n        const response = await axios.get('/vehicle/getVehicleListByPage', {\r\n          params: {\r\n            page: 1,\r\n            size: 100, // 获取所有车辆\r\n            customerId: userId  // 修改为customerId参数\r\n          }\r\n        });\r\n        \r\n        if (response.data.success && response.data.content) {\r\n          // 转换后端数据格式到前端格式\r\n          const backendVehicles = response.data.content.list || [];\r\n          vehicles.value = backendVehicles.map((vehicle: any) => ({\r\n            id: vehicle.id,\r\n            brand: vehicle.brand,\r\n            model: vehicle.model,\r\n            plateNumber: vehicle.licensePlate, // 后端字段是licensePlate\r\n            year: vehicle.year,\r\n            color: vehicle.color,\r\n            mileage: vehicle.mileage || 0,\r\n            engineNumber: vehicle.engineNumber,\r\n            vinNumber: vehicle.vin, // 后端字段是vin\r\n            image: vehicle.image || '/image/default-service.png'\r\n          }));\r\n        } else {\r\n          console.warn('获取车辆列表失败:', response.data.message);\r\n          vehicles.value = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('加载车辆列表失败:', error);\r\n        message.error('加载车辆列表失败');\r\n        // 使用空数组作为默认值\r\n        vehicles.value = [];\r\n      }\r\n    };\r\n\r\n    onMounted(() => {\r\n      loadVehicles();\r\n    });\r\n\r\n    return {\r\n      vehicles,\r\n      totalMileage,\r\n      modalVisible,\r\n      isEdit,\r\n      formRef,\r\n      fileInputRef,\r\n      vehicleForm,\r\n      formRules,\r\n      showAddModal,\r\n      editVehicle,\r\n      deleteVehicle,\r\n      handleSubmit,\r\n      handleCancel,\r\n      viewHistory,\r\n      duplicateVehicle,\r\n      changeVehicleImage,\r\n      handleImageUpload\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.vehicle-management {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 0;\r\n  position: relative;\r\n}\r\n\r\n.vehicle-management::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\r\n  pointer-events: none;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 0 0 24px 24px;\r\n  padding: 32px;\r\n  margin-bottom: 32px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  gap: 32px;\r\n}\r\n\r\n.title-section h1 {\r\n  margin: 0 0 8px 0;\r\n  color: #1a1a1a;\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.title-section p {\r\n  margin: 0 0 20px 0;\r\n  color: #666;\r\n  font-size: 16px;\r\n}\r\n\r\n.vehicle-stats {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: baseline;\r\n  gap: 4px;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 24px;\r\n  font-weight: 700;\r\n  color: #667eea;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.stat-divider {\r\n  width: 1px;\r\n  height: 24px;\r\n  background: rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.header-actions .ant-btn {\r\n  height: 44px;\r\n  border-radius: 12px;\r\n  font-weight: 500;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n/* 车辆容器 */\r\n.vehicles-container {\r\n  padding: 0 32px 32px;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 80px 24px;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.empty-illustration {\r\n  width: 120px;\r\n  height: 120px;\r\n  margin: 0 auto 32px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 48px;\r\n  color: #667eea;\r\n}\r\n\r\n.empty-state h2 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  color: #1a1a1a;\r\n}\r\n\r\n.empty-state p {\r\n  margin: 0 0 32px 0;\r\n  color: #666;\r\n  font-size: 16px;\r\n}\r\n\r\n/* 车辆网格 */\r\n.vehicles-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\r\n  gap: 24px;\r\n}\r\n\r\n.vehicle-card {\r\n  background: rgba(255, 255, 255, 0.98);\r\n  backdrop-filter: blur(20px);\r\n  border-radius: 20px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.vehicle-card:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 车辆头部 */\r\n.vehicle-header {\r\n  position: relative;\r\n  height: 200px;\r\n  overflow: hidden;\r\n}\r\n\r\n.vehicle-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n}\r\n\r\n.vehicle-image img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.vehicle-card:hover .vehicle-image img {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.image-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n}\r\n\r\n.vehicle-card:hover .image-overlay {\r\n  opacity: 1;\r\n}\r\n\r\n.change-image-btn {\r\n  color: white;\r\n  border: 1px solid rgba(255, 255, 255, 0.5);\r\n  background: rgba(255, 255, 255, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border-radius: 8px;\r\n}\r\n\r\n.change-image-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n/* 车辆内容 */\r\n.vehicle-content {\r\n  padding: 24px;\r\n}\r\n\r\n.vehicle-title {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.vehicle-title h3 {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n  color: #1a1a1a;\r\n}\r\n\r\n.vehicle-year {\r\n  font-size: 14px;\r\n  color: #666;\r\n  background: rgba(102, 126, 234, 0.1);\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n}\r\n\r\n/* 车牌号 */\r\n.license-plate {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.plate-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.plate-prefix {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.plate-number {\r\n  background: linear-gradient(135deg, #667eea, #764ba2);\r\n  color: white;\r\n  padding: 8px 16px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  letter-spacing: 2px;\r\n  font-family: 'Courier New', monospace;\r\n}\r\n\r\n/* 车辆详情 */\r\n.vehicle-details {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.detail-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 16px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  background: rgba(248, 250, 252, 0.8);\r\n  border-radius: 12px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.detail-item:hover {\r\n  background: rgba(248, 250, 252, 1);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.detail-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  color: white;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.detail-icon.color {\r\n  background: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.detail-icon.mileage {\r\n  background: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.detail-icon.engine {\r\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.detail-icon.vin {\r\n  background: linear-gradient(135deg, #fa709a, #fee140);\r\n}\r\n\r\n.detail-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n}\r\n\r\n.detail-label {\r\n  font-size: 12px;\r\n  color: #666;\r\n  line-height: 1;\r\n}\r\n\r\n.detail-value {\r\n  font-size: 14px;\r\n  color: #1a1a1a;\r\n  font-weight: 500;\r\n  line-height: 1;\r\n}\r\n\r\n/* 车辆操作 */\r\n.vehicle-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.vehicle-actions .ant-btn {\r\n  border-radius: 8px;\r\n  font-weight: 500;\r\n  flex: 1;\r\n}\r\n\r\n.vehicle-actions .ant-btn:last-child {\r\n  flex: 0 0 auto;\r\n  width: 40px;\r\n  padding: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1400px) {\r\n  .vehicles-grid {\r\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .page-header {\r\n    padding: 24px 20px;\r\n    border-radius: 0;\r\n  }\r\n  \r\n  .vehicles-container {\r\n    padding: 0 20px 24px;\r\n  }\r\n  \r\n  .header-content {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .title-section h1 {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .vehicle-stats {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .vehicles-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .detail-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .empty-state {\r\n    padding: 60px 20px;\r\n  }\r\n  \r\n  .empty-illustration {\r\n    width: 100px;\r\n    height: 100px;\r\n    font-size: 40px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .page-header {\r\n    padding: 20px 16px;\r\n  }\r\n  \r\n  .vehicles-container {\r\n    padding: 0 16px 20px;\r\n  }\r\n  \r\n  .title-section h1 {\r\n    font-size: 20px;\r\n  }\r\n  \r\n  .vehicle-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .vehicle-title {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .plate-container {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .vehicle-actions {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .vehicle-actions .ant-btn:last-child {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}