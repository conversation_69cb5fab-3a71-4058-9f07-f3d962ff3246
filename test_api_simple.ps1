# 简化的API测试脚本
Write-Host "=== 汽车维修服务管理API功能测试 ===" -ForegroundColor Green

$baseUrl = "http://localhost:8880"

Write-Host "`n1. 测试服务列表查询..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/service/getServiceListByPage?page=1&size=10" -Method GET
    Write-Host "✅ 服务列表查询成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "   返回数据: success=$($data.success), total=$($data.content.total)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 服务列表查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n2. 测试分类数据查询..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/category/allList" -Method GET
    Write-Host "✅ 分类数据查询成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "   返回数据: success=$($data.success), 分类数量=$($data.content.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 分类数据查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n3. 测试服务搜索功能..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/service/getServiceListByPage?page=1&size=10&name=Test" -Method GET
    Write-Host "✅ 服务搜索成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
    $data = $response.Content | ConvertFrom-Json
    Write-Host "   搜索结果: success=$($data.success), 匹配数量=$($data.content.total)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 服务搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
