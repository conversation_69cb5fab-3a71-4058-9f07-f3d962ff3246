# 测试维修店预约功能的PowerShell脚本
# 确保后端服务正在运行在 localhost:8080

$baseUrl = "http://localhost:8080"

Write-Host "=== 测试维修店预约管理功能 ===" -ForegroundColor Green

# 测试1: 获取今日预约列表
Write-Host "`n1. 测试获取今日预约列表..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/shop/booking/today" -Method GET
    Write-Host "✅ 今日预约列表获取成功:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 获取今日预约列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 获取预约统计
Write-Host "`n2. 测试获取预约统计..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/shop/booking/stats" -Method GET
    Write-Host "✅ 预约统计获取成功:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 获取预约统计失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试3: 更新预约状态
Write-Host "`n3. 测试更新预约状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/shop/booking/1/status?status=2" -Method PUT
    Write-Host "✅ 预约状态更新成功:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
    
    # 再次获取今日预约列表查看变化
    Start-Sleep -Seconds 1
    $updatedList = Invoke-RestMethod -Uri "$baseUrl/shop/booking/today" -Method GET
    Write-Host "更新后的预约列表:" -ForegroundColor Cyan
    Write-Host ($updatedList | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 更新预约状态失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: 测试快捷确认方法
Write-Host "`n4. 测试快捷确认方法..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/shop/booking/4/confirm" -Method PUT
    Write-Host "✅ 预约确认成功:" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "❌ 预约确认失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试5: 测试错误情况
Write-Host "`n5. 测试错误情况..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/shop/booking/999/status?status=2" -Method PUT
    Write-Host "预约不存在的响应:" -ForegroundColor Magenta
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "✅ 正确处理了不存在的预约: $($_.Exception.Message)" -ForegroundColor Green
}

# 测试6: 测试无效状态值
Write-Host "`n6. 测试无效状态值..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/shop/booking/1/status?status=10" -Method PUT
    Write-Host "无效状态值的响应:" -ForegroundColor Magenta
    Write-Host ($response | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "✅ 正确处理了无效状态值: $($_.Exception.Message)" -ForegroundColor Green
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "请检查上述结果，确保所有功能正常工作。" -ForegroundColor Cyan
