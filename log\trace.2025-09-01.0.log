2025-09-01 15:28:44.336 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 1712 (D:\copy\wiki\wiki\target\classes started by fls in D:\copy\wiki)
2025-09-01 15:28:44.340 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-01 15:28:45.640 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-09-01 15:28:45.650 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-09-01 15:28:45.651 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-01 15:28:45.651 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-01 15:28:45.764 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-01 15:28:45.764 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1373 ms
2025-09-01 15:28:47.469 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-01 15:28:47.681 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-09-01 15:28:47.709 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-09-01 15:28:47.718 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.898 seconds (JVM running for 5.595)
2025-09-01 15:28:47.720 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-09-01 15:28:47.721 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-09-01 15:28:59.656 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-01 15:28:59.657 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-01 15:28:59.658 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-09-01 15:28:59.742 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171222234144  [0;39m ------------- 开始 -------------
2025-09-01 15:28:59.743 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171222234144  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-09-01 15:28:59.744 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171222234144  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-09-01 15:28:59.745 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171222234144  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:28:59.745 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171222365216  [0;39m ------------- 开始 -------------
2025-09-01 15:28:59.746 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171222365216  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:28:59.747 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171222365216  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:28:59.747 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171222365216  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:28:59.837 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171222365216  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-01 15:28:59.837 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171222234144  [0;39m 请求参数: [{}]
2025-09-01 15:29:00.013 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4825171222234144  [0;39m {dataSource-1} inited
2025-09-01 15:29:00.650 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825171222365216  [0;39m 总行数：19
2025-09-01 15:29:00.651 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825171222365216  [0;39m 总页数：1
2025-09-01 15:29:00.660 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171222234144  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:29:00.661 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171222234144  [0;39m ------------- 结束 耗时：920 ms -------------
2025-09-01 15:29:00.674 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171222365216  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-01 15:29:00.676 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171222365216  [0;39m ------------- 结束 耗时：931 ms -------------
2025-09-01 15:29:00.883 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171259655200  [0;39m ------------- 开始 -------------
2025-09-01 15:29:00.884 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171259655200  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:29:00.884 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171259655200  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:29:00.885 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171259655200  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:29:00.885 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171259655200  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-01 15:29:00.902 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825171259655200  [0;39m 总行数：19
2025-09-01 15:29:00.902 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825171259655200  [0;39m 总页数：1
2025-09-01 15:29:00.905 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171259655200  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-01 15:29:00.906 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171259655200  [0;39m ------------- 结束 耗时：23 ms -------------
2025-09-01 15:29:04.939 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171392562208  [0;39m ------------- 开始 -------------
2025-09-01 15:29:04.939 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171392562209  [0;39m ------------- 开始 -------------
2025-09-01 15:29:04.946 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171392562208  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-09-01 15:29:04.946 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171392562209  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:29:04.946 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171392562208  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-09-01 15:29:04.946 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171392562208  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:29:04.946 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171392562209  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:29:04.948 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171392562209  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:29:04.948 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171392562208  [0;39m 请求参数: [{}]
2025-09-01 15:29:04.948 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171392562209  [0;39m 请求参数: [{"page":1,"size":5}]
2025-09-01 15:29:04.962 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825171392562209  [0;39m 总行数：19
2025-09-01 15:29:04.962 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825171392562209  [0;39m 总页数：4
2025-09-01 15:29:04.962 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171392562208  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:29:04.965 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171392562208  [0;39m ------------- 结束 耗时：26 ms -------------
2025-09-01 15:29:04.965 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171392562209  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":19},"success":true}
2025-09-01 15:29:04.966 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171392562209  [0;39m ------------- 结束 耗时：27 ms -------------
2025-09-01 15:29:10.504 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171574916128  [0;39m ------------- 开始 -------------
2025-09-01 15:29:10.504 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171574916128  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-09-01 15:29:10.504 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171574916128  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-09-01 15:29:10.506 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171574916128  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:29:10.506 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171574916128  [0;39m 请求参数: [{"name":""}]
2025-09-01 15:29:10.529 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171574916128  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:29:10.529 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171574916128  [0;39m ------------- 结束 耗时：25 ms -------------
2025-09-01 15:29:16.699 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171777913888  [0;39m ------------- 开始 -------------
2025-09-01 15:29:16.699 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171777913888  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-09-01 15:29:16.700 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171777913888  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-09-01 15:29:16.700 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171777913888  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:29:16.700 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171777913888  [0;39m 请求参数: [{"name":""}]
2025-09-01 15:29:16.708 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171777913888  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:29:16.708 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171777913888  [0;39m ------------- 结束 耗时：9 ms -------------
2025-09-01 15:29:20.744 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171910460448  [0;39m ------------- 开始 -------------
2025-09-01 15:29:20.744 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171910460448  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-09-01 15:29:20.744 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171910460449  [0;39m ------------- 开始 -------------
2025-09-01 15:29:20.744 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171910460448  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-09-01 15:29:20.749 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171910460448  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:29:20.749 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171910460449  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:29:20.749 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171910460448  [0;39m 请求参数: [{}]
2025-09-01 15:29:20.749 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171910460449  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:29:20.752 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171910460449  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:29:20.752 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171910460449  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-01 15:29:20.761 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171910460448  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:29:20.761 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171910460448  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-01 15:29:20.766 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825171910460449  [0;39m 总行数：19
2025-09-01 15:29:20.766 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825171910460449  [0;39m 总页数：1
2025-09-01 15:29:20.771 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171910460449  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-01 15:29:20.772 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171910460449  [0;39m ------------- 结束 耗时：28 ms -------------
2025-09-01 15:29:20.866 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825171914458144  [0;39m ------------- 开始 -------------
2025-09-01 15:29:20.868 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825171914458144  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:29:20.868 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825171914458144  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:29:20.868 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825171914458144  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:29:20.870 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825171914458144  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-01 15:29:20.882 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825171914458144  [0;39m 总行数：19
2025-09-01 15:29:20.884 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825171914458144  [0;39m 总页数：1
2025-09-01 15:29:20.884 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825171914458144  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-01 15:29:20.884 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825171914458144  [0;39m ------------- 结束 耗时：18 ms -------------
2025-09-01 15:29:28.163 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825172153566240  [0;39m ------------- 开始 -------------
2025-09-01 15:29:28.163 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825172153566240  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-09-01 15:29:28.163 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825172153566240  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-09-01 15:29:28.163 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825172153566240  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:29:28.163 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825172153566240  [0;39m 请求参数: [{"name":""}]
2025-09-01 15:29:28.186 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825172153566240  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:29:28.188 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825172153566240  [0;39m ------------- 结束 耗时：25 ms -------------
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825175145022496  [0;39m ------------- 开始 -------------
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825175145022497  [0;39m ------------- 开始 -------------
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825175145022496  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825175145022497  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825175145022496  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825175145022497  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825175145022496  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825175145022497  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825175145022496  [0;39m 请求参数: [{}]
2025-09-01 15:30:59.455 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825175145022497  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-01 15:30:59.510 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4825175145022496  [0;39m discard long time none received connection. , jdbcUrl : *********************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 91310
2025-09-01 15:30:59.510 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4825175145022497  [0;39m discard long time none received connection. , jdbcUrl : *********************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 98731
2025-09-01 15:30:59.559 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825175145022496  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:30:59.559 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825175145022496  [0;39m ------------- 结束 耗时：104 ms -------------
2025-09-01 15:30:59.568 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825175145022497  [0;39m 总行数：19
2025-09-01 15:30:59.568 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825175145022497  [0;39m 总页数：1
2025-09-01 15:30:59.568 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825175145022497  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-01 15:30:59.568 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825175145022497  [0;39m ------------- 结束 耗时：113 ms -------------
2025-09-01 15:30:59.644 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825175151215648  [0;39m ------------- 开始 -------------
2025-09-01 15:30:59.644 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825175151215648  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:30:59.644 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825175151215648  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:30:59.644 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825175151215648  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:30:59.644 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825175151215648  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-01 15:30:59.658 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825175151215648  [0;39m 总行数：19
2025-09-01 15:30:59.658 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825175151215648  [0;39m 总页数：1
2025-09-01 15:30:59.664 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825175151215648  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-01 15:30:59.664 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825175151215648  [0;39m ------------- 结束 耗时：20 ms -------------
2025-09-01 15:31:32.650 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-01 15:31:32.655 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-01 15:55:41.789 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 8324 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-01 15:55:41.796 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-01 15:55:43.080 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-09-01 15:55:43.089 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-09-01 15:55:43.089 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-01 15:55:43.089 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-01 15:55:43.202 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-01 15:55:43.202 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1349 ms
2025-09-01 15:55:45.053 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-01 15:55:45.308 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-09-01 15:55:45.333 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-09-01 15:55:45.348 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.073 seconds (JVM running for 5.564)
2025-09-01 15:55:45.351 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-09-01 15:55:45.351 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-09-01 15:55:54.747 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-01 15:55:54.750 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-01 15:55:54.750 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-01 15:55:54.813 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825224144913440  [0;39m ------------- 开始 -------------
2025-09-01 15:55:54.813 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825224144913440  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-09-01 15:55:54.813 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825224144913440  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-09-01 15:55:54.813 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825224144913441  [0;39m ------------- 开始 -------------
2025-09-01 15:55:54.813 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825224144913440  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:55:54.813 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825224144913441  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:55:54.817 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825224144913441  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:55:54.817 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825224144913441  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:55:54.876 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825224144913441  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-01 15:55:54.876 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825224144913440  [0;39m 请求参数: [{}]
2025-09-01 15:55:54.996 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4825224144913440  [0;39m {dataSource-1} inited
2025-09-01 15:55:55.274 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825224144913441  [0;39m 总行数：19
2025-09-01 15:55:55.274 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825224144913441  [0;39m 总页数：1
2025-09-01 15:55:55.283 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825224144913440  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:55:55.283 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825224144913440  [0;39m ------------- 结束 耗时：470 ms -------------
2025-09-01 15:55:55.291 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825224144913441  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-01 15:55:55.291 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825224144913441  [0;39m ------------- 结束 耗时：478 ms -------------
2025-09-01 15:55:55.431 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825224165164064  [0;39m ------------- 开始 -------------
2025-09-01 15:55:55.431 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825224165164064  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:55:55.431 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825224165164064  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:55:55.431 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825224165164064  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:55:55.436 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825224165164064  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-01 15:55:55.449 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825224165164064  [0;39m 总行数：19
2025-09-01 15:55:55.449 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825224165164064  [0;39m 总页数：1
2025-09-01 15:55:55.449 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825224165164064  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-01 15:55:55.449 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825224165164064  [0;39m ------------- 结束 耗时：18 ms -------------
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825224281130017  [0;39m ------------- 开始 -------------
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825224281130016  [0;39m ------------- 开始 -------------
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825224281130016  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825224281130017  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825224281130016  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825224281130017  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825224281130016  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825224281130017  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825224281130016  [0;39m 请求参数: [{}]
2025-09-01 15:55:58.970 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825224281130017  [0;39m 请求参数: [{"page":1,"size":5}]
2025-09-01 15:55:58.987 INFO  com.gec.wiki.service.EbookService                 :72   [32m4825224281130017  [0;39m 总行数：19
2025-09-01 15:55:58.987 INFO  com.gec.wiki.service.EbookService                 :73   [32m4825224281130017  [0;39m 总页数：4
2025-09-01 15:55:58.991 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825224281130016  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:55:58.991 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825224281130016  [0;39m ------------- 结束 耗时：21 ms -------------
2025-09-01 15:55:58.991 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825224281130017  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":19},"success":true}
2025-09-01 15:55:58.991 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825224281130017  [0;39m ------------- 结束 耗时：21 ms -------------
2025-09-01 15:56:01.398 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4825224360690720  [0;39m ------------- 开始 -------------
2025-09-01 15:56:01.398 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4825224360690720  [0;39m 请求地址: http://localhost:8080/category/getCategoryByCategoryReq GET
2025-09-01 15:56:01.398 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4825224360690720  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getCategoryByCategoryReq
2025-09-01 15:56:01.398 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4825224360690720  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-01 15:56:01.398 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4825224360690720  [0;39m 请求参数: [{"name":""}]
2025-09-01 15:56:01.414 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4825224360690720  [0;39m 返回结果: {"content":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-01 15:56:01.414 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4825224360690720  [0;39m ------------- 结束 耗时：16 ms -------------
2025-09-01 15:56:13.482 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-01 15:56:13.498 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
