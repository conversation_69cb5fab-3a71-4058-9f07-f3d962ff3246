import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import Register from '../views/Register.vue'
import Booking from '../views/Booking.vue'
import AdminService from "@/views/admin/admin-service.vue";
import CategoryManagement from "@/views/admin/CategoryManagement.vue";
import AdminDashboard from "@/views/admin/AdminDashboard.vue";
import OwnerDashboard from "@/views/owner/OwnerDashboard.vue";
import ShopDashboard from "@/views/shop/ShopDashboard.vue";
// 维修店功能页面
import OrderManagement from "@/views/shop/OrderManagement.vue";
import TechnicianManagement from "@/views/shop/TechnicianManagement.vue";
// 车主功能页面
import PersonalCenter from "@/views/owner/PersonalCenter.vue";
// 管理员功能页面
import UserManagement from "@/views/admin/UserManagement.vue";
import { getCurrentUser, canAccessPath, getDefaultDashboard, clearUserSession } from '../utils/auth';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/register',
    name: 'Register',
    component: Register
  },
  {
    path: '/booking',
    name: 'Booking',
    component: Booking
  },
  {
    path: '/about',
    name: 'About',
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: function () {
      return import(/* webpackChunkName: "about" */ '../views/About.vue')
    }
  },
  {
    path: '/admin/service',
    name: 'AdminService',
    component: AdminService
  },
  // 角色Dashboard路由
  {
    path: '/admin/dashboard',
    name: 'AdminDashboard',
    component: AdminDashboard,
    meta: { requiresAuth: true, role: 3 }
  },
  {
    path: '/owner/dashboard',
    name: 'OwnerDashboard',
    component: OwnerDashboard,
    meta: { requiresAuth: true, role: 1 }
  },
  {
    path: '/shop/dashboard',
    name: 'ShopDashboard', 
    component: ShopDashboard,
    meta: { requiresAuth: true, role: 2 }
  },
  // 维修店功能页面
  {
    path: '/shop/orders',
    name: 'ShopOrders',
    component: OrderManagement,
    meta: { requiresAuth: true, role: 2 }
  },
  {
    path: '/shop/technicians',
    name: 'ShopTechnicians',
    component: TechnicianManagement,
    meta: { requiresAuth: true, role: 2 }
  },
  // 车主功能页面
  {
    path: '/owner/profile',
    name: 'OwnerProfile',
    component: PersonalCenter,
    meta: { requiresAuth: true, role: 1 }
  },
  // 管理员功能页面
  {
    path: '/admin/users',
    name: 'AdminUsers',
    component: UserManagement,
    meta: { requiresAuth: true, role: 3 }
  },
  {
    path: '/admin/orders',
    name: 'AdminOrders',
    component: () => import('../views/admin/OrderManagement.vue'),
    meta: { requiresAuth: true, role: 3 }
  },
  {
    path: '/admin/services',
    name: 'AdminServices',
    component: AdminService,
    meta: { requiresAuth: true, role: 3 }
  },
  {
    path: '/admin/categories',
    name: 'AdminCategories',
    component: CategoryManagement,
    meta: { requiresAuth: true, role: 3 }
  },
  {
    path: '/admin/reports',
    name: 'AdminReports',
    component: () => import('../views/admin/Reports.vue'),
    meta: { requiresAuth: true, role: 3 }
  },
  {
    path: '/admin/settings',
    name: 'AdminSettings',
    component: () => import('../views/admin/Settings.vue'),
    meta: { requiresAuth: true, role: 3 }
  },
  {
    path: '/admin/login-lock-settings',
    name: 'AdminLoginLockSettings',
    component: () => import('../views/admin/LoginLockSettings.vue'),
    meta: { requiresAuth: true, role: 3 }
  },
  {
    path: '/admin/logs',
    name: 'AdminLogs',
    component: () => import('../views/admin/Logs.vue'),
    meta: { requiresAuth: true, role: 3 }
  },
  // 车主功能页面
  {
    path: '/owner/vehicles',
    name: 'OwnerVehicles',
    component: () => import('../views/owner/VehicleManagement.vue'),
    meta: { requiresAuth: true, role: 1 }
  },
  {
    path: '/owner/orders',
    name: 'OwnerOrders',
    component: () => import('../views/owner/OrderHistory.vue'),
    meta: { requiresAuth: true, role: 1 }
  },
  // 维修店功能页面
  {
    path: '/shop/services',
    name: 'ShopServices',
    component: () => import('../views/shop/ServiceManagement.vue'),
    meta: { requiresAuth: true, role: 2 }
  },
  {
    path: '/shop/reports',
    name: 'ShopReports',
    component: () => import('../views/shop/Reports.vue'),
    meta: { requiresAuth: true, role: 2 }
  },
  {
    path: '/shop/orders/:id',
    name: 'ShopOrderDetail',
    component: () => import('../views/shop/OrderDetail.vue'),
    meta: { requiresAuth: true, role: 2 }
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

// 路由守卫 - 权限验证
router.beforeEach((to, from, next) => {
  const currentUser = getCurrentUser();
  const toPath = to.path;
  
  console.log(`Navigating to: ${toPath}, User: ${currentUser ? `${currentUser.username}(${currentUser.userType})` : 'Not logged in'}`);
  
  // 1. 检查需要认证的页面
  if (to.meta.requiresAuth) {
    if (!currentUser) {
      // 未登录，跳转到登录页
      console.log('User not authenticated, redirecting to login');
      next('/login');
      return;
    }
    
    // 检查角色权限
    if (to.meta.role && to.meta.role !== currentUser.userType) {
      // 角色不匹配，跳转到对应Dashboard
      console.warn(`Access denied: User role ${currentUser.userType} trying to access role ${to.meta.role} page`);
      const defaultPath = getDefaultDashboard(currentUser.userType);
      next(defaultPath);
      return;
    }
    
    // 使用权限工具进行路径访问检查
    if (!canAccessPath(toPath)) {
      console.warn(`Access denied: User cannot access path ${toPath}`);
      const defaultPath = getDefaultDashboard(currentUser.userType);
      next(defaultPath);
      return;
    }
  }
  
  // 2. 已登录用户访问登录/注册页面，重定向到dashboard
  if ((toPath === '/login' || toPath === '/register') && currentUser) {
    const defaultPath = getDefaultDashboard(currentUser.userType);
    console.log(`Already logged in, redirecting to dashboard: ${defaultPath}`);
    next(defaultPath);
    return;
  }
  
  // 3. 已登录用户访问首页，重定向到对应dashboard（除非刚退出登录）
  if (toPath === '/' && currentUser) {
    // 如果来自登出操作或首次加载，允许访问首页
    if (from.path === '/login' || from.path === '/' || !from.name) {
      console.log('Allowing access to home page');
      next();
      return;
    }
    
    const defaultPath = getDefaultDashboard(currentUser.userType);
    console.log(`Logged in user accessing home, redirecting to dashboard: ${defaultPath}`);
    next(defaultPath);
    return;
  }
  
  // 4. 预约页面需要登录
  if (toPath === '/booking' && !currentUser) {
    console.log('Booking page requires login');
    next('/login');
    return;
  }
  
  // 5. 其他情况，直接通过
  next();
});

export default router