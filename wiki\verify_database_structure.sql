-- 验证数据库表结构脚本
USE car_service;

-- 检查booking表结构
SELECT 'booking表结构:' as info;
DESCRIBE booking;

-- 检查service表结构
SELECT 'service表结构:' as info;
DESCRIBE service;

-- 检查service表是否有shop_id字段
SELECT '检查service表shop_id字段:' as info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM information_schema.columns 
WHERE table_schema = 'car_service' 
AND table_name = 'service' 
AND column_name = 'shop_id';

-- 检查是否有测试数据
SELECT 'service表数据统计:' as info;
SELECT COUNT(*) as total_services, 
       COUNT(shop_id) as services_with_shop_id,
       COUNT(*) - COUNT(shop_id) as services_without_shop_id
FROM service;

-- 检查booking表数据
SELECT 'booking表数据统计:' as info;
SELECT COUNT(*) as total_bookings FROM booking;

-- 检查今日预约
SELECT '今日预约数据:' as info;
SELECT COUNT(*) as today_bookings 
FROM booking b
LEFT JOIN service s ON b.service_id = s.id
WHERE b.booking_date = CURDATE();
