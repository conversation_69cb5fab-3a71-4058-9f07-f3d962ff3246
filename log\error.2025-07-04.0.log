2025-07-04 00:32:59.078 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4656371435045920  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 00:33:43.961 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4656372906558496  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 00:35:15.936 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4656375920428064  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 01:11:19.814 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4656446825825312  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 12:09:19.657 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4657740501386272  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 13:26:42.481 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4657892637344800  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 13:32:34.365 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4657904167945248  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 13:35:16.605 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4657909483963424  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 13:54:08.910 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4657946587431968  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 14:00:51.237 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4657959771046944  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 14:12:44.289 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4657983136302112  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
2025-07-04 14:31:11.936 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4658019431679008  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.gec.wiki.exception.BusinessException: 保存文件异常] with root cause
com.gec.wiki.exception.BusinessException: 保存文件异常
