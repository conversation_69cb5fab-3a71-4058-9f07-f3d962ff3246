-- 修复数据库结构以支持订单管理功能
-- 2025-01-11

USE car_service;

-- 检查并添加shop_id字段到service表（如果不存在）
SET @shop_id_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'car_service' 
    AND TABLE_NAME = 'service' 
    AND COLUMN_NAME = 'shop_id'
);

-- 如果shop_id字段不存在，则添加它
SET @sql = IF(@shop_id_exists = 0, 
    'ALTER TABLE service ADD COLUMN shop_id BIGINT DEFAULT 1 COMMENT \'维修店ID\' AFTER id', 
    'SELECT "shop_id column already exists" AS result'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有服务设置shop_id（如果还没有设置）
UPDATE service SET shop_id = 1 WHERE shop_id IS NULL OR shop_id = 0;

-- 插入测试预约数据
INSERT IGNORE INTO booking (id, booking_no, user_id, vehicle_id, service_id, technician_id, service_name, service_price, contact_name, contact_phone, booking_date, booking_time, estimated_duration, problem_description, remark, status, create_time, update_time) VALUES 
(1, 'B20250911001', 2, 1, 1, 1, '机油更换', 150.00, '张先生', '13800138001', CURDATE(), '09:00:00', 30, '需要更换机油，机油已经使用5000公里', '预约时间请准时', 1, NOW(), NOW()),
(2, 'B20250911002', 3, 2, 2, 2, '轮胎更换', 400.00, '李女士', '13800138002', CURDATE(), '14:30:00', 45, '前轮轮胎磨损严重，需要更换', '更换后请检查动平衡', 2, NOW(), NOW()),
(3, 'B20250911003', 4, 3, 3, 3, '刹车片更换', 300.00, '王先生', '13800138003', CURDATE(), '16:00:00', 60, '刹车有异响，怀疑刹车片磨损', '刹车系统全面检查', 3, NOW(), NOW()),
(4, 'B20250911004', 5, 4, 5, 1, '水箱清洗', 120.00, '赵师傅', '13800138004', CURDATE(), '11:00:00', 40, '水温偏高，需要清洗水箱', '检查冷却液是否需要更换', 1, NOW(), NOW()),
(5, 'B20250911005', 6, 5, 6, 2, '电瓶检测', 50.00, '陈师傅', '13800138005', CURDATE(), '15:00:00', 20, '启动困难，怀疑电瓶问题', '如有问题建议更换', 4, NOW(), NOW()),
(6, 'B20250911006', 2, 1, 7, 3, '空调清洗', 180.00, '张先生', '13800138001', CURDATE(), '10:30:00', 50, '空调有异味，需要深度清洗', '使用专业清洗剂', 1, NOW(), NOW()),
(7, 'B20250911007', 3, 2, 1, 1, '机油更换', 150.00, '李女士', '13800138002', CURDATE(), '13:00:00', 30, '定期保养，更换机油', '使用全合成机油', 2, NOW(), NOW()),
(8, 'B20250911008', 4, 3, 4, 1, '发动机大修', 2500.00, '王先生', '13800138003', DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:00:00', 480, '发动机动力不足，需要大修', '预计需要一天时间', 1, NOW(), NOW());

-- 插入一些历史数据用于统计
INSERT IGNORE INTO booking (id, booking_no, user_id, vehicle_id, service_id, technician_id, service_name, service_price, contact_name, contact_phone, booking_date, booking_time, estimated_duration, problem_description, status, create_time, update_time) VALUES 
(9, 'B20250910001', 2, 1, 1, 1, '机油更换', 150.00, '张先生', '13800138001', DATE_SUB(CURDATE(), INTERVAL 1 DAY), '10:00:00', 30, '定期保养', 4, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
(10, 'B20250910002', 3, 2, 2, 2, '轮胎更换', 400.00, '李女士', '13800138002', DATE_SUB(CURDATE(), INTERVAL 1 DAY), '14:00:00', 45, '轮胎老化', 4, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
(11, 'B20250910003', 4, 3, 7, 3, '空调清洗', 180.00, '王先生', '13800138003', DATE_SUB(CURDATE(), INTERVAL 2 DAY), '16:30:00', 50, '空调有异味', 4, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)),
(12, 'B20250909001', 5, 4, 5, 1, '水箱清洗', 120.00, '赵师傅', '13800138004', DATE_SUB(CURDATE(), INTERVAL 3 DAY), '11:30:00', 40, '水温高', 4, DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
(13, 'B20250908001', 6, 5, 6, 2, '电瓶检测', 50.00, '陈师傅', '13800138005', DATE_SUB(CURDATE(), INTERVAL 4 DAY), '15:30:00', 20, '启动困难', 4, DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY));

-- 确保有测试用户和车辆数据
INSERT IGNORE INTO users (id, username, password, real_name, phone, email, user_type, status) VALUES 
(2, 'customer1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '张先生', '13800138001', '<EMAIL>', 1, 1),
(3, 'customer2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '李女士', '13800138002', '<EMAIL>', 1, 1),
(4, 'customer3', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '王先生', '13800138003', '<EMAIL>', 1, 1),
(5, 'customer4', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '赵师傅', '13800138004', '<EMAIL>', 1, 1),
(6, 'customer5', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqtC6mFVmxTb1Dk6Jm0bA3O6q', '陈师傅', '13800138005', '<EMAIL>', 1, 1);

INSERT IGNORE INTO vehicle (id, user_id, license_plate, brand, model, color, year, is_default, status) VALUES 
(1, 2, '京A12345', '大众', '朗逸', '白色', 2020, 1, 1),
(2, 3, '京B23456', '丰田', '凯美瑞', '黑色', 2019, 1, 1),
(3, 4, '京C34567', '本田', '雅阁', '银色', 2021, 1, 1),
(4, 5, '京D45678', '日产', '轩逸', '红色', 2018, 1, 1),
(5, 6, '京E56789', '现代', '伊兰特', '蓝色', 2020, 1, 1);

-- 检查结果
SELECT 
    'booking' as table_name,
    COUNT(*) as count 
FROM booking
UNION ALL
SELECT 
    'service' as table_name,
    COUNT(*) as count 
FROM service
UNION ALL
SELECT 
    'users' as table_name,
    COUNT(*) as count 
FROM users
UNION ALL
SELECT 
    'vehicle' as table_name,
    COUNT(*) as count 
FROM vehicle;

SELECT 'Database setup completed successfully!' as result;
