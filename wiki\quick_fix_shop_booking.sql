-- 快速修复维修店预约功能
USE car_service;

-- 第1步：确保service表有shop_id字段
ALTER TABLE service ADD COLUMN shop_id BIGINT NULL COMMENT '维修店ID' AFTER status;
CREATE INDEX idx_service_shop_id ON service(shop_id);
UPDATE service SET shop_id = 1 WHERE shop_id IS NULL;

-- 第2步：插入基础测试数据
-- 用户数据
INSERT IGNORE INTO users (id, username, password, real_name, phone, user_type, status) VALUES 
(2, 'test_user1', 'password', '张先生', '13800138001', 1, 1),
(3, 'test_user2', 'password', '李女士', '13800138002', 1, 1),
(4, 'test_user3', 'password', '王先生', '13800138003', 1, 1);

-- 车辆数据  
INSERT IGNORE INTO vehicle (id, user_id, license_plate, brand, model, is_default, status) VALUES 
(1, 2, '京A12345', '大众', '朗逸', 1, 1),
(2, 3, '京B23456', '丰田', '凯美瑞', 1, 1),
(3, 4, '京C34567', '本田', '雅阁', 1, 1);

-- 技师数据
INSERT IGNORE INTO technician (id, name, phone, specialties, level, status) VALUES 
(1, '张师傅', '13800001001', '发动机维修', 4, 1),
(2, '李师傅', '13800001002', '电气系统', 3, 1),
(3, '王师傅', '13800001003', '轮胎更换', 2, 1);

-- 第3步：清理并插入今日预约数据
DELETE FROM booking WHERE booking_date = CURDATE();

INSERT INTO booking (booking_no, user_id, vehicle_id, service_id, technician_id, service_name, service_price, contact_name, contact_phone, booking_date, booking_time, estimated_duration, problem_description, status) VALUES 
(CONCAT('B', DATE_FORMAT(NOW(), '%Y%m%d'), '001'), 2, 1, 1, 1, '机油更换', 150.00, '张先生', '13800138001', CURDATE(), '09:00:00', 30, '需要更换机油', 1),
(CONCAT('B', DATE_FORMAT(NOW(), '%Y%m%d'), '002'), 3, 2, 2, 2, '轮胎更换', 400.00, '李女士', '13800138002', CURDATE(), '14:30:00', 45, '轮胎磨损', 2),
(CONCAT('B', DATE_FORMAT(NOW(), '%Y%m%d'), '003'), 4, 3, 3, 3, '刹车维修', 300.00, '王先生', '13800138003', CURDATE(), '16:00:00', 60, '刹车异响', 3),
(CONCAT('B', DATE_FORMAT(NOW(), '%Y%m%d'), '004'), 2, 1, 5, 1, '水箱清洗', 120.00, '张先生', '13800138001', CURDATE(), '11:00:00', 40, '水温偏高', 1),
(CONCAT('B', DATE_FORMAT(NOW(), '%Y%m%d'), '005'), 3, 2, 6, 2, '电瓶检测', 50.00, '李女士', '13800138002', CURDATE(), '15:00:00', 20, '启动困难', 4);

-- 第4步：插入历史数据用于统计
INSERT IGNORE INTO booking (booking_no, user_id, vehicle_id, service_id, technician_id, service_name, service_price, contact_name, contact_phone, booking_date, booking_time, status) VALUES 
('B20250901001', 2, 1, 1, 1, '机油更换', 150.00, '张先生', '13800138001', '2025-09-01', '09:00:00', 4),
('B20250902001', 3, 2, 2, 2, '轮胎更换', 400.00, '李女士', '13800138002', '2025-09-02', '14:00:00', 4),
('B20250903001', 4, 3, 1, 3, '机油更换', 150.00, '王先生', '13800138003', '2025-09-03', '16:30:00', 4);

-- 验证数据
SELECT '验证今日预约:' as info;
SELECT COUNT(*) as count FROM booking b JOIN service s ON b.service_id = s.id WHERE b.booking_date = CURDATE() AND s.shop_id = 1;

SELECT '完成！请重启应用并刷新页面测试。' as result;
