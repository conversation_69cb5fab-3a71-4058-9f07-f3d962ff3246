-- 汽车维修服务平台数据库表结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS car_service DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE car_service;

-- 分类表
CREATE TABLE category (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    parent BIGINT NOT NULL DEFAULT 0 COMMENT '父id',
    name VARCHAR(50) NOT NULL COMMENT '名称',
    sort INT COMMENT '顺序'
) COMMENT = '汽车维修服务分类';

-- 服务表
CREATE TABLE service (
    id BIGINT PRIMARY KEY COMMENT 'id',
    name VARCHAR(50) NOT NULL COMMENT '服务名称',
    category1_id BIGINT COMMENT '一级分类',
    category2_id BIGINT COMMENT '二级分类',
    description VARCHAR(200) COMMENT '服务描述',
    cover VARCHAR(200) COMMENT '服务图片',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '服务价格',
    booking_count INT DEFAULT 0 COMMENT '预约次数',
    complete_count INT DEFAULT 0 COMMENT '完成次数',
    rating_count INT DEFAULT 0 COMMENT '好评数',
    duration INT DEFAULT 60 COMMENT '服务时长(分钟)'
) COMMENT = '汽车维修服务';

-- 客户表
CREATE TABLE customer (
    id BIGINT PRIMARY KEY COMMENT 'id',
    name VARCHAR(50) NOT NULL COMMENT '客户姓名',
    phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    email VARCHAR(50) COMMENT '邮箱',
    address VARCHAR(200) COMMENT '地址',
    id_card VARCHAR(20) COMMENT '身份证号',
    level INT DEFAULT 1 COMMENT '客户级别 (1-普通 2-VIP 3-黄金 4-钻石)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT = '客户信息';

-- 车辆表
CREATE TABLE vehicle (
    id BIGINT PRIMARY KEY COMMENT 'id',
    license_plate VARCHAR(20) NOT NULL COMMENT '车牌号',
    brand VARCHAR(50) COMMENT '车辆品牌',
    model VARCHAR(50) COMMENT '车辆型号',
    color VARCHAR(20) COMMENT '车辆颜色',
    year INT COMMENT '车辆年份',
    engine_number VARCHAR(50) COMMENT '发动机号',
    vin VARCHAR(50) COMMENT '车架号',
    customer_id BIGINT COMMENT '客户ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_customer_id (customer_id),
    INDEX idx_license_plate (license_plate)
) COMMENT = '车辆信息';

-- 技师表
CREATE TABLE technician (
    id BIGINT PRIMARY KEY COMMENT 'id',
    name VARCHAR(50) NOT NULL COMMENT '技师姓名',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(50) COMMENT '邮箱',
    specialties VARCHAR(200) COMMENT '专业技能',
    experience_years INT DEFAULT 0 COMMENT '工作年限',
    level INT DEFAULT 1 COMMENT '技师等级 (1-初级 2-中级 3-高级 4-专家)',
    salary DECIMAL(10,2) COMMENT '工资',
    status INT DEFAULT 1 COMMENT '状态 (1-在职 2-离职 3-休假)',
    hire_date DATETIME COMMENT '入职时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT = '技师信息';

-- 维修记录表
CREATE TABLE maintenance_record (
    id BIGINT PRIMARY KEY COMMENT 'id',
    vehicle_id BIGINT NOT NULL COMMENT '车辆ID',
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    service_id BIGINT COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    description TEXT COMMENT '维修描述',
    problem_description TEXT COMMENT '问题描述',
    solution TEXT COMMENT '解决方案',
    cost DECIMAL(10,2) DEFAULT 0.00 COMMENT '维修费用',
    status INT DEFAULT 1 COMMENT '状态 (1-预约 2-进行中 3-已完成 4-已取消)',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_vehicle_id (vehicle_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_service_id (service_id),
    INDEX idx_technician_id (technician_id),
    INDEX idx_status (status)
) COMMENT = '维修记录';

-- 插入示例分类数据
INSERT INTO category (id, parent, name, sort) VALUES 
(100, 0, '常规保养', 1),
(101, 100, '机油保养', 1),
(102, 100, '轮胎保养', 2),
(103, 100, '制动系统', 3),
(200, 0, '发动机维修', 2),
(201, 200, '发动机检修', 1),
(202, 200, '冷却系统', 2),
(300, 0, '电气系统', 3),
(301, 300, '电瓶维护', 1),
(302, 300, '空调系统', 2);

-- 插入示例服务数据
INSERT INTO service (id, name, category1_id, category2_id, description, cover, price, booking_count, complete_count, rating_count, duration) VALUES 
(1, '机油更换', 100, 101, '更换发动机机油，保证发动机正常运行', '', 150.00, 0, 0, 0, 30),
(2, '轮胎更换', 100, 102, '更换磨损轮胎，确保行车安全', '', 400.00, 0, 0, 0, 45),
(3, '刹车片更换', 100, 103, '更换磨损刹车片，保证制动效果', '', 300.00, 0, 0, 0, 60),
(4, '发动机大修', 200, 201, '发动机全面检修，解决动力不足问题', '', 2500.00, 0, 0, 0, 480),
(5, '水箱清洗', 200, 202, '清洗水箱，保证发动机正常冷却', '', 120.00, 0, 0, 0, 40),
(6, '电瓶检测', 300, 301, '检测电瓶性能，预防启动故障', '', 50.00, 0, 0, 0, 20),
(7, '空调清洗', 300, 302, '清洗空调系统，改善车内空气质量', '', 180.00, 0, 0, 0, 50);
