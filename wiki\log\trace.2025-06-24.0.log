2025-06-24 09:23:49.570 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 29388 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-24 09:23:49.573 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-24 09:23:50.551 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m <PERSON><PERSON> initialized with port(s): 8080 (http)
2025-06-24 09:23:50.559 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 09:23:50.560 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-24 09:23:50.560 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-24 09:23:50.627 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-24 09:23:50.627 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1015 ms
2025-06-24 09:23:51.117 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-24 09:23:51.246 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 09:23:51.259 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-24 09:23:51.267 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 2.018 seconds (JVM running for 4.788)
2025-06-24 09:23:51.268 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-24 09:23:51.268 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-24 09:24:21.880 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 09:24:21.880 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-24 09:24:21.881 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-24 09:26:08.553 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-06-24 09:26:25.047 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 10776 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-24 09:26:25.050 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-24 09:26:25.801 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-24 09:26:25.808 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 09:26:25.808 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-24 09:26:25.809 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-24 09:26:25.879 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-24 09:26:25.879 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 792 ms
2025-06-24 09:26:26.271 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-24 09:26:26.401 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 09:26:26.420 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-24 09:26:26.430 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.685 seconds (JVM running for 2.386)
2025-06-24 09:26:26.433 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-24 09:26:26.433 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-24 09:26:50.936 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 09:26:50.936 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-24 09:26:50.937 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-24 09:44:11.813 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m                  [0;39m {dataSource-1} inited
2025-06-24 09:56:40.137 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-24 09:56:40.137 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-24 09:56:44.096 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 21592 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-24 09:56:44.098 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-24 09:56:44.767 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-24 09:56:44.774 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 09:56:44.775 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-24 09:56:44.775 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-24 09:56:44.839 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-24 09:56:44.839 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 706 ms
2025-06-24 09:56:45.228 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-24 09:56:45.342 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 09:56:45.357 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-24 09:56:45.364 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.544 seconds (JVM running for 2.218)
2025-06-24 09:56:45.365 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-24 09:56:45.366 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-24 09:56:57.270 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 09:56:57.270 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-24 09:56:57.270 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-06-24 09:56:57.384 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m                  [0;39m {dataSource-1} inited
2025-06-24 10:16:16.894 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-24 10:16:16.896 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-24 10:16:19.854 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 15460 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-24 10:16:19.856 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-24 10:16:20.499 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-24 10:16:20.506 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 10:16:20.506 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-24 10:16:20.506 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-24 10:16:20.577 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-24 10:16:20.577 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 688 ms
2025-06-24 10:16:20.956 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-24 10:16:21.062 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 10:16:21.076 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-24 10:16:21.083 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.508 seconds (JVM running for 2.172)
2025-06-24 10:16:21.085 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-24 10:16:21.085 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-24 10:16:27.482 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 10:16:27.482 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-24 10:16:27.484 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 2 ms
2025-06-24 10:16:27.588 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m                  [0;39m {dataSource-1} inited
2025-06-24 11:05:04.153 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-24 11:05:04.155 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-24 11:05:06.748 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 25308 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-24 11:05:06.752 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-24 11:05:07.418 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-24 11:05:07.426 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 11:05:07.427 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-24 11:05:07.427 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-24 11:05:07.505 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-24 11:05:07.505 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 713 ms
2025-06-24 11:05:07.907 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-24 11:05:08.017 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 11:05:08.031 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-24 11:05:08.038 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.676 seconds (JVM running for 2.296)
2025-06-24 11:05:08.040 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-24 11:05:08.040 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-24 11:05:25.715 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 11:05:25.716 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-24 11:05:25.716 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-06-24 11:05:25.828 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m                  [0;39m {dataSource-1} inited
2025-06-24 11:15:56.326 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-24 11:15:56.327 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-24 11:16:01.852 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 21100 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-24 11:16:01.854 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-24 11:16:02.706 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-24 11:16:02.712 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 11:16:02.712 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-24 11:16:02.712 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-24 11:16:02.772 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-24 11:16:02.772 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 888 ms
2025-06-24 11:16:03.255 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-24 11:16:03.404 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 11:16:03.416 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-24 11:16:03.424 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.852 seconds (JVM running for 2.333)
2025-06-24 11:16:03.425 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-24 11:16:03.425 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-24 11:16:17.229 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 11:16:17.229 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-24 11:16:17.230 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-24 11:16:17.259 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592553557660667904[0;39m ------------- 开始 -------------
2025-06-24 11:16:17.260 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592553557660667904[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 11:16:17.260 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592553557660667904[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 11:16:17.260 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592553557660667904[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 11:16:17.299 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592553557660667904[0;39m 请求参数: [{}]
2025-06-24 11:16:17.362 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m592553557660667904[0;39m {dataSource-1} inited
2025-06-24 11:16:17.512 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592553557660667904[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/b5deff812515426293116c48a6226c23.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 11:16:17.513 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592553557660667904[0;39m ------------- 结束 耗时：254 ms -------------
2025-06-24 11:33:57.883 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-24 11:33:57.886 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-24 11:34:01.119 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 31708 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-24 11:34:01.122 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-24 11:34:01.939 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-24 11:34:01.947 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 11:34:01.948 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-24 11:34:01.948 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-24 11:34:02.012 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-24 11:34:02.012 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 854 ms
2025-06-24 11:34:02.544 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-24 11:34:02.707 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 11:34:02.722 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-24 11:34:02.730 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.912 seconds (JVM running for 2.515)
2025-06-24 11:34:02.732 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-24 11:34:02.732 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-24 11:34:54.358 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 11:34:54.358 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-24 11:34:54.359 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-06-24 11:34:54.397 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592558243272855552[0;39m ------------- 开始 -------------
2025-06-24 11:34:54.398 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592558243272855552[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 11:34:54.398 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592558243272855552[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 11:34:54.398 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592558243272855552[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 11:34:54.458 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592558243272855552[0;39m 请求参数: [{}]
2025-06-24 11:34:54.533 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m592558243272855552[0;39m {dataSource-1} inited
2025-06-24 11:34:54.701 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592558243272855552[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/b5deff812515426293116c48a6226c23.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 11:34:54.701 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592558243272855552[0;39m ------------- 结束 耗时：305 ms -------------
2025-06-24 11:35:08.763 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592558303532421120[0;39m ------------- 开始 -------------
2025-06-24 11:35:08.763 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592558303532421120[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 11:35:08.763 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592558303532421120[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 11:35:08.764 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592558303532421120[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 11:35:08.764 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592558303532421120[0;39m 请求参数: [{}]
2025-06-24 11:35:08.766 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592558303532421120[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/b5deff812515426293116c48a6226c23.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 11:35:08.766 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592558303532421120[0;39m ------------- 结束 耗时：3 ms -------------
2025-06-24 12:53:45.239 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592578085866573824[0;39m ------------- 开始 -------------
2025-06-24 12:53:45.239 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592578085866573824[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 12:53:45.239 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592578085866573824[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 12:53:45.240 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592578085866573824[0;39m 远程地址: 127.0.0.1
2025-06-24 12:53:45.240 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592578085866573824[0;39m 请求参数: [{}]
2025-06-24 12:53:45.266 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592578085866573824[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/b5deff812515426293116c48a6226c23.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 12:53:45.267 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592578085866573824[0;39m ------------- 结束 耗时：28 ms -------------
2025-06-24 12:53:50.517 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592578108004110336[0;39m ------------- 开始 -------------
2025-06-24 12:53:50.517 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592578108004110336[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 12:53:50.517 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592578108004110336[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 12:53:50.517 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592578108004110336[0;39m 远程地址: 127.0.0.1
2025-06-24 12:53:50.518 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592578108004110336[0;39m 请求参数: [{}]
2025-06-24 12:53:50.523 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592578108004110336[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/b5deff812515426293116c48a6226c23.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 12:53:50.524 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592578108004110336[0;39m ------------- 结束 耗时：7 ms -------------
2025-06-24 13:01:45.176 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592580098868252672[0;39m ------------- 开始 -------------
2025-06-24 13:01:45.177 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592580098868252672[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:01:45.177 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592580098868252672[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:01:45.177 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592580098868252672[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 13:01:45.177 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592580098868252672[0;39m 请求参数: [{}]
2025-06-24 13:01:45.193 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592580098868252672[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/b5deff812515426293116c48a6226c23.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:01:45.194 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592580098868252672[0;39m ------------- 结束 耗时：18 ms -------------
2025-06-24 13:06:01.889 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592581175600615424[0;39m ------------- 开始 -------------
2025-06-24 13:06:01.890 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592581175600615424[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:06:01.890 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592581175600615424[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:06:01.890 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592581175600615424[0;39m 远程地址: 127.0.0.1
2025-06-24 13:06:01.890 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592581175600615424[0;39m 请求参数: [{}]
2025-06-24 13:06:01.895 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592581175600615424[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:06:01.896 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592581175600615424[0;39m ------------- 结束 耗时：8 ms -------------
2025-06-24 13:06:52.641 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592581388469932032[0;39m ------------- 开始 -------------
2025-06-24 13:06:52.641 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592581388469932032[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:06:52.641 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592581388469932032[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:06:52.641 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592581388469932032[0;39m 远程地址: 127.0.0.1
2025-06-24 13:06:52.643 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592581388469932032[0;39m 请求参数: [{}]
2025-06-24 13:06:52.647 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592581388469932032[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:06:52.647 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592581388469932032[0;39m ------------- 结束 耗时：6 ms -------------
2025-06-24 13:06:53.595 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592581392471298048[0;39m ------------- 开始 -------------
2025-06-24 13:06:53.596 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592581392471298048[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:06:53.596 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592581392471298048[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:06:53.596 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592581392471298048[0;39m 远程地址: 127.0.0.1
2025-06-24 13:06:53.596 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592581392471298048[0;39m 请求参数: [{}]
2025-06-24 13:06:53.600 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592581392471298048[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:06:53.600 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592581392471298048[0;39m ------------- 结束 耗时：5 ms -------------
2025-06-24 13:06:57.100 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592581407172333568[0;39m ------------- 开始 -------------
2025-06-24 13:06:57.100 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592581407172333568[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:06:57.101 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592581407172333568[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:06:57.101 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592581407172333568[0;39m 远程地址: 127.0.0.1
2025-06-24 13:06:57.101 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592581407172333568[0;39m 请求参数: [{}]
2025-06-24 13:06:57.104 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592581407172333568[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:06:57.104 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592581407172333568[0;39m ------------- 结束 耗时：4 ms -------------
2025-06-24 13:06:58.781 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592581414222958592[0;39m ------------- 开始 -------------
2025-06-24 13:06:58.781 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592581414222958592[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:06:58.781 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592581414222958592[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:06:58.782 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592581414222958592[0;39m 远程地址: 127.0.0.1
2025-06-24 13:06:58.782 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592581414222958592[0;39m 请求参数: [{}]
2025-06-24 13:06:58.785 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592581414222958592[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:06:58.785 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592581414222958592[0;39m ------------- 结束 耗时：4 ms -------------
2025-06-24 13:07:00.413 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592581421068062720[0;39m ------------- 开始 -------------
2025-06-24 13:07:00.414 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592581421068062720[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:07:00.414 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592581421068062720[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:07:00.414 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592581421068062720[0;39m 远程地址: 127.0.0.1
2025-06-24 13:07:00.414 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592581421068062720[0;39m 请求参数: [{}]
2025-06-24 13:07:00.418 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592581421068062720[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:07:00.418 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592581421068062720[0;39m ------------- 结束 耗时：5 ms -------------
2025-06-24 13:07:06.941 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592581448448479232[0;39m ------------- 开始 -------------
2025-06-24 13:07:06.941 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592581448448479232[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:07:06.941 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592581448448479232[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:07:06.941 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592581448448479232[0;39m 远程地址: 127.0.0.1
2025-06-24 13:07:06.941 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592581448448479232[0;39m 请求参数: [{}]
2025-06-24 13:07:06.944 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592581448448479232[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:07:06.944 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592581448448479232[0;39m ------------- 结束 耗时：3 ms -------------
2025-06-24 13:07:08.236 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592581453880102912[0;39m ------------- 开始 -------------
2025-06-24 13:07:08.236 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592581453880102912[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:07:08.237 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592581453880102912[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:07:08.237 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592581453880102912[0;39m 远程地址: 127.0.0.1
2025-06-24 13:07:08.237 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592581453880102912[0;39m 请求参数: [{}]
2025-06-24 13:07:08.240 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592581453880102912[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:07:08.241 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592581453880102912[0;39m ------------- 结束 耗时：5 ms -------------
2025-06-24 13:38:50.360 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592589431966404608[0;39m ------------- 开始 -------------
2025-06-24 13:38:50.361 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592589431966404608[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:38:50.361 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592589431966404608[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:38:50.361 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592589431966404608[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 13:38:50.361 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592589431966404608[0;39m 请求参数: [{}]
2025-06-24 13:38:50.375 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592589431966404608[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:38:50.376 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592589431966404608[0;39m ------------- 结束 耗时：16 ms -------------
2025-06-24 13:38:51.661 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592589437423194112[0;39m ------------- 开始 -------------
2025-06-24 13:38:51.661 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592589437423194112[0;39m 请求地址: http://localhost:8080/getEbookByEbookReq GET
2025-06-24 13:38:51.661 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592589437423194112[0;39m 类名方法: com.gec.wiki.controller.EbookController.getEbookByEbookReq
2025-06-24 13:38:51.661 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592589437423194112[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 13:38:51.662 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592589437423194112[0;39m 请求参数: [{}]
2025-06-24 13:38:51.665 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592589437423194112[0;39m 返回结果: {"content":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"success":true}
2025-06-24 13:38:51.665 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592589437423194112[0;39m ------------- 结束 耗时：4 ms -------------
2025-06-24 13:40:57.398 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-24 13:40:57.400 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-24 13:41:05.174 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 29012 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-24 13:41:05.176 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-24 13:41:06.272 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-24 13:41:06.282 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 13:41:06.283 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-24 13:41:06.283 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-24 13:41:06.357 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-24 13:41:06.357 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1146 ms
2025-06-24 13:41:06.969 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-24 13:41:07.131 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 13:41:07.147 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-24 13:41:07.153 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 2.276 seconds (JVM running for 3.324)
2025-06-24 13:41:07.155 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-24 13:41:07.155 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-24 13:42:28.895 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 13:42:28.895 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-24 13:42:28.896 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-24 13:42:28.959 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592590348832870400[0;39m ------------- 开始 -------------
2025-06-24 13:42:28.960 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592590348832870400[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-24 13:42:28.960 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592590348832870400[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-24 13:42:28.961 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592590348832870400[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 13:42:29.063 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592590348832870400[0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-24 13:42:29.266 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m592590348832870400[0;39m {dataSource-1} inited
2025-06-24 13:42:29.591 INFO  com.gec.wiki.service.EbookService                 :60   [32m592590348832870400[0;39m 总行数：6
2025-06-24 13:42:29.592 INFO  com.gec.wiki.service.EbookService                 :61   [32m592590348832870400[0;39m 总页数：1
2025-06-24 13:42:29.612 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592590348832870400[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":6},"success":true}
2025-06-24 13:42:29.613 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592590348832870400[0;39m ------------- 结束 耗时：655 ms -------------
2025-06-24 13:42:32.957 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592590365605892096[0;39m ------------- 开始 -------------
2025-06-24 13:42:32.957 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592590365605892096[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-24 13:42:32.958 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592590365605892096[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-24 13:42:32.958 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592590365605892096[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 13:42:32.958 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592590365605892096[0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-24 13:42:32.965 INFO  com.gec.wiki.service.EbookService                 :60   [32m592590365605892096[0;39m 总行数：6
2025-06-24 13:42:32.965 INFO  com.gec.wiki.service.EbookService                 :61   [32m592590365605892096[0;39m 总页数：1
2025-06-24 13:42:32.966 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592590365605892096[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover1.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":6},"success":true}
2025-06-24 13:42:32.967 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592590365605892096[0;39m ------------- 结束 耗时：10 ms -------------
2025-06-24 13:42:35.017 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592590374246158336[0;39m ------------- 开始 -------------
2025-06-24 13:42:35.017 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592590374246158336[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-24 13:42:35.018 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592590374246158336[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-24 13:42:35.018 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592590374246158336[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 13:42:35.018 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592590374246158336[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-24 13:42:35.024 INFO  com.gec.wiki.service.EbookService                 :60   [32m592590374246158336[0;39m 总行数：6
2025-06-24 13:42:35.024 INFO  com.gec.wiki.service.EbookService                 :61   [32m592590374246158336[0;39m 总页数：3
2025-06-24 13:42:35.025 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592590374246158336[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-24 13:42:35.025 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592590374246158336[0;39m ------------- 结束 耗时：8 ms -------------
2025-06-24 13:45:57.064 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592591221688504320[0;39m ------------- 开始 -------------
2025-06-24 13:45:57.064 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592591221688504320[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-24 13:45:57.064 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592591221688504320[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-24 13:45:57.064 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592591221688504320[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 13:45:57.065 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592591221688504320[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-24 13:45:57.084 INFO  com.gec.wiki.service.EbookService                 :60   [32m592591221688504320[0;39m 总行数：6
2025-06-24 13:45:57.084 INFO  com.gec.wiki.service.EbookService                 :61   [32m592591221688504320[0;39m 总页数：3
2025-06-24 13:45:57.086 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592591221688504320[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-24 13:45:57.087 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592591221688504320[0;39m ------------- 结束 耗时：24 ms -------------
2025-06-24 13:48:51.208 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592591952105574400[0;39m ------------- 开始 -------------
2025-06-24 13:48:51.208 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592591952105574400[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-24 13:48:51.209 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592591952105574400[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-24 13:48:51.209 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592591952105574400[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-24 13:48:51.209 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592591952105574400[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-24 13:48:51.214 INFO  com.gec.wiki.service.EbookService                 :60   [32m592591952105574400[0;39m 总行数：6
2025-06-24 13:48:51.216 INFO  com.gec.wiki.service.EbookService                 :61   [32m592591952105574400[0;39m 总页数：3
2025-06-24 13:48:51.216 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592591952105574400[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-24 13:48:51.216 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592591952105574400[0;39m ------------- 结束 耗时：8 ms -------------
2025-06-24 13:51:00.357 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592592493795741696[0;39m ------------- 开始 -------------
2025-06-24 13:51:00.357 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592592493795741696[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-24 13:51:00.357 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592592493795741696[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-24 13:51:00.357 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592592493795741696[0;39m 远程地址: 127.0.0.1
2025-06-24 13:51:00.358 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592592493795741696[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-24 13:51:00.365 INFO  com.gec.wiki.service.EbookService                 :60   [32m592592493795741696[0;39m 总行数：6
2025-06-24 13:51:00.365 INFO  com.gec.wiki.service.EbookService                 :61   [32m592592493795741696[0;39m 总页数：3
2025-06-24 13:51:00.367 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592592493795741696[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-24 13:51:00.367 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592592493795741696[0;39m ------------- 结束 耗时：10 ms -------------
2025-06-24 13:52:33.480 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-24 13:52:33.483 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
