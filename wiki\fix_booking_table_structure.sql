-- 修复booking表结构，确保与代码匹配
-- 执行前请备份数据库！

USE car_service;

-- 检查并添加缺失的字段
-- 如果字段不存在，则添加；如果存在，MySQL会忽略错误

-- 添加预约时间字段
ALTER TABLE booking 
ADD COLUMN booking_time TIME NOT NULL DEFAULT '09:00:00' COMMENT '预约时间' 
AFTER booking_date;

-- 添加预约编号字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN booking_no VARCHAR(32) UNIQUE COMMENT '预约编号' 
AFTER id;

-- 添加用户ID字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN user_id BIGINT NOT NULL DEFAULT 1 COMMENT '用户ID' 
AFTER booking_no;

-- 添加车辆ID字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN vehicle_id BIGINT NOT NULL DEFAULT 1 COMMENT '车辆ID' 
AFTER user_id;

-- 添加服务ID字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN service_id BIGINT NOT NULL DEFAULT 1 COMMENT '服务ID' 
AFTER vehicle_id;

-- 添加技师ID字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN technician_id BIGINT COMMENT '技师ID' 
AFTER service_id;

-- 添加服务名称字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN service_name VARCHAR(50) NOT NULL DEFAULT '未知服务' COMMENT '服务名称' 
AFTER technician_id;

-- 添加服务价格字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN service_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '服务价格' 
AFTER service_name;

-- 添加联系人姓名字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN contact_name VARCHAR(50) NOT NULL DEFAULT '未知客户' COMMENT '联系人姓名' 
AFTER service_price;

-- 添加联系电话字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN contact_phone VARCHAR(20) NOT NULL DEFAULT '00000000000' COMMENT '联系电话' 
AFTER contact_name;

-- 添加预约日期字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN booking_date DATE NOT NULL DEFAULT (CURRENT_DATE) COMMENT '预约日期' 
AFTER contact_phone;

-- 添加预计时长字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN estimated_duration INT DEFAULT 60 COMMENT '预计时长(分钟)' 
AFTER booking_time;

-- 添加问题描述字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN problem_description TEXT COMMENT '问题描述' 
AFTER estimated_duration;

-- 添加备注字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN remark TEXT COMMENT '备注' 
AFTER problem_description;

-- 添加状态字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN status INT DEFAULT 1 COMMENT '状态(1-待确认 2-已确认 3-服务中 4-已完成 5-已取消)' 
AFTER remark;

-- 添加创建时间和更新时间字段（如果不存在）
ALTER TABLE booking 
ADD COLUMN create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' 
AFTER status;

ALTER TABLE booking 
ADD COLUMN update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' 
AFTER create_time;

-- 添加索引以提高查询性能
ALTER TABLE booking ADD INDEX idx_user_id (user_id);
ALTER TABLE booking ADD INDEX idx_vehicle_id (vehicle_id);
ALTER TABLE booking ADD INDEX idx_service_id (service_id);
ALTER TABLE booking ADD INDEX idx_technician_id (technician_id);
ALTER TABLE booking ADD INDEX idx_booking_date (booking_date);
ALTER TABLE booking ADD INDEX idx_status (status);

-- 更新现有数据的预约编号（如果为空）
UPDATE booking 
SET booking_no = CONCAT('B', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(id, 6, '0'))
WHERE booking_no IS NULL OR booking_no = '';

COMMIT;

-- 验证表结构
DESCRIBE booking;

SELECT 'booking表结构更新完成！' as message;
