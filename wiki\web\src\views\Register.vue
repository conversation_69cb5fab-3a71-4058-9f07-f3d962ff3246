<template>
  <div class="register-container">
    <div class="register-box">
      <div class="register-header">
        <img src="/images/logo.png" alt="汽车维修" class="logo" />
        <h2>注册汽车维修服务平台</h2>
        <p>加入我们，享受专业的汽车维修服务</p>
      </div>
      
      <a-form
        :model="registerForm"
        :rules="registerRules"
        @finish="handleRegister"
        layout="vertical"
        class="register-form"
      >
        <a-form-item name="username" label="用户名">
          <a-input
            v-model:value="registerForm.username"
            size="large"
            placeholder="请输入用户名"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item name="realName" label="真实姓名">
          <a-input
            v-model:value="registerForm.realName"
            size="large"
            placeholder="请输入真实姓名"
          >
            <template #prefix>
              <IdcardOutlined />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item name="phone" label="手机号">
          <a-input
            v-model:value="registerForm.phone"
            size="large"
            placeholder="请输入手机号"
          >
            <template #prefix>
              <PhoneOutlined />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item name="email" label="邮箱">
          <a-input
            v-model:value="registerForm.email"
            size="large"
            placeholder="请输入邮箱（可选）"
          >
            <template #prefix>
              <MailOutlined />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item name="userType" label="用户类型">
          <a-radio-group 
            v-model:value="registerForm.userType" 
            size="large" 
            class="user-type-radio"
          >
            <a-radio :value="1" class="radio-option">
              <div class="radio-content">
                <CarOutlined class="radio-icon" />
                <div class="radio-text">
                  <div class="radio-title">车主</div>
                  <div class="radio-desc">预约维修服务，管理车辆信息</div>
                </div>
              </div>
            </a-radio>
            <a-radio :value="2" class="radio-option">
              <div class="radio-content">
                <ShopOutlined class="radio-icon" />
                <div class="radio-text">
                  <div class="radio-title">维修店</div>
                  <div class="radio-desc">提供维修服务，管理技师和订单</div>
                </div>
              </div>
            </a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item name="password" label="密码">
          <a-input-password
            v-model:value="registerForm.password"
            size="large"
            placeholder="请输入密码"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>
        
        <a-form-item name="confirmPassword" label="确认密码">
          <a-input-password
            v-model:value="registerForm.confirmPassword"
            size="large"
            placeholder="请再次输入密码"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="agreeTerms">
            我已阅读并同意 <a href="#" @click="showTerms">《用户协议》</a> 和 <a href="#" @click="showPrivacy">《隐私政策》</a>
          </a-checkbox>
        </a-form-item>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            :disabled="!agreeTerms"
            class="register-button"
          >
            注册
          </a-button>
        </a-form-item>
        
        <div class="login-link">
          已有账户？ 
          <router-link to="/login">立即登录</router-link>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { 
  UserOutlined, 
  LockOutlined, 
  PhoneOutlined, 
  MailOutlined,
  IdcardOutlined,
  CarOutlined,
  ShopOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'Register',
  components: {
    UserOutlined,
    LockOutlined,
    PhoneOutlined,
    MailOutlined,
    IdcardOutlined,
    CarOutlined,
    ShopOutlined
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const agreeTerms = ref(false);

    const registerForm = ref({
      username: '',
      realName: '',
      phone: '',
      email: '',
      password: '',
      confirmPassword: '',
      userType: 1 // 默认选择车主
    });

    // 安全设置
    const securitySettings = ref({
      minPasswordLength: 6,
      requireComplexPassword: false
    });
    
    // 加载安全设置
    const loadSecuritySettings = async () => {
      try {
        const response = await axios.get('/api/admin/security-settings');
        if (response.data.success) {
          securitySettings.value = response.data.content;
        }
      } catch (error) {
        console.error('加载安全设置失败:', error);
      }
    };

    // 验证密码强度
    const validatePasswordStrength = async (rule: any, value: string) => {
      if (!value) {
        return Promise.resolve();
      }

      // 检查长度
      if (value.length < securitySettings.value.minPasswordLength) {
        return Promise.reject(`密码长度不能少于${securitySettings.value.minPasswordLength}位`);
      }

      // 检查复杂度
      if (securitySettings.value.requireComplexPassword) {
        const hasNumber = /\d/.test(value);
        const hasLetter = /[a-zA-Z]/.test(value);
        if (!hasNumber || !hasLetter) {
          return Promise.reject('密码必须包含数字和字母');
        }
      }

      return Promise.resolve();
    };

    const validatePassword = async (rule: any, value: string) => {
      if (value && value !== registerForm.value.password) {
        return Promise.reject('两次输入的密码不一致');
      }
      return Promise.resolve();
    };
    
    const registerRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
      ],
      realName: [
        { required: true, message: '请输入真实姓名', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      email: [
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      userType: [
        { required: true, message: '请选择用户类型', trigger: 'change' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { validator: validatePasswordStrength, trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        { validator: validatePassword, trigger: 'blur' }
      ]
    };
    
    const handleRegister = async () => {
      if (!agreeTerms.value) {
        message.warning('请先同意用户协议和隐私政策');
        return;
      }
      
      loading.value = true;
      try {
        const response = await axios.post('/auth/register', registerForm.value);
        const data = response.data;
        
        if (data.success) {
          message.success('注册成功，请登录');
          router.push('/login');
        } else {
          message.error(data.message || '注册失败');
        }
      } catch (error) {
        message.error('注册失败，请检查网络连接');
        console.error('Register error:', error);
      } finally {
        loading.value = false;
      }
    };
    
    const showTerms = () => {
      message.info('用户协议页面开发中...');
    };
    
    const showPrivacy = () => {
      message.info('隐私政策页面开发中...');
    };

    // 组件挂载时加载安全设置
    onMounted(() => {
      loadSecuritySettings();
    });

    return {
      registerForm,
      registerRules,
      loading,
      agreeTerms,
      securitySettings,
      handleRegister,
      showTerms,
      showPrivacy,
      validatePasswordStrength,
      validatePassword
    };
  }
});
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-box {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  max-height: 90vh;
  overflow-y: auto;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
}

.register-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
}

.register-header p {
  color: #666;
  margin-bottom: 0;
}

.register-form {
  margin-top: 32px;
}

.register-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
}

.login-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
}

.login-link a {
  color: #1890ff;
  text-decoration: none;
}

.login-link a:hover {
  text-decoration: underline;
}

.ant-checkbox-wrapper a {
  color: #1890ff;
  text-decoration: none;
}

.ant-checkbox-wrapper a:hover {
  text-decoration: underline;
}

/* 用户类型选择样式 */
.user-type-radio {
  width: 100%;
}

.radio-option {
  width: 100%;
  margin-bottom: 12px;
  padding: 16px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s;
  display: flex;
  align-items: flex-start;
}

.radio-option:hover {
  border-color: #1890ff;
  background-color: #f0f5ff;
}

.radio-option.ant-radio-wrapper-checked {
  border-color: #1890ff;
  background-color: #f0f5ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.radio-content {
  display: flex;
  align-items: center;
  width: 100%;
  margin-left: 8px;
}

.radio-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 12px;
}

.radio-text {
  flex: 1;
}

.radio-title {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.radio-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}
</style>
