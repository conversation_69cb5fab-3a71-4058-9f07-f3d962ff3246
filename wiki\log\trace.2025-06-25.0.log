2025-06-25 09:33:32.490 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 13944 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-25 09:33:32.493 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-25 09:33:33.574 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m <PERSON><PERSON> initialized with port(s): 8080 (http)
2025-06-25 09:33:33.582 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 09:33:33.583 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-25 09:33:33.583 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-25 09:33:33.652 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-25 09:33:33.653 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1128 ms
2025-06-25 09:33:34.293 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-25 09:33:34.464 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 09:33:34.478 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 09:33:34.486 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 2.308 seconds (JVM running for 4.759)
2025-06-25 09:33:34.488 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-25 09:33:34.488 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-25 09:40:52.216 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 09:40:52.217 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-25 09:40:52.218 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-25 09:40:52.273 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592891933190393856[0;39m ------------- 开始 -------------
2025-06-25 09:40:52.273 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592891933190393856[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 09:40:52.274 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592891933190393856[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 09:40:52.274 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592891933190393856[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 09:40:52.326 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592891933190393856[0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-25 09:40:52.457 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m592891933190393856[0;39m {dataSource-1} inited
2025-06-25 09:40:52.652 INFO  com.gec.wiki.service.EbookService                 :60   [32m592891933190393856[0;39m 总行数：6
2025-06-25 09:40:52.652 INFO  com.gec.wiki.service.EbookService                 :61   [32m592891933190393856[0;39m 总页数：1
2025-06-25 09:40:52.665 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592891933190393856[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":6},"success":true}
2025-06-25 09:40:52.666 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592891933190393856[0;39m ------------- 结束 耗时：394 ms -------------
2025-06-25 09:40:59.873 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592891965071298560[0;39m ------------- 开始 -------------
2025-06-25 09:40:59.874 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592891965071298560[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 09:40:59.874 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592891965071298560[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 09:40:59.874 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592891965071298560[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 09:40:59.874 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592891965071298560[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 09:40:59.883 INFO  com.gec.wiki.service.EbookService                 :60   [32m592891965071298560[0;39m 总行数：6
2025-06-25 09:40:59.883 INFO  com.gec.wiki.service.EbookService                 :61   [32m592891965071298560[0;39m 总页数：3
2025-06-25 09:40:59.884 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592891965071298560[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 09:40:59.884 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592891965071298560[0;39m ------------- 结束 耗时：11 ms -------------
2025-06-25 09:44:37.394 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592892877420498944[0;39m ------------- 开始 -------------
2025-06-25 09:44:37.394 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592892877420498944[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 09:44:37.394 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592892877420498944[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 09:44:37.394 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592892877420498944[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 09:44:37.395 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592892877420498944[0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-25 09:44:37.410 INFO  com.gec.wiki.service.EbookService                 :60   [32m592892877420498944[0;39m 总行数：6
2025-06-25 09:44:37.411 INFO  com.gec.wiki.service.EbookService                 :61   [32m592892877420498944[0;39m 总页数：1
2025-06-25 09:44:37.411 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592892877420498944[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":6},"success":true}
2025-06-25 09:44:37.411 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592892877420498944[0;39m ------------- 结束 耗时：17 ms -------------
2025-06-25 09:44:40.859 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592892891953762304[0;39m ------------- 开始 -------------
2025-06-25 09:44:40.859 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592892891953762304[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 09:44:40.859 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592892891953762304[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 09:44:40.859 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592892891953762304[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 09:44:40.859 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592892891953762304[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 09:44:40.866 INFO  com.gec.wiki.service.EbookService                 :60   [32m592892891953762304[0;39m 总行数：6
2025-06-25 09:44:40.866 INFO  com.gec.wiki.service.EbookService                 :61   [32m592892891953762304[0;39m 总页数：3
2025-06-25 09:44:40.869 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592892891953762304[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 09:44:40.869 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592892891953762304[0;39m ------------- 结束 耗时：10 ms -------------
2025-06-25 10:19:59.515 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592901778241097728[0;39m ------------- 开始 -------------
2025-06-25 10:19:59.516 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592901778241097728[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:19:59.517 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592901778241097728[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:19:59.518 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592901778241097728[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:19:59.518 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592901778241097728[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:19:59.538 INFO  com.gec.wiki.service.EbookService                 :60   [32m592901778241097728[0;39m 总行数：6
2025-06-25 10:19:59.538 INFO  com.gec.wiki.service.EbookService                 :61   [32m592901778241097728[0;39m 总页数：3
2025-06-25 10:19:59.538 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592901778241097728[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 10:19:59.539 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592901778241097728[0;39m ------------- 结束 耗时：24 ms -------------
2025-06-25 10:20:01.010 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592901784511582208[0;39m ------------- 开始 -------------
2025-06-25 10:20:01.010 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592901784511582208[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:20:01.010 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592901784511582208[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:20:01.011 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592901784511582208[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:20:01.011 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592901784511582208[0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-25 10:20:01.017 INFO  com.gec.wiki.service.EbookService                 :60   [32m592901784511582208[0;39m 总行数：6
2025-06-25 10:20:01.017 INFO  com.gec.wiki.service.EbookService                 :61   [32m592901784511582208[0;39m 总页数：1
2025-06-25 10:20:01.017 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592901784511582208[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":6},"success":true}
2025-06-25 10:20:01.017 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592901784511582208[0;39m ------------- 结束 耗时：7 ms -------------
2025-06-25 10:20:02.479 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592901790673014784[0;39m ------------- 开始 -------------
2025-06-25 10:20:02.479 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592901790673014784[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:20:02.480 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592901790673014784[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:20:02.480 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592901790673014784[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:20:02.480 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592901790673014784[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:20:02.486 INFO  com.gec.wiki.service.EbookService                 :60   [32m592901790673014784[0;39m 总行数：6
2025-06-25 10:20:02.487 INFO  com.gec.wiki.service.EbookService                 :61   [32m592901790673014784[0;39m 总页数：3
2025-06-25 10:20:02.487 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592901790673014784[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 10:20:02.487 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592901790673014784[0;39m ------------- 结束 耗时：8 ms -------------
2025-06-25 10:20:09.438 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592901819861176320[0;39m ------------- 开始 -------------
2025-06-25 10:20:09.438 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592901819861176320[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:20:09.438 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592901819861176320[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:20:09.439 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592901819861176320[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:20:09.439 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592901819861176320[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:20:09.448 INFO  com.gec.wiki.service.EbookService                 :60   [32m592901819861176320[0;39m 总行数：6
2025-06-25 10:20:09.448 INFO  com.gec.wiki.service.EbookService                 :61   [32m592901819861176320[0;39m 总页数：3
2025-06-25 10:20:09.449 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592901819861176320[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 10:20:09.449 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592901819861176320[0;39m ------------- 结束 耗时：11 ms -------------
2025-06-25 10:28:26.617 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592903905181044736[0;39m ------------- 开始 -------------
2025-06-25 10:28:26.617 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592903905181044736[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:28:26.617 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592903905181044736[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:28:26.617 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592903905181044736[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:28:26.618 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592903905181044736[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:28:26.624 INFO  com.gec.wiki.service.EbookService                 :60   [32m592903905181044736[0;39m 总行数：6
2025-06-25 10:28:26.624 INFO  com.gec.wiki.service.EbookService                 :61   [32m592903905181044736[0;39m 总页数：3
2025-06-25 10:28:26.625 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592903905181044736[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 10:28:26.625 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592903905181044736[0;39m ------------- 结束 耗时：8 ms -------------
2025-06-25 10:28:43.576 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592903976312246272[0;39m ------------- 开始 -------------
2025-06-25 10:28:43.577 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592903976312246272[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:28:43.577 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592903976312246272[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:28:43.577 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592903976312246272[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:28:43.577 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592903976312246272[0;39m 请求参数: [{"page":3,"size":2}]
2025-06-25 10:28:43.577 INFO  com.gec.wiki.service.EbookService                 :60   [32m592903976312246272[0;39m 总行数：6
2025-06-25 10:28:43.583 INFO  com.gec.wiki.service.EbookService                 :61   [32m592903976312246272[0;39m 总页数：3
2025-06-25 10:28:43.583 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592903976312246272[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":6},"success":true}
2025-06-25 10:28:43.584 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592903976312246272[0;39m ------------- 结束 耗时：8 ms -------------
2025-06-25 10:29:15.683 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592904110978764800[0;39m ------------- 开始 -------------
2025-06-25 10:29:15.683 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592904110978764800[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:29:15.684 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592904110978764800[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:29:15.684 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592904110978764800[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:29:15.684 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592904110978764800[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:29:15.689 INFO  com.gec.wiki.service.EbookService                 :60   [32m592904110978764800[0;39m 总行数：6
2025-06-25 10:29:15.689 INFO  com.gec.wiki.service.EbookService                 :61   [32m592904110978764800[0;39m 总页数：3
2025-06-25 10:29:15.690 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592904110978764800[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 10:29:15.690 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592904110978764800[0;39m ------------- 结束 耗时：7 ms -------------
2025-06-25 10:29:22.803 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592904140842209280[0;39m ------------- 开始 -------------
2025-06-25 10:29:22.804 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592904140842209280[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:29:22.804 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592904140842209280[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:29:22.804 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592904140842209280[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:29:22.804 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592904140842209280[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:29:22.809 INFO  com.gec.wiki.service.EbookService                 :60   [32m592904140842209280[0;39m 总行数：6
2025-06-25 10:29:22.809 INFO  com.gec.wiki.service.EbookService                 :61   [32m592904140842209280[0;39m 总页数：3
2025-06-25 10:29:22.810 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592904140842209280[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 10:29:22.810 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592904140842209280[0;39m ------------- 结束 耗时：7 ms -------------
2025-06-25 10:30:02.763 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592904308446597120[0;39m ------------- 开始 -------------
2025-06-25 10:30:02.763 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592904308446597120[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:30:02.763 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592904308446597120[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:30:02.763 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592904308446597120[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:30:02.763 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592904308446597120[0;39m 请求参数: [{"page":3,"size":2}]
2025-06-25 10:30:02.767 INFO  com.gec.wiki.service.EbookService                 :60   [32m592904308446597120[0;39m 总行数：6
2025-06-25 10:30:02.767 INFO  com.gec.wiki.service.EbookService                 :61   [32m592904308446597120[0;39m 总页数：3
2025-06-25 10:30:02.767 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592904308446597120[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":6},"success":true}
2025-06-25 10:30:02.767 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592904308446597120[0;39m ------------- 结束 耗时：4 ms -------------
2025-06-25 10:30:23.155 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592904393976844288[0;39m ------------- 开始 -------------
2025-06-25 10:30:23.155 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592904393976844288[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:30:23.155 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592904393976844288[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:30:23.155 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592904393976844288[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:30:23.155 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592904393976844288[0;39m 请求参数: [{"page":2,"size":2}]
2025-06-25 10:30:23.160 INFO  com.gec.wiki.service.EbookService                 :60   [32m592904393976844288[0;39m 总行数：6
2025-06-25 10:30:23.160 INFO  com.gec.wiki.service.EbookService                 :61   [32m592904393976844288[0;39m 总页数：3
2025-06-25 10:30:23.160 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592904393976844288[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2}],"total":6},"success":true}
2025-06-25 10:30:23.160 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592904393976844288[0;39m ------------- 结束 耗时：5 ms -------------
2025-06-25 10:30:25.869 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592904405360185344[0;39m ------------- 开始 -------------
2025-06-25 10:30:25.869 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592904405360185344[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:30:25.869 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592904405360185344[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:30:25.869 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592904405360185344[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:30:25.869 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592904405360185344[0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:30:25.871 INFO  com.gec.wiki.service.EbookService                 :60   [32m592904405360185344[0;39m 总行数：6
2025-06-25 10:30:25.871 INFO  com.gec.wiki.service.EbookService                 :61   [32m592904405360185344[0;39m 总页数：3
2025-06-25 10:30:25.871 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592904405360185344[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 10:30:25.871 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592904405360185344[0;39m ------------- 结束 耗时：2 ms -------------
2025-06-25 10:30:27.202 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m592904410951192576[0;39m ------------- 开始 -------------
2025-06-25 10:30:27.202 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m592904410951192576[0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:30:27.202 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m592904410951192576[0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:30:27.202 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m592904410951192576[0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:30:27.202 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m592904410951192576[0;39m 请求参数: [{"page":3,"size":2}]
2025-06-25 10:30:27.206 INFO  com.gec.wiki.service.EbookService                 :60   [32m592904410951192576[0;39m 总行数：6
2025-06-25 10:30:27.206 INFO  com.gec.wiki.service.EbookService                 :61   [32m592904410951192576[0;39m 总页数：3
2025-06-25 10:30:27.207 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m592904410951192576[0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":6},"success":true}
2025-06-25 10:30:27.207 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m592904410951192576[0;39m ------------- 结束 耗时：5 ms -------------
2025-06-25 10:31:12.885 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-25 10:31:12.886 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-06-25 10:31:17.170 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 19160 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-25 10:31:17.172 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-25 10:31:17.983 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-25 10:31:17.990 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-25 10:31:17.991 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-25 10:31:17.991 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-25 10:31:18.048 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-25 10:31:18.049 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 846 ms
2025-06-25 10:31:18.591 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-25 10:31:18.774 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-25 10:31:18.790 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-25 10:31:18.799 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.921 seconds (JVM running for 2.522)
2025-06-25 10:31:18.800 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-25 10:31:18.801 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-25 10:32:30.367 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 10:32:30.367 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-25 10:32:30.369 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-06-25 10:32:30.407 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632069747737632  [0;39m ------------- 开始 -------------
2025-06-25 10:32:30.408 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632069747737632  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:32:30.408 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632069747737632  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:32:30.409 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632069747737632  [0;39m 远程地址: 127.0.0.1
2025-06-25 10:32:30.472 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632069747737632  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-25 10:32:30.653 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4632069747737632  [0;39m {dataSource-1} inited
2025-06-25 10:32:30.878 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632069747737632  [0;39m 总行数：6
2025-06-25 10:32:30.878 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632069747737632  [0;39m 总页数：1
2025-06-25 10:32:30.892 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632069747737632  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":6},"success":true}
2025-06-25 10:32:30.893 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632069747737632  [0;39m ------------- 结束 耗时：486 ms -------------
2025-06-25 10:32:33.865 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632069861049376  [0;39m ------------- 开始 -------------
2025-06-25 10:32:33.865 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632069861049376  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:32:33.865 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632069861049376  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:32:33.865 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632069861049376  [0;39m 远程地址: 127.0.0.1
2025-06-25 10:32:33.865 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632069861049376  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:32:33.872 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632069861049376  [0;39m 总行数：6
2025-06-25 10:32:33.872 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632069861049376  [0;39m 总页数：3
2025-06-25 10:32:33.873 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632069861049376  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":6},"success":true}
2025-06-25 10:32:33.873 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632069861049376  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-25 10:32:41.599 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632070114477088  [0;39m ------------- 开始 -------------
2025-06-25 10:32:41.599 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632070114477088  [0;39m 请求地址: http://localhost:8080/save POST
2025-06-25 10:32:41.599 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632070114477088  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-25 10:32:41.599 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632070114477088  [0;39m 远程地址: 127.0.0.1
2025-06-25 10:32:41.602 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632070114477088  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸0","viewCount":744,"voteCount":484}]
2025-06-25 10:32:41.620 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632070114477088  [0;39m 返回结果: {"message":"添加成功","success":true}
2025-06-25 10:32:41.621 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632070114477088  [0;39m ------------- 结束 耗时：22 ms -------------
2025-06-25 10:32:41.638 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632070115755040  [0;39m ------------- 开始 -------------
2025-06-25 10:32:41.638 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632070115755040  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:32:41.638 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632070115755040  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:32:41.638 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632070115755040  [0;39m 远程地址: 127.0.0.1
2025-06-25 10:32:41.638 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632070115755040  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:32:41.643 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632070115755040  [0;39m 总行数：7
2025-06-25 10:32:41.644 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632070115755040  [0;39m 总页数：4
2025-06-25 10:32:41.644 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632070115755040  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-06-25 10:32:41.644 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632070115755040  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-25 10:32:49.278 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632070366102560  [0;39m ------------- 开始 -------------
2025-06-25 10:32:49.279 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632070366102560  [0;39m 请求地址: http://localhost:8080/save POST
2025-06-25 10:32:49.279 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632070366102560  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-25 10:32:49.279 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632070366102560  [0;39m 远程地址: 127.0.0.1
2025-06-25 10:32:49.279 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632070366102560  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸1","viewCount":744,"voteCount":484}]
2025-06-25 10:32:49.284 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632070366102560  [0;39m 返回结果: {"message":"添加成功","success":true}
2025-06-25 10:32:49.284 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632070366102560  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-25 10:32:49.301 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632070366856224  [0;39m ------------- 开始 -------------
2025-06-25 10:32:49.302 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632070366856224  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:32:49.302 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632070366856224  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:32:49.302 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632070366856224  [0;39m 远程地址: 127.0.0.1
2025-06-25 10:32:49.302 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632070366856224  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:32:49.307 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632070366856224  [0;39m 总行数：8
2025-06-25 10:32:49.307 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632070366856224  [0;39m 总页数：4
2025-06-25 10:32:49.308 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632070366856224  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":8},"success":true}
2025-06-25 10:32:49.308 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632070366856224  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-25 10:32:51.660 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632070444155936  [0;39m ------------- 开始 -------------
2025-06-25 10:32:51.660 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632070444155936  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:32:51.660 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632070444155936  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:32:51.660 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632070444155936  [0;39m 远程地址: 127.0.0.1
2025-06-25 10:32:51.660 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632070444155936  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-25 10:32:51.664 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632070444155936  [0;39m 总行数：8
2025-06-25 10:32:51.664 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632070444155936  [0;39m 总页数：4
2025-06-25 10:32:51.664 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632070444155936  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸1","viewCount":744,"voteCount":484}],"total":8},"success":true}
2025-06-25 10:32:51.664 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632070444155936  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-25 10:32:53.250 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632070496257056  [0;39m ------------- 开始 -------------
2025-06-25 10:32:53.252 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632070496257056  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:32:53.252 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632070496257056  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:32:53.252 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632070496257056  [0;39m 远程地址: 127.0.0.1
2025-06-25 10:32:53.252 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632070496257056  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-25 10:32:53.255 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632070496257056  [0;39m 总行数：8
2025-06-25 10:32:53.255 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632070496257056  [0;39m 总页数：4
2025-06-25 10:32:53.255 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632070496257056  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"}],"total":8},"success":true}
2025-06-25 10:32:53.255 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632070496257056  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-25 10:32:54.724 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632070544557088  [0;39m ------------- 开始 -------------
2025-06-25 10:32:54.725 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632070544557088  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:32:54.725 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632070544557088  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:32:54.725 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632070544557088  [0;39m 远程地址: 127.0.0.1
2025-06-25 10:32:54.725 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632070544557088  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:32:54.727 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632070544557088  [0;39m 总行数：8
2025-06-25 10:32:54.727 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632070544557088  [0;39m 总页数：4
2025-06-25 10:32:54.727 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632070544557088  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":8},"success":true}
2025-06-25 10:32:54.727 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632070544557088  [0;39m ------------- 结束 耗时：3 ms -------------
2025-06-25 10:34:16.770 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632073233040416  [0;39m ------------- 开始 -------------
2025-06-25 10:34:16.770 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632073233040416  [0;39m 请求地址: http://localhost:8080/save POST
2025-06-25 10:34:16.770 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632073233040416  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-25 10:34:16.770 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632073233040416  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:34:16.771 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632073233040416  [0;39m 请求参数: [{"category1Id":6,"category2Id":6,"description":"6","name":"6"}]
2025-06-25 10:34:16.787 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632073233040416  [0;39m 返回结果: {"message":"添加成功","success":true}
2025-06-25 10:34:16.787 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632073233040416  [0;39m ------------- 结束 耗时：17 ms -------------
2025-06-25 10:34:16.803 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632073234121760  [0;39m ------------- 开始 -------------
2025-06-25 10:34:16.803 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632073234121760  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:34:16.803 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632073234121760  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:34:16.803 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632073234121760  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:34:16.803 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632073234121760  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:34:16.809 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632073234121760  [0;39m 总行数：9
2025-06-25 10:34:16.809 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632073234121760  [0;39m 总页数：5
2025-06-25 10:34:16.810 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632073234121760  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":9},"success":true}
2025-06-25 10:34:16.810 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632073234121760  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-25 10:34:19.236 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632073313846304  [0;39m ------------- 开始 -------------
2025-06-25 10:34:19.237 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632073313846304  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:34:19.237 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632073313846304  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:34:19.240 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632073313846304  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:34:19.240 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632073313846304  [0;39m 请求参数: [{"page":5,"size":2}]
2025-06-25 10:34:19.244 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632073313846304  [0;39m 总行数：9
2025-06-25 10:34:19.244 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632073313846304  [0;39m 总页数：5
2025-06-25 10:34:19.244 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632073313846304  [0;39m 返回结果: {"content":{"list":[{"category1Id":6,"category2Id":6,"description":"6","name":"6"}],"total":9},"success":true}
2025-06-25 10:34:19.245 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632073313846304  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-25 10:34:24.363 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632073481847840  [0;39m ------------- 开始 -------------
2025-06-25 10:34:24.364 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632073481847840  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:34:24.364 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632073481847840  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:34:24.364 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632073481847840  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:34:24.364 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632073481847840  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-25 10:34:24.368 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632073481847840  [0;39m 总行数：9
2025-06-25 10:34:24.368 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632073481847840  [0;39m 总页数：5
2025-06-25 10:34:24.368 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632073481847840  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸1","viewCount":744,"voteCount":484}],"total":9},"success":true}
2025-06-25 10:34:24.369 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632073481847840  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-25 10:42:17.158 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632088974394400  [0;39m ------------- 开始 -------------
2025-06-25 10:42:17.159 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632088974394400  [0;39m 请求地址: http://localhost:8080/save POST
2025-06-25 10:42:17.159 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632088974394400  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-25 10:42:17.159 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632088974394400  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:42:17.160 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632088974394400  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸012","viewCount":744,"voteCount":484}]
2025-06-25 10:42:17.168 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632088974394400  [0;39m 返回结果: {"message":"添加成功","success":true}
2025-06-25 10:42:17.168 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632088974394400  [0;39m ------------- 结束 耗时：10 ms -------------
2025-06-25 10:42:17.182 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632088975180832  [0;39m ------------- 开始 -------------
2025-06-25 10:42:17.182 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632088975180832  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:42:17.183 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632088975180832  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:42:17.183 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632088975180832  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:42:17.183 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632088975180832  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-25 10:42:17.188 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632088975180832  [0;39m 总行数：10
2025-06-25 10:42:17.188 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632088975180832  [0;39m 总页数：5
2025-06-25 10:42:17.188 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632088975180832  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸1","viewCount":744,"voteCount":484}],"total":10},"success":true}
2025-06-25 10:42:17.189 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632088975180832  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-25 10:42:18.707 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632089025152032  [0;39m ------------- 开始 -------------
2025-06-25 10:42:18.707 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632089025152032  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:42:18.707 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632089025152032  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:42:18.708 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632089025152032  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:42:18.708 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632089025152032  [0;39m 请求参数: [{"page":5,"size":2}]
2025-06-25 10:42:18.713 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632089025152032  [0;39m 总行数：10
2025-06-25 10:42:18.713 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632089025152032  [0;39m 总页数：5
2025-06-25 10:42:18.714 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632089025152032  [0;39m 返回结果: {"content":{"list":[{"category1Id":6,"category2Id":6,"description":"6","name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":10},"success":true}
2025-06-25 10:42:18.714 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632089025152032  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-25 10:42:23.718 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632089189352480  [0;39m ------------- 开始 -------------
2025-06-25 10:42:23.718 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632089189352480  [0;39m 请求地址: http://localhost:8080/save POST
2025-06-25 10:42:23.718 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632089189352480  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-25 10:42:23.718 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632089189352480  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:42:23.718 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632089189352480  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸012","viewCount":744,"voteCount":484}]
2025-06-25 10:42:23.729 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632089189352480  [0;39m 返回结果: {"message":"添加成功","success":true}
2025-06-25 10:42:23.729 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632089189352480  [0;39m ------------- 结束 耗时：11 ms -------------
2025-06-25 10:42:23.743 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632089190171680  [0;39m ------------- 开始 -------------
2025-06-25 10:42:23.744 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632089190171680  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:42:23.744 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632089190171680  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:42:23.744 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632089190171680  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:42:23.744 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632089190171680  [0;39m 请求参数: [{"page":5,"size":2}]
2025-06-25 10:42:23.750 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632089190171680  [0;39m 总行数：11
2025-06-25 10:42:23.750 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632089190171680  [0;39m 总页数：6
2025-06-25 10:42:23.750 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632089190171680  [0;39m 返回结果: {"content":{"list":[{"category1Id":6,"category2Id":6,"description":"6","name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":11},"success":true}
2025-06-25 10:42:23.750 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632089190171680  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-25 10:42:25.522 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632089248465952  [0;39m ------------- 开始 -------------
2025-06-25 10:42:25.522 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632089248465952  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:42:25.522 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632089248465952  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:42:25.522 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632089248465952  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:42:25.522 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632089248465952  [0;39m 请求参数: [{"page":6,"size":2}]
2025-06-25 10:42:25.526 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632089248465952  [0;39m 总行数：11
2025-06-25 10:42:25.527 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632089248465952  [0;39m 总页数：6
2025-06-25 10:42:25.527 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632089248465952  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":11},"success":true}
2025-06-25 10:42:25.527 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632089248465952  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-25 10:42:26.794 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632089290146848  [0;39m ------------- 开始 -------------
2025-06-25 10:42:26.794 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632089290146848  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:42:26.794 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632089290146848  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:42:26.794 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632089290146848  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:42:26.795 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632089290146848  [0;39m 请求参数: [{"page":5,"size":2}]
2025-06-25 10:42:26.797 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632089290146848  [0;39m 总行数：11
2025-06-25 10:42:26.798 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632089290146848  [0;39m 总页数：6
2025-06-25 10:42:26.798 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632089290146848  [0;39m 返回结果: {"content":{"list":[{"category1Id":6,"category2Id":6,"description":"6","name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":11},"success":true}
2025-06-25 10:42:26.798 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632089290146848  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-25 10:50:36.974 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632105352365088  [0;39m ------------- 开始 -------------
2025-06-25 10:50:36.975 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632105352365088  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:50:36.975 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632105352365088  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:50:36.975 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632105352365088  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:50:36.975 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632105352365088  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-25 10:50:36.983 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632105352365088  [0;39m 总行数：11
2025-06-25 10:50:36.984 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632105352365088  [0;39m 总页数：1
2025-06-25 10:50:36.984 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632105352365088  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover2.png","description":"虎虎虎","name":"虎鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸0","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸1","viewCount":744,"voteCount":484},{"category1Id":6,"category2Id":6,"description":"6","name":"6"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸012","viewCount":744,"voteCount":484}],"total":11},"success":true}
2025-06-25 10:50:36.985 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632105352365088  [0;39m ------------- 结束 耗时：11 ms -------------
2025-06-25 10:54:08.932 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632112297804832  [0;39m ------------- 开始 -------------
2025-06-25 10:54:08.932 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632112297804832  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:54:08.932 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632112297804832  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:54:08.932 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632112297804832  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:54:08.932 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632112297804832  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:54:08.937 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632112297804832  [0;39m 总行数：11
2025-06-25 10:54:08.937 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632112297804832  [0;39m 总页数：6
2025-06-25 10:54:08.937 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632112297804832  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":11},"success":true}
2025-06-25 10:54:08.938 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632112297804832  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-25 10:54:12.170 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632112403907616  [0;39m ------------- 开始 -------------
2025-06-25 10:54:12.171 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632112403907616  [0;39m 请求地址: http://localhost:8080/save POST
2025-06-25 10:54:12.171 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632112403907616  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-25 10:54:12.171 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632112403907616  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:54:12.171 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632112403907616  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484}]
2025-06-25 10:54:12.180 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632112403907616  [0;39m 返回结果: {"message":"添加成功","success":true}
2025-06-25 10:54:12.181 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632112403907616  [0;39m ------------- 结束 耗时：11 ms -------------
2025-06-25 10:54:12.194 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632112404694048  [0;39m ------------- 开始 -------------
2025-06-25 10:54:12.194 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632112404694048  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 10:54:12.194 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632112404694048  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 10:54:12.194 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632112404694048  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 10:54:12.195 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632112404694048  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 10:54:12.199 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632112404694048  [0;39m 总行数：12
2025-06-25 10:54:12.199 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632112404694048  [0;39m 总页数：6
2025-06-25 10:54:12.200 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632112404694048  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":12},"success":true}
2025-06-25 10:54:12.200 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632112404694048  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-25 11:01:23.311 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632126531535904  [0;39m ------------- 开始 -------------
2025-06-25 11:01:23.311 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632126531535904  [0;39m 请求地址: http://localhost:8080/save POST
2025-06-25 11:01:23.312 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632126531535904  [0;39m 类名方法: com.gec.wiki.controller.EbookController.save
2025-06-25 11:01:23.312 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632126531535904  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 11:01:23.312 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632126531535904  [0;39m 请求参数: [{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸0","viewCount":744,"voteCount":484}]
2025-06-25 11:01:23.322 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632126531535904  [0;39m 返回结果: {"message":"添加成功","success":true}
2025-06-25 11:01:23.322 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632126531535904  [0;39m ------------- 结束 耗时：11 ms -------------
2025-06-25 11:01:23.336 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632126532355104  [0;39m ------------- 开始 -------------
2025-06-25 11:01:23.336 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632126532355104  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 11:01:23.336 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632126532355104  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 11:01:23.336 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632126532355104  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 11:01:23.337 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632126532355104  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 11:01:23.340 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632126532355104  [0;39m 总行数：13
2025-06-25 11:01:23.340 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632126532355104  [0;39m 总页数：7
2025-06-25 11:01:23.340 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632126532355104  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-25 11:01:23.340 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632126532355104  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-25 11:01:29.532 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632126735385632  [0;39m ------------- 开始 -------------
2025-06-25 11:01:29.532 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632126735385632  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 11:01:29.532 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632126735385632  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 11:01:29.532 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632126735385632  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 11:01:29.532 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632126735385632  [0;39m 请求参数: [{"page":2,"size":2}]
2025-06-25 11:01:29.536 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632126735385632  [0;39m 总行数：13
2025-06-25 11:01:29.536 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632126735385632  [0;39m 总页数：7
2025-06-25 11:01:29.536 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632126735385632  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"name":"海神草","viewCount":6,"voteCount":2}],"total":13},"success":true}
2025-06-25 11:01:29.536 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632126735385632  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-25 11:01:30.365 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632126762681376  [0;39m ------------- 开始 -------------
2025-06-25 11:01:30.366 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632126762681376  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 11:01:30.366 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632126762681376  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 11:01:30.366 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632126762681376  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 11:01:30.366 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632126762681376  [0;39m 请求参数: [{"page":7,"size":2}]
2025-06-25 11:01:30.369 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632126762681376  [0;39m 总行数：13
2025-06-25 11:01:30.370 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632126762681376  [0;39m 总页数：7
2025-06-25 11:01:30.370 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632126762681376  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸0","viewCount":744,"voteCount":484}],"total":13},"success":true}
2025-06-25 11:01:30.370 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632126762681376  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-25 11:01:33.004 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4632126849156128  [0;39m ------------- 开始 -------------
2025-06-25 11:01:33.004 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4632126849156128  [0;39m 请求地址: http://localhost:8080/getebookListByPage GET
2025-06-25 11:01:33.004 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4632126849156128  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-25 11:01:33.004 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4632126849156128  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-25 11:01:33.004 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4632126849156128  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-25 11:01:33.007 INFO  com.gec.wiki.service.EbookService                 :59   [32m4632126849156128  [0;39m 总行数：13
2025-06-25 11:01:33.007 INFO  com.gec.wiki.service.EbookService                 :60   [32m4632126849156128  [0;39m 总页数：7
2025-06-25 11:01:33.008 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4632126849156128  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"name":"海藻","viewCount":13,"voteCount":4}],"total":13},"success":true}
2025-06-25 11:01:33.008 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4632126849156128  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-25 11:33:18.912 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-25 11:33:18.912 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
