# 测试登录锁定功能
$uri = "http://localhost:8880/auth/login"
$body = @{
    username = "testuser"
    password = "wrong_password"
} | ConvertTo-Json

Write-Host "测试testuser用户登录（应该不受其他用户锁定影响）..."
Write-Host "=" * 50

for ($i = 1; $i -le 3; $i++) {
    Write-Host ""
    Write-Host "第 $i 次尝试登录..."
    
    try {
        $response = Invoke-WebRequest -Uri $uri -Method POST -Body $body -ContentType "application/json"
        $result = $response.Content | ConvertFrom-Json
        
        Write-Host "响应状态码: $($response.StatusCode)"
        Write-Host "响应内容: $($response.Content)"
        
        if (-not $result.success) {
            $message = $result.message
            Write-Host "登录失败: $message"
            
            if ($message -like "*连续错误*") {
                Write-Host "✅ 失败次数显示正常"
            } elseif ($message -like "*已被锁定*") {
                Write-Host "🔒 账号已被锁定"
                break
            }
        } else {
            Write-Host "✅ 登录成功"
            break
        }
    } catch {
        Write-Host "❌ 请求异常: $($_.Exception.Message)"
        break
    }
    
    if ($i -lt 5) {
        Start-Sleep -Seconds 1
    }
}

Write-Host ""
Write-Host "=" * 50
Write-Host "测试完成"
