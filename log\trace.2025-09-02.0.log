2025-09-02 14:26:32.172 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 6084 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 14:26:32.176 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 14:26:33.575 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-09-02 14:26:33.584 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-09-02 14:26:33.585 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 14:26:33.586 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 14:26:33.684 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 14:26:33.685 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1454 ms
2025-09-02 14:26:35.395 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 14:26:35.607 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-09-02 14:26:35.648 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-09-02 14:26:35.656 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.006 seconds (JVM running for 5.367)
2025-09-02 14:26:35.659 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-09-02 14:26:35.659 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-09-02 14:27:03.344 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02 14:27:03.344 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-02 14:27:03.345 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-09-02 14:27:03.415 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4827880600831008  [0;39m ------------- 开始 -------------
2025-09-02 14:27:03.417 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4827880600831008  [0;39m 请求地址: http://localhost:8080/category/allList GET
2025-09-02 14:27:03.417 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4827880600929312  [0;39m ------------- 开始 -------------
2025-09-02 14:27:03.417 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4827880600831008  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.allList
2025-09-02 14:27:03.417 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4827880600929312  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-02 14:27:03.417 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4827880600929312  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-02 14:27:03.417 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4827880600831008  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-02 14:27:03.417 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4827880600929312  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-02 14:27:03.488 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4827880600831008  [0;39m 请求参数: [{}]
2025-09-02 14:27:03.488 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4827880600929312  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-02 14:27:03.624 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4827880600831008  [0;39m {dataSource-1} inited
2025-09-02 14:27:04.037 INFO  com.gec.wiki.service.EbookService                 :72   [32m4827880600929312  [0;39m 总行数：19
2025-09-02 14:27:04.038 INFO  com.gec.wiki.service.EbookService                 :73   [32m4827880600929312  [0;39m 总页数：1
2025-09-02 14:27:04.043 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4827880600831008  [0;39m 返回结果: {"content":[{"id":211,"name":"海洋动物","parent":0,"sort":1},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1},{"id":215,"name":"海洋鱼类","parent":211,"sort":1},{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":200,"name":"微生物","parent":0,"sort":3},{"id":214,"name":"海洋底栖动物","parent":211,"sort":3},{"id":216,"name":"哺乳类海洋生物","parent":0,"sort":100},{"id":101,"name":"藻类植物","parent":100,"sort":101},{"id":221,"name":"鲸类","parent":100,"sort":101},{"id":102,"name":"红树林","parent":100,"sort":102},{"id":222,"name":"海豚类","parent":100,"sort":102},{"id":223,"name":"海豹和海狮","parent":100,"sort":103},{"id":217,"name":"鱼类","parent":0,"sort":200},{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":224,"name":"软骨鱼类","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202},{"id":225,"name":"硬骨鱼类","parent":200,"sort":202},{"id":226,"name":"深海鱼类","parent":200,"sort":203},{"id":218,"name":"无脊椎动物","parent":0,"sort":300},{"id":227,"name":"甲壳类","parent":300,"sort":301},{"id":228,"name":"软体动物","parent":300,"sort":302},{"id":229,"name":"棘皮动物","parent":300,"sort":303},{"id":219,"name":"爬行类海洋生物","parent":0,"sort":400},{"id":230,"name":"海龟","parent":400,"sort":401},{"id":231,"name":"海蛇","parent":400,"sort":402},{"id":220,"name":"珊瑚和海绵","parent":100,"sort":500},{"id":232,"name":"硬珊瑚","parent":500,"sort":501},{"id":233,"name":"软珊瑚","parent":500,"sort":502},{"id":234,"name":"海绵类","parent":500,"sort":503}],"success":true}
2025-09-02 14:27:04.044 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4827880600831008  [0;39m ------------- 结束 耗时：630 ms -------------
2025-09-02 14:27:04.052 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4827880600929312  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-02 14:27:04.052 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4827880600929312  [0;39m ------------- 结束 耗时：635 ms -------------
2025-09-02 14:27:04.198 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4827880626521120  [0;39m ------------- 开始 -------------
2025-09-02 14:27:04.198 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4827880626521120  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-09-02 14:27:04.198 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4827880626521120  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-09-02 14:27:04.199 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4827880626521120  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-02 14:27:04.199 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4827880626521120  [0;39m 请求参数: [{"category2Id":0,"page":1,"size":1000}]
2025-09-02 14:27:04.209 INFO  com.gec.wiki.service.EbookService                 :72   [32m4827880626521120  [0;39m 总行数：19
2025-09-02 14:27:04.209 INFO  com.gec.wiki.service.EbookService                 :73   [32m4827880626521120  [0;39m 总页数：1
2025-09-02 14:27:04.211 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4827880626521120  [0;39m 返回结果: {"content":{"list":[{"category1Id":100,"category2Id":101,"cover":"/image/海藻.png","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/双生水母.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/海神草.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"description":"44与5有","id":4658137268192288,"name":"yu"},{"category1Id":211,"category2Id":213,"cover":"/image/虎鲸.jpg","description":"虎鲸很凶猛，海中肉食动物","docCount":3,"id":595151271078531073,"name":"虎鲸","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/蓝鲸.jpg","description":"蓝鲸是地球上最大的动物，成年蓝鲸平均体长为24-27米，体重可达150吨。它们主要以磷虾为食，每天可以摄入约4吨食物。尽管体型庞大，但蓝鲸却是以过滤饵食的方式进食，被列为濒危物种。","docCount":5,"id":595531949385322501,"name":"蓝鲸","viewCount":120,"voteCount":30},{"category1Id":100,"category2Id":101,"cover":"/image/座头鲸.png","description":"座头鲸是一种大型鲸类，成年座头鲸体长可达12-16米，体重约25-30吨。它们以其复杂的歌声和令人惊叹的跃出水面的行为而闻名。座头鲸每年都会进行长距离迁徙，从热带繁殖区到极地觅食区。","docCount":4,"id":595531949385322502,"name":"座头鲸","viewCount":98,"voteCount":25},{"category1Id":100,"category2Id":101,"cover":"/image/抹香鲸.jpg","description":"抹香鲸是一种大型齿鲸，拥有世界上最大的大脑。它们可以潜入深达2000米的海洋深处，猎捕大型鱿鱼。抹香鲸因其头部的鲸蜡而闻名，曾被广泛猎杀用于制造香水和油脂。","docCount":3,"id":595531949385322503,"name":"抹香鲸","viewCount":85,"voteCount":20},{"category1Id":100,"category2Id":102,"cover":"/image/瓶鼻海豚.jpg","description":"瓶鼻海豚是最常见和最著名的海豚种类之一。它们高度智能，具有复杂的社会结构和自我意识。瓶鼻海豚使用回声定位来寻找食物和导航，能够识别各种物体和形状。","docCount":4,"id":595531949385322504,"name":"瓶鼻海豚","viewCount":150,"voteCount":45},{"category1Id":100,"category2Id":102,"cover":"/image/虎鲸.jpg","description":"虎鲸，也被称为逆戟鲸，实际上是海豚家族中最大的成员。它们是顶级掠食者，以其高智商和协作狩猎技能著称。虎鲸有着复杂的社会结构，家族群体可以世代传承特定的狩猎技术和文化行为。","docCount":5,"id":595531949385322505,"name":"虎鲸","viewCount":180,"voteCount":60},{"category1Id":200,"category2Id":201,"cover":"/image/大白鲨.jpg","description":"大白鲨是海洋中最著名的掠食者之一，可长达6米，重达2吨。它们拥有锋利的锯齿状牙齿，可以感知水中极微小的电场变化来定位猎物。尽管常被误解为嗜血的怪物，但大白鲨对维持海洋生态平衡起着重要作用。","docCount":6,"id":595531949385322506,"name":"大白鲨","viewCount":200,"voteCount":80},{"category1Id":200,"category2Id":201,"cover":"/image/蝠鲼.jpg","description":"蝠鲼，也被称为魔鬼鱼，是一种巨大的蝠鲼科鱼类，它们的鳍展可达7米宽。尽管体型庞大，但它们主要以浮游生物为食，是温和的海洋巨人。蝠鲼以优雅的\"飞行\"姿态在海中游弋而闻名。","docCount":3,"id":595531949385322507,"name":"蝠鲼","viewCount":90,"voteCount":30},{"category1Id":300,"category2Id":302,"cover":"/image/海神草.png","description":"巨型乌贼是地球上最大的无脊椎动物，体长可达13米以上。它们生活在深海中，拥有地球上最大的眼睛，直径可达25厘米，有助于在深海黑暗环境中捕食。这些神秘的生物很少被活体观察到，大部分知识来自于搁浅标本。","docCount":4,"id":595531949385322508,"name":"巨型乌贼","viewCount":120,"voteCount":40},{"category1Id":300,"category2Id":302,"cover":"/image/蓝环章鱼.jpg","description":"蓝环章鱼是世界上最毒的海洋生物之一，尽管体型仅有12-20厘米。它们的唾液腺含有致命的河豚毒素，没有已知的解毒剂。当受到威胁时，其皮肤上的明亮蓝环会更加鲜艳，作为警告信号。","docCount":5,"id":595531949385322509,"name":"蓝环章鱼","viewCount":150,"voteCount":35},{"category1Id":400,"category2Id":401,"cover":"/image/绿海龟.jpg","description":"绿海龟是一种大型海龟，以其绿色的脂肪而得名。它们主要是草食性的，以海草和海藻为食。绿海龟可以在水下停留数小时，并能进行长距离的洄游，从觅食区域返回出生的海滩产卵。","docCount":4,"id":595531949385322510,"name":"绿海龟","viewCount":110,"voteCount":28},{"category1Id":500,"category2Id":501,"cover":"/image/鹿角珊瑚.jpg","description":"鹿角珊瑚是珊瑚礁生态系统中的关键物种，以其分枝状结构类似鹿角而得名。它们是重要的栖息地构建者，为无数海洋生物提供庇护所和繁殖场所。鹿角珊瑚可能是当今海洋中生长最快的珊瑚类型之一。","docCount":3,"id":595531949385322511,"name":"鹿角珊瑚","viewCount":80,"voteCount":15},{"category1Id":300,"category2Id":302,"cover":"/image/水母.jpg","description":"水母是一种古老的海洋生物，已存在至少6.5亿年。它们的身体由95%的水组成，没有大脑、心脏或骨骼，却拥有高效的神经网络。一些水母种类能够发光，这种生物发光现象帮助它们吸引猎物或吓退捕食者。","docCount":5,"id":595531949385322512,"name":"水母","viewCount":130,"voteCount":42},{"category1Id":500,"category2Id":503,"cover":"/image/海神草.png","description":"海神草是一种生长在深海区域的稀有海洋植物，能够在极低光照环境下通过特殊的光合作用机制生存。它含有丰富的生物活性物质，被科学家研究用于开发新型药物。海神草形成的草场是许多深海生物的重要栖息地。","docCount":4,"id":595531949385322513,"name":"海神草","viewCount":95,"voteCount":22},{"category1Id":300,"category2Id":302,"cover":"/image/地下微生物.png","description":"海底热泉周围的地下微生物群落是地球上最极端环境中的生命形式之一。这些微生物能够在高温、高压和高硫环境中繁衍，不依赖阳光进行化能合成。科学家认为研究这些生物可能提供关于地球早期生命甚至外星生命的线索。","docCount":6,"id":595531949385322514,"name":"地下微生物群落","viewCount":115,"voteCount":38}],"total":19},"success":true}
2025-09-02 14:27:04.211 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4827880626521120  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-02 14:27:18.992 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-02 14:27:18.999 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-02 15:58:30.768 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 30080 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 15:58:30.772 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 15:58:32.206 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8083 (http)
2025-09-02 15:58:32.223 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8083"]
2025-09-02 15:58:32.224 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 15:58:32.225 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 15:58:32.323 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 15:58:32.323 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1494 ms
2025-09-02 15:58:34.142 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 15:58:34.362 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8083"]
2025-09-02 15:58:34.382 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8083 (http) with context path ''
2025-09-02 15:58:34.392 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.176 seconds (JVM running for 5.621)
2025-09-02 15:58:34.395 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 15:58:34.395 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8083
2025-09-02 15:59:14.288 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02 15:59:14.289 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-02 15:59:14.291 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-09-02 15:59:56.905 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 16:04:25.683 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 29788 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 16:04:25.686 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 16:04:26.968 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8083 (http)
2025-09-02 16:04:26.977 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8083"]
2025-09-02 16:04:26.978 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 16:04:26.978 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 16:04:27.079 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 16:04:27.079 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1346 ms
2025-09-02 16:04:29.212 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 16:04:29.465 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8083"]
2025-09-02 16:04:29.473 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-09-02 16:04:29.476 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 16:04:29.479 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Pausing ProtocolHandler ["http-nio-8083"]
2025-09-02 16:04:29.480 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-09-02 16:04:29.492 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Stopping ProtocolHandler ["http-nio-8083"]
2025-09-02 16:04:29.493 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Destroying ProtocolHandler ["http-nio-8083"]
2025-09-02 16:04:29.501 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-09-02 16:04:29.528 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

2025-09-02 16:06:20.611 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 15396 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 16:06:20.614 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 16:06:21.778 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8083 (http)
2025-09-02 16:06:21.786 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8083"]
2025-09-02 16:06:21.787 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 16:06:21.787 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 16:06:21.871 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 16:06:21.871 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1212 ms
2025-09-02 16:06:23.764 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 16:06:23.989 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8083"]
2025-09-02 16:06:23.993 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8083 is already in use
2025-09-02 16:06:23.995 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 16:06:23.997 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Pausing ProtocolHandler ["http-nio-8083"]
2025-09-02 16:06:23.998 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-09-02 16:06:24.007 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Stopping ProtocolHandler ["http-nio-8083"]
2025-09-02 16:06:24.008 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Destroying ProtocolHandler ["http-nio-8083"]
2025-09-02 16:06:24.015 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-09-02 16:06:24.028 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8083 was already in use.

Action:

Identify and stop the process that's listening on port 8083 or configure this application to listen on another port.

2025-09-02 16:09:03.694 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 1088 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 16:09:03.696 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 16:09:05.049 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 16:09:05.058 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 16:09:05.058 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 16:09:05.059 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 16:09:05.147 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 16:09:05.147 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1409 ms
2025-09-02 16:09:06.892 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 16:09:07.101 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 16:09:07.117 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 16:09:07.126 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.782 seconds (JVM running for 4.923)
2025-09-02 16:09:07.128 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 16:09:07.128 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 16:10:04.663 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-02 16:10:04.663 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-02 16:10:04.665 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-09-02 16:10:21.024 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 16:10:40.007 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 14848 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 16:10:40.010 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 16:10:41.131 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 16:10:41.138 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 16:10:41.138 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 16:10:41.139 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 16:10:41.222 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 16:10:41.222 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1172 ms
2025-09-02 16:10:42.857 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 16:10:43.070 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 16:10:43.088 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 16:10:43.097 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.446 seconds (JVM running for 4.588)
2025-09-02 16:10:43.100 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 16:10:43.101 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 16:11:23.194 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 16:11:48.389 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 31380 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 16:11:48.393 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 16:11:50.160 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 16:11:50.172 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 16:11:50.173 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 16:11:50.174 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 16:11:50.338 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 16:11:50.339 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1879 ms
2025-09-02 16:11:52.645 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 16:11:53.021 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 16:11:53.045 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 16:11:53.057 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 5.2 seconds (JVM running for 6.562)
2025-09-02 16:11:53.061 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 16:11:53.062 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 16:12:35.662 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 16:13:34.855 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 28344 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 16:13:34.858 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 16:13:36.087 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 16:13:36.098 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 16:13:36.098 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 16:13:36.099 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 16:13:36.193 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 16:13:36.193 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1293 ms
2025-09-02 16:13:37.927 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 16:13:38.140 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 16:13:38.157 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 16:13:38.167 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.666 seconds (JVM running for 4.876)
2025-09-02 16:13:38.169 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 16:13:38.169 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 16:16:10.101 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 16:20:48.198 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 3664 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 16:20:48.201 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 16:20:49.536 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 16:20:49.545 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 16:20:49.547 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 16:20:49.547 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 16:20:49.635 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 16:20:49.636 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1383 ms
2025-09-02 16:20:51.549 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 16:20:51.842 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 16:20:51.866 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 16:20:51.876 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.118 seconds (JVM running for 5.407)
2025-09-02 16:20:51.880 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 16:20:51.880 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 16:21:51.030 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 16:45:38.495 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 9104 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 16:45:38.497 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 16:45:39.766 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 16:45:39.773 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 16:45:39.774 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 16:45:39.774 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 16:45:39.862 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 16:45:39.862 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1319 ms
2025-09-02 16:45:41.586 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 16:45:41.797 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 16:45:41.812 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 16:45:41.820 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.733 seconds (JVM running for 4.995)
2025-09-02 16:45:41.823 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 16:45:41.823 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 16:47:30.628 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 16:50:48.946 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 29448 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 16:50:48.948 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 16:50:50.167 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 16:50:50.175 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 16:50:50.175 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 16:50:50.175 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 16:50:50.260 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 16:50:50.260 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1265 ms
2025-09-02 16:50:52.357 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 16:50:52.681 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 16:50:52.705 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 16:50:52.715 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.174 seconds (JVM running for 5.453)
2025-09-02 16:50:52.719 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 16:50:52.719 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 16:51:27.774 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 17:03:04.249 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 24332 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 17:03:04.252 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 17:03:05.586 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 17:03:05.594 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 17:03:05.595 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 17:03:05.596 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 17:03:05.694 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 17:03:05.694 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1394 ms
2025-09-02 17:03:07.483 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 17:03:07.724 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 17:03:07.745 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 17:03:07.754 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.945 seconds (JVM running for 5.216)
2025-09-02 17:03:07.757 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 17:03:07.757 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 17:04:02.514 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 17:10:48.680 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 3104 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 17:10:48.683 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 17:10:49.805 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 17:10:49.813 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 17:10:49.814 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 17:10:49.814 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 17:10:49.893 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 17:10:49.893 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1167 ms
2025-09-02 17:10:51.478 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 17:10:51.673 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 17:10:51.691 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 17:10:51.700 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.354 seconds (JVM running for 4.48)
2025-09-02 17:10:51.704 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 17:10:51.704 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 17:11:52.623 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 19:51:11.515 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 17448 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 19:51:11.519 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 19:51:13.041 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 19:51:13.054 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 19:51:13.055 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 19:51:13.056 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 19:51:13.178 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 19:51:13.178 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1600 ms
2025-09-02 19:51:15.130 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 19:51:15.382 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 19:51:15.407 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 19:51:15.416 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.479 seconds (JVM running for 5.935)
2025-09-02 19:51:15.419 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 19:51:15.419 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 20:02:43.036 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 20:02:57.329 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 18472 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 20:02:57.335 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 20:02:58.766 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 20:02:58.774 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 20:02:58.775 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 20:02:58.775 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 20:02:58.863 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 20:02:58.864 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1449 ms
2025-09-02 20:03:00.992 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 20:03:01.242 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 20:03:01.262 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 20:03:01.272 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.493 seconds (JVM running for 6.143)
2025-09-02 20:03:01.277 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 20:03:01.278 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 20:06:17.759 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 20:06:23.979 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 10248 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 20:06:23.983 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 20:06:25.447 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 20:06:25.454 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 20:06:25.455 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 20:06:25.455 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 20:06:25.551 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 20:06:25.552 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1507 ms
2025-09-02 20:06:27.493 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 20:06:27.713 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 20:06:27.734 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 20:06:27.742 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.375 seconds (JVM running for 6.307)
2025-09-02 20:06:27.746 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 20:06:27.746 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 20:12:15.839 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-02 20:12:23.735 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 14820 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-02 20:12:23.738 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-02 20:12:24.901 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-02 20:12:24.909 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-02 20:12:24.910 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-02 20:12:24.910 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-02 20:12:24.986 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-02 20:12:24.986 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1207 ms
2025-09-02 20:12:26.848 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-02 20:12:27.106 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-02 20:12:27.131 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-02 20:12:27.142 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.822 seconds (JVM running for 4.972)
2025-09-02 20:12:27.145 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-02 20:12:27.145 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-02 20:14:48.300 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
