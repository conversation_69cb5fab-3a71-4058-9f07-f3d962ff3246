<template>
  <div class="vehicle-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1>
            <CarOutlined />
            我的车辆
          </h1>
          <p>管理您的爱车信息，享受更好的维修服务</p>
          <div class="vehicle-stats">
            <div class="stat-item">
              <span class="stat-number">{{ vehicles.length }}</span>
              <span class="stat-label">辆车辆</span>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <span class="stat-number">{{ totalMileage.toLocaleString() }}</span>
              <span class="stat-label">总里程(km)</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <a-button type="primary" size="large" @click="showAddModal">
            <PlusOutlined />
            添加车辆
          </a-button>
        </div>
      </div>
    </div>

    <!-- 车辆列表 -->
    <div class="vehicles-container">
      <div v-if="vehicles.length === 0" class="empty-state">
        <div class="empty-illustration">
          <CarOutlined />
        </div>
        <h2>还没有添加车辆</h2>
        <p>添加您的第一辆车，开始专属的汽车服务之旅</p>
        <a-button type="primary" size="large" @click="showAddModal">
          <PlusOutlined />
          立即添加车辆
        </a-button>
      </div>

      <div v-else class="vehicles-grid">
        <div 
          v-for="vehicle in vehicles" 
          :key="vehicle.id"
          class="vehicle-card"
        >
          <div class="vehicle-header">
            <div class="vehicle-image">
              <img :src="vehicle.image || '/image/default-service.png'" :alt="vehicle.brand" />
              <div class="image-overlay">
                <a-button size="small" type="text" class="change-image-btn" @click="changeVehicleImage(vehicle.id)">
                  <CameraOutlined />
                  更换图片
                </a-button>
              </div>
            </div>
          </div>
          
          <div class="vehicle-content">
            <div class="vehicle-title">
              <h3>{{ vehicle.brand }} {{ vehicle.model }}</h3>
              <div class="vehicle-year">{{ vehicle.year }}年款</div>
            </div>
            
            <div class="license-plate">
              <div class="plate-container">
                <span class="plate-prefix">车牌号</span>
                <span class="plate-number">{{ vehicle.plateNumber }}</span>
              </div>
            </div>
            
            <div class="vehicle-details">
              <div class="detail-grid">
                <div class="detail-item">
                  <div class="detail-icon color">
                    <BgColorsOutlined />
                  </div>
                  <div class="detail-info">
                    <span class="detail-label">颜色</span>
                    <span class="detail-value">{{ vehicle.color }}</span>
                  </div>
                </div>
                
                <div class="detail-item">
                  <div class="detail-icon mileage">
                    <DashboardOutlined />
                  </div>
                  <div class="detail-info">
                    <span class="detail-label">里程</span>
                    <span class="detail-value">{{ vehicle.mileage ? vehicle.mileage.toLocaleString() : '0' }} km</span>
                  </div>
                </div>
                
                <div class="detail-item" v-if="vehicle.engineNumber">
                  <div class="detail-icon engine">
                    <SettingOutlined />
                  </div>
                  <div class="detail-info">
                    <span class="detail-label">发动机号</span>
                    <span class="detail-value">{{ vehicle.engineNumber }}</span>
                  </div>
                </div>
                
                <div class="detail-item" v-if="vehicle.vinNumber">
                  <div class="detail-icon vin">
                    <BarcodeOutlined />
                  </div>
                  <div class="detail-info">
                    <span class="detail-label">车架号</span>
                    <span class="detail-value">{{ vehicle.vinNumber }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="vehicle-actions">
              <a-button type="primary" ghost @click="editVehicle(vehicle.id)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button @click="viewHistory(vehicle.id)">
                <HistoryOutlined />
                维保记录
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="1" @click="duplicateVehicle(vehicle.id)">
                      <CopyOutlined />
                      复制车辆
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="2" danger @click="deleteVehicle(vehicle.id)">
                      <DeleteOutlined />
                      删除车辆
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button>
                  <MoreOutlined />
                </a-button>
              </a-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑车辆弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑车辆' : '添加车辆'"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        :model="vehicleForm"
        :rules="formRules"
        ref="formRef"
        layout="vertical"
      >
        <a-form-item label="品牌" name="brand">
          <a-input v-model:value="vehicleForm.brand" placeholder="请输入车辆品牌" />
        </a-form-item>
        <a-form-item label="型号" name="model">
          <a-input v-model:value="vehicleForm.model" placeholder="请输入车辆型号" />
        </a-form-item>
        <a-form-item label="车牌号" name="plateNumber">
          <a-input v-model:value="vehicleForm.plateNumber" placeholder="请输入车牌号" />
        </a-form-item>
        <a-form-item label="年份" name="year">
          <a-input-number v-model:value="vehicleForm.year" :min="1990" :max="2024" style="width: 100%" />
        </a-form-item>
        <a-form-item label="颜色" name="color">
          <a-input v-model:value="vehicleForm.color" placeholder="请输入车辆颜色" />
        </a-form-item>
        <a-form-item label="发动机号" name="engineNumber">
          <a-input v-model:value="vehicleForm.engineNumber" placeholder="请输入发动机号" />
        </a-form-item>
        <a-form-item label="车架号" name="vinNumber">
          <a-input v-model:value="vehicleForm.vinNumber" placeholder="请输入车架号" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 隐藏的文件输入用于图片上传 -->
    <input 
      ref="fileInputRef" 
      type="file" 
      accept="image/*" 
      style="display: none;" 
      @change="handleImageUpload"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  CarOutlined,
  CameraOutlined,
  BgColorsOutlined,
  DashboardOutlined,
  SettingOutlined,
  BarcodeOutlined,
  HistoryOutlined,
  CopyOutlined,
  MoreOutlined
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import axios from 'axios';
import { getCurrentUser } from '@/utils/auth';

export default defineComponent({
  name: 'VehicleManagement',
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    CarOutlined,
    CameraOutlined,
    BgColorsOutlined,
    DashboardOutlined,
    SettingOutlined,
    BarcodeOutlined,
    HistoryOutlined,
    CopyOutlined,
    MoreOutlined
  },
  setup() {
    const vehicles = ref<any[]>([]);
    const modalVisible = ref(false);
    const isEdit = ref(false);
    const formRef = ref();
    const fileInputRef = ref();
    const currentVehicleId = ref<number | null>(null);
    
    // 计算总里程
    const totalMileage = computed(() => {
      return vehicles.value.reduce((total, vehicle) => {
        return total + (vehicle.mileage || 0);
      }, 0);
    });

    const vehicleForm = ref({
      id: null,
      brand: '',
      model: '',
      plateNumber: '',
      year: new Date().getFullYear(),
      color: '',
      engineNumber: '',
      vinNumber: ''
    });

    const formRules = {
      brand: [{ required: true, message: '请输入车辆品牌', trigger: 'blur' }],
      model: [{ required: true, message: '请输入车辆型号', trigger: 'blur' }],
      plateNumber: [{ required: true, message: '请输入车牌号', trigger: 'blur' }],
      year: [{ required: true, message: '请选择年份', trigger: 'blur' }],
      color: [{ required: true, message: '请输入车辆颜色', trigger: 'blur' }]
    };

    const showAddModal = () => {
      isEdit.value = false;
      vehicleForm.value = {
        id: null,
        brand: '',
        model: '',
        plateNumber: '',
        year: new Date().getFullYear(),
        color: '',
        engineNumber: '',
        vinNumber: ''
      };
      modalVisible.value = true;
    };

    const editVehicle = (id: number) => {
      isEdit.value = true;
      const vehicle = vehicles.value.find((v: any) => v.id === id);
      if (vehicle) {
        vehicleForm.value = { ...vehicle };
      }
      modalVisible.value = true;
    };

    const deleteVehicle = (id: number) => {
      Modal.confirm({
        title: '确认删除',
        content: '确定要删除这辆车辆吗？',
        async onOk() {
          try {
            const response = await axios.get('/vehicle/remove', {
              params: { id }
            });
            
            if (response.data.success !== false) {
              message.success(response.data.message || '车辆删除成功');
              // 重新加载车辆列表
              await loadVehicles();
            } else {
              message.error(response.data.message || '删除失败');
            }
          } catch (error) {
            console.error('删除车辆失败:', error);
            message.error('删除车辆失败');
          }
        }
      });
    };

    const handleSubmit = async () => {
      try {
        await formRef.value.validate();
        
        // 从当前登录用户会话中获取用户ID
        const currentUser = getCurrentUser();
        if (!currentUser) {
          message.error('用户未登录，请重新登录');
          return;
        }
        const userId = currentUser.id;
        
        // 转换前端数据格式到后端格式
        const saveData = {
          id: isEdit.value ? vehicleForm.value.id : null,
          licensePlate: vehicleForm.value.plateNumber, // 前端字段是plateNumber
          brand: vehicleForm.value.brand,
          model: vehicleForm.value.model,
          color: vehicleForm.value.color,
          year: vehicleForm.value.year,
          engineNumber: vehicleForm.value.engineNumber,
          vin: vehicleForm.value.vinNumber, // 前端字段是vinNumber
          customerId: userId  // 修改为customerId字段
        };
        
        const response = await axios.post('/vehicle/save', saveData);
        
        if (response.data.success !== false) {
          message.success(response.data.message || (isEdit.value ? '车辆更新成功' : '车辆添加成功'));
          modalVisible.value = false;
          // 重新加载车辆列表
          await loadVehicles();
        } else {
          message.error(response.data.message || '操作失败');
        }
      } catch (error) {
        console.error('保存车辆失败:', error);
        message.error('保存车辆失败');
      }
    };

    const handleCancel = () => {
      modalVisible.value = false;
    };

    const viewHistory = (vehicleId: number) => {
      message.info(`查看车辆 ${vehicleId} 的维保记录`);
    };

    const duplicateVehicle = (vehicleId: number) => {
      const vehicle = vehicles.value.find(v => v.id === vehicleId);
      if (vehicle) {
        const newVehicle = {
          ...vehicle,
          id: Date.now(),
          plateNumber: vehicle.plateNumber + '_副本'
        };
        vehicles.value.push(newVehicle);
        message.success('车辆复制成功');
      }
    };

    const changeVehicleImage = (vehicleId: number) => {
      currentVehicleId.value = vehicleId;
      if (fileInputRef.value) {
        fileInputRef.value.click();
      }
    };

    const handleImageUpload = async (event: Event) => {
      const target = event.target as HTMLInputElement;
      const file = target.files && target.files[0];
      
      if (!file || !currentVehicleId.value) {
        return;
      }

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        message.error('请选择图片文件');
        return;
      }

      // 验证文件大小（限制为5MB）
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        message.error('图片大小不能超过5MB');
        return;
      }

      try {
        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        formData.append('vehicleId', currentVehicleId.value.toString());

        // TODO: 这里应该调用真实的图片上传API
        // const response = await axios.post('/api/upload/vehicle-image', formData);
        
        // 暂时模拟上传成功，使用本地预览
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target && e.target.result as string;
          // 更新车辆图片
          const vehicleIndex = vehicles.value.findIndex(v => v.id === currentVehicleId.value);
          if (vehicleIndex !== -1) {
            vehicles.value[vehicleIndex].image = imageUrl;
          }
          message.success('图片上传成功');
        };
        reader.readAsDataURL(file);
        
      } catch (error) {
        console.error('图片上传失败:', error);
        message.error('图片上传失败');
      } finally {
        // 清空文件输入
        target.value = '';
        currentVehicleId.value = null;
      }
    };

    const loadVehicles = async () => {
      try {
        // 从当前登录用户会话中获取用户ID
        const currentUser = getCurrentUser();
        if (!currentUser) {
          message.error('用户未登录，请重新登录');
          vehicles.value = [];
          return;
        }
        const userId = currentUser.id;
        
        const response = await axios.get('/vehicle/getVehicleListByPage', {
          params: {
            page: 1,
            size: 100, // 获取所有车辆
            customerId: userId  // 修改为customerId参数
          }
        });
        
        if (response.data.success && response.data.content) {
          // 转换后端数据格式到前端格式
          const backendVehicles = response.data.content.list || [];
          vehicles.value = backendVehicles.map((vehicle: any) => ({
            id: vehicle.id,
            brand: vehicle.brand,
            model: vehicle.model,
            plateNumber: vehicle.licensePlate, // 后端字段是licensePlate
            year: vehicle.year,
            color: vehicle.color,
            mileage: vehicle.mileage || 0,
            engineNumber: vehicle.engineNumber,
            vinNumber: vehicle.vin, // 后端字段是vin
            image: vehicle.image || '/image/default-service.png'
          }));
        } else {
          console.warn('获取车辆列表失败:', response.data.message);
          vehicles.value = [];
        }
      } catch (error) {
        console.error('加载车辆列表失败:', error);
        message.error('加载车辆列表失败');
        // 使用空数组作为默认值
        vehicles.value = [];
      }
    };

    onMounted(() => {
      loadVehicles();
    });

    return {
      vehicles,
      totalMileage,
      modalVisible,
      isEdit,
      formRef,
      fileInputRef,
      vehicleForm,
      formRules,
      showAddModal,
      editVehicle,
      deleteVehicle,
      handleSubmit,
      handleCancel,
      viewHistory,
      duplicateVehicle,
      changeVehicleImage,
      handleImageUpload
    };
  }
});
</script>

<style scoped>
.vehicle-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  position: relative;
}

.vehicle-management::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.title-section h1 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 32px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-section p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 16px;
}

.vehicle-stats {
  display: flex;
  align-items: center;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-divider {
  width: 1px;
  height: 24px;
  background: rgba(0, 0, 0, 0.1);
}

.header-actions .ant-btn {
  height: 44px;
  border-radius: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 车辆容器 */
.vehicles-container {
  padding: 0 32px 32px;
  position: relative;
  z-index: 1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.empty-illustration {
  width: 120px;
  height: 120px;
  margin: 0 auto 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: #667eea;
}

.empty-state h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.empty-state p {
  margin: 0 0 32px 0;
  color: #666;
  font-size: 16px;
}

/* 车辆网格 */
.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.vehicle-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.vehicle-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

/* 车辆头部 */
.vehicle-header {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.vehicle-image {
  width: 100%;
  height: 100%;
  position: relative;
}

.vehicle-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.vehicle-card:hover .vehicle-image img {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.vehicle-card:hover .image-overlay {
  opacity: 1;
}

.change-image-btn {
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
}

.change-image-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.8);
}

/* 车辆内容 */
.vehicle-content {
  padding: 24px;
}

.vehicle-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.vehicle-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.vehicle-year {
  font-size: 14px;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}

/* 车牌号 */
.license-plate {
  margin-bottom: 24px;
}

.plate-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.plate-prefix {
  font-size: 14px;
  color: #666;
}

.plate-number {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 2px;
  font-family: 'Courier New', monospace;
}

/* 车辆详情 */
.vehicle-details {
  margin-bottom: 24px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  transition: all 0.3s;
}

.detail-item:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-1px);
}

.detail-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
  flex-shrink: 0;
}

.detail-icon.color {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.detail-icon.mileage {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.detail-icon.engine {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.detail-icon.vin {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.detail-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-label {
  font-size: 12px;
  color: #666;
  line-height: 1;
}

.detail-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
  line-height: 1;
}

/* 车辆操作 */
.vehicle-actions {
  display: flex;
  gap: 8px;
}

.vehicle-actions .ant-btn {
  border-radius: 8px;
  font-weight: 500;
  flex: 1;
}

.vehicle-actions .ant-btn:last-child {
  flex: 0 0 auto;
  width: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .vehicles-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 24px 20px;
    border-radius: 0;
  }
  
  .vehicles-container {
    padding: 0 20px 24px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }
  
  .title-section h1 {
    font-size: 24px;
  }
  
  .vehicle-stats {
    justify-content: center;
  }
  
  .vehicles-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .empty-state {
    padding: 60px 20px;
  }
  
  .empty-illustration {
    width: 100px;
    height: 100px;
    font-size: 40px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 20px 16px;
  }
  
  .vehicles-container {
    padding: 0 16px 20px;
  }
  
  .title-section h1 {
    font-size: 20px;
  }
  
  .vehicle-content {
    padding: 20px;
  }
  
  .vehicle-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .plate-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .vehicle-actions {
    flex-direction: column;
  }
  
  .vehicle-actions .ant-btn:last-child {
    width: 100%;
  }
}
</style>
