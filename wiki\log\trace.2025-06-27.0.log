2025-06-27 10:31:30.352 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 7852 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-27 10:31:30.355 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-27 10:31:31.402 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m <PERSON><PERSON> initialized with port(s): 8080 (http)
2025-06-27 10:31:31.409 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-27 10:31:31.410 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-27 10:31:31.411 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-27 10:31:31.471 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-27 10:31:31.472 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1059 ms
2025-06-27 10:31:31.520 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'categoryController': Unsatisfied dependency expressed through field 'categoryService'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.gec.wiki.service.CategoryService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-06-27 10:31:31.522 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-06-27 10:31:31.540 WARN  org.apache.catalina.loader.WebappClassLoaderBase  :173  [32m                  [0;39m The web application [ROOT] appears to have started a thread named [Abandoned connection cleanup thread] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.8/java.lang.Object.wait(Native Method)
 java.base@17.0.8/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 app//com.mysql.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:40)
2025-06-27 10:31:31.545 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-27 10:31:31.557 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field categoryService in com.gec.wiki.controller.CategoryController required a bean of type 'com.gec.wiki.service.CategoryService' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'com.gec.wiki.service.CategoryService' in your configuration.

2025-06-27 10:35:38.300 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 27484 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-27 10:35:38.307 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-27 10:35:39.152 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-27 10:35:39.159 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-27 10:35:39.159 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-27 10:35:39.160 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-27 10:35:39.222 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-27 10:35:39.223 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 881 ms
2025-06-27 10:35:39.267 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'categoryController': Unsatisfied dependency expressed through field 'categoryService'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.gec.wiki.service.CategoryService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-06-27 10:35:39.269 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-06-27 10:35:39.285 WARN  org.apache.catalina.loader.WebappClassLoaderBase  :173  [32m                  [0;39m The web application [ROOT] appears to have started a thread named [Abandoned connection cleanup thread] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.8/java.lang.Object.wait(Native Method)
 java.base@17.0.8/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 app//com.mysql.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:40)
2025-06-27 10:35:39.292 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-27 10:35:39.304 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field categoryService in com.gec.wiki.controller.CategoryController required a bean of type 'com.gec.wiki.service.CategoryService' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'com.gec.wiki.service.CategoryService' in your configuration.

2025-06-27 10:45:47.386 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 17208 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-27 10:45:47.389 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-27 10:45:48.121 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-27 10:45:48.126 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-27 10:45:48.126 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-27 10:45:48.128 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-27 10:45:48.184 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-27 10:45:48.184 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 764 ms
2025-06-27 10:45:48.230 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'categoryController': Unsatisfied dependency expressed through field 'categoryService'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.gec.wiki.service.CategoryService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-06-27 10:45:48.231 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-06-27 10:45:48.244 WARN  org.apache.catalina.loader.WebappClassLoaderBase  :173  [32m                  [0;39m The web application [ROOT] appears to have started a thread named [Abandoned connection cleanup thread] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.8/java.lang.Object.wait(Native Method)
 java.base@17.0.8/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 app//com.mysql.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:40)
2025-06-27 10:45:48.251 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-27 10:45:48.264 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field categoryService in com.gec.wiki.controller.CategoryController required a bean of type 'com.gec.wiki.service.CategoryService' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'com.gec.wiki.service.CategoryService' in your configuration.

2025-06-27 10:56:57.220 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 17.0.8 on LAPTOP-3B4KQOU1 with PID 8664 (D:\wiki\target\classes started by 86147 in D:\wiki)
2025-06-27 10:56:57.225 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-06-27 10:56:57.964 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8080 (http)
2025-06-27 10:56:57.970 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8080"]
2025-06-27 10:56:57.970 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-06-27 10:56:57.971 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-06-27 10:56:58.028 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-06-27 10:56:58.028 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 772 ms
2025-06-27 10:56:58.615 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-06-27 10:56:58.774 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8080"]
2025-06-27 10:56:58.787 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8080 (http) with context path ''
2025-06-27 10:56:58.795 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 1.857 seconds (JVM running for 2.352)
2025-06-27 10:56:58.797 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 启动成功！！
2025-06-27 10:56:58.797 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8080
2025-06-27 10:59:10.916 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-27 10:59:10.916 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-06-27 10:59:10.918 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 1 ms
2025-06-27 10:59:10.986 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637784505877536  [0;39m ------------- 开始 -------------
2025-06-27 10:59:10.987 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637784505877536  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-27 10:59:10.987 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637784505877536  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-27 10:59:10.988 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637784505877536  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 10:59:11.061 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637784505877536  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-27 10:59:11.227 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4637784505877536  [0;39m {dataSource-1} inited
2025-06-27 10:59:11.473 INFO  com.gec.wiki.service.EbookService                 :61   [32m4637784505877536  [0;39m 总行数：7
2025-06-27 10:59:11.474 INFO  com.gec.wiki.service.EbookService                 :62   [32m4637784505877536  [0;39m 总页数：1
2025-06-27 10:59:11.488 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637784505877536  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-06-27 10:59:11.488 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637784505877536  [0;39m ------------- 结束 耗时：503 ms -------------
2025-06-27 10:59:13.279 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637784581047328  [0;39m ------------- 开始 -------------
2025-06-27 10:59:13.280 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637784581047328  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-27 10:59:13.280 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637784581047328  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-27 10:59:13.280 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637784581047328  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 10:59:13.281 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637784581047328  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-27 10:59:13.287 INFO  com.gec.wiki.service.EbookService                 :61   [32m4637784581047328  [0;39m 总行数：7
2025-06-27 10:59:13.288 INFO  com.gec.wiki.service.EbookService                 :62   [32m4637784581047328  [0;39m 总页数：4
2025-06-27 10:59:13.288 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637784581047328  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4}],"total":7},"success":true}
2025-06-27 10:59:13.288 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637784581047328  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-27 10:59:14.174 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637784610374688  [0;39m ------------- 开始 -------------
2025-06-27 10:59:14.174 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637784610374688  [0;39m 请求地址: http://localhost:8080/ebook/getebookListByPage GET
2025-06-27 10:59:14.174 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637784610374688  [0;39m 类名方法: com.gec.wiki.controller.EbookController.getebookListByPage
2025-06-27 10:59:14.175 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637784610374688  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 10:59:14.175 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637784610374688  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-06-27 10:59:14.181 INFO  com.gec.wiki.service.EbookService                 :61   [32m4637784610374688  [0;39m 总行数：7
2025-06-27 10:59:14.182 INFO  com.gec.wiki.service.EbookService                 :62   [32m4637784610374688  [0;39m 总页数：1
2025-06-27 10:59:14.184 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637784610374688  [0;39m 返回结果: {"content":{"list":[{"category1Id":211,"category2Id":213,"cover":"/image/fbe46dc9a6fc4f3f91867e384437775e.png","description":"虎皮鲨鱼","docCount":3,"id":1,"name":"虎鲸11","viewCount":744,"voteCount":484},{"category1Id":100,"category2Id":101,"cover":"/image/106a247d894843248b58e3b5998b4048.jpg","description":"海产藻类（Algae）的统称，通常固着于海底或某种固体结构上，是基础细胞所构成的单株或一长串的简单植物。","docCount":1,"id":2,"name":"海藻","viewCount":13,"voteCount":4},{"category1Id":211,"category2Id":212,"cover":"/image/75a60e7183894aa3ac41c6aa0a7e91e3.jpg","description":"双生水母多营养体期有前、后2个泳钟。前泳钟五角锥状,有5条完整的纵棱,泳囊口有齿,干室较深,口板不分瓣;后泳钟顶部为显著的榫突插进前泳钟干室,泳囊口也有齿。","docCount":1,"id":3,"name":"双生水母","viewCount":6,"voteCount":3},{"category1Id":100,"category2Id":102,"cover":"/image/22e88c9a14344ccd880debb4b23bb6b7.png","description":"多年生海生沉水草本。根茎匍匐，侧扁，棕红色，密被厚层长纤维（叶鞘残迹）。","docCount":1,"id":4,"name":"海神草","viewCount":6,"voteCount":2},{"category1Id":211,"category2Id":213,"cover":"/image/cover22.png","description":"鲸鱼","id":5,"name":"抹香鲸"},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632088974459936,"name":"虎鲸012","viewCount":744,"voteCount":484},{"category1Id":211,"category2Id":213,"cover":"/image/cover11.png","description":"虎皮鲨鱼","docCount":3,"id":4632112403940384,"name":"虎鲸","viewCount":744,"voteCount":484}],"total":7},"success":true}
2025-06-27 10:59:14.185 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637784610374688  [0;39m ------------- 结束 耗时：11 ms -------------
2025-06-27 11:01:18.549 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637788685894688  [0;39m ------------- 开始 -------------
2025-06-27 11:01:18.549 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637788685894688  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:01:18.549 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637788685894688  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:01:18.549 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637788685894688  [0;39m 远程地址: 127.0.0.1
2025-06-27 11:01:18.551 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637788685894688  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-27 11:01:18.630 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637788685894688  [0;39m 总行数：11
2025-06-27 11:01:18.631 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637788685894688  [0;39m 总页数：6
2025-06-27 11:01:18.639 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637788685894688  [0;39m 返回结果: {"content":{"list":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101}],"total":11},"success":true}
2025-06-27 11:01:18.640 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637788685894688  [0;39m ------------- 结束 耗时：91 ms -------------
2025-06-27 11:01:31.165 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637789099295776  [0;39m ------------- 开始 -------------
2025-06-27 11:01:31.165 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637789099295776  [0;39m 请求地址: http://localhost:8080/category/save POST
2025-06-27 11:01:31.165 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637789099295776  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.save
2025-06-27 11:01:31.165 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637789099295776  [0;39m 远程地址: 127.0.0.1
2025-06-27 11:01:31.167 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637789099295776  [0;39m 请求参数: [{"name":"123"}]
2025-06-27 11:01:31.216 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4637789099295776  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'parent' doesn't have a default value
### The error may exist in com/gec/wiki/mapper/CategoryMapper.java (best guess)
### The error may involve com.gec.wiki.mapper.CategoryMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO category  ( id,  name )  VALUES  ( ?,  ? )
### Cause: java.sql.SQLException: Field 'parent' doesn't have a default value
; Field 'parent' doesn't have a default value; nested exception is java.sql.SQLException: Field 'parent' doesn't have a default value] with root cause
java.sql.SQLException: Field 'parent' doesn't have a default value
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:1078)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:4187)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:4119)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2570)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:2731)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2815)
	at com.mysql.jdbc.PreparedStatement.executeInternal(PreparedStatement.java:2155)
	at com.mysql.jdbc.PreparedStatement.execute(PreparedStatement.java:1379)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy96.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at jdk.proxy2/jdk.proxy2.$Proxy94.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at jdk.proxy2/jdk.proxy2.$Proxy93.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at jdk.proxy2/jdk.proxy2.$Proxy74.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy75.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.saveOrUpdate(ServiceImpl.java:165)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.gec.wiki.service.impl.CategoryServiceImpl$$EnhancerBySpringCGLIB$$8d056fb1.saveOrUpdate(<generated>)
	at com.gec.wiki.controller.CategoryController.save(CategoryController.java:88)
	at com.gec.wiki.controller.CategoryController$$FastClassBySpringCGLIB$$89f0d984.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.gec.wiki.aspect.LogAspect.doAround(LogAspect.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.gec.wiki.controller.CategoryController$$EnhancerBySpringCGLIB$$87172f7a.save(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-27 11:01:40.593 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637789408232480  [0;39m ------------- 开始 -------------
2025-06-27 11:01:40.593 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637789408232480  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:01:40.593 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637789408232480  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:01:40.594 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637789408232480  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:01:40.594 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637789408232480  [0;39m 请求参数: [{"page":2,"size":2}]
2025-06-27 11:01:40.596 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637789408232480  [0;39m 总行数：11
2025-06-27 11:01:40.596 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637789408232480  [0;39m 总页数：6
2025-06-27 11:01:40.596 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637789408232480  [0;39m 返回结果: {"content":{"list":[{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3}],"total":11},"success":true}
2025-06-27 11:01:40.596 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637789408232480  [0;39m ------------- 结束 耗时：3 ms -------------
2025-06-27 11:01:41.487 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637789437527072  [0;39m ------------- 开始 -------------
2025-06-27 11:01:41.487 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637789437527072  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:01:41.487 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637789437527072  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:01:41.487 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637789437527072  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:01:41.487 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637789437527072  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-27 11:01:41.491 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637789437527072  [0;39m 总行数：11
2025-06-27 11:01:41.491 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637789437527072  [0;39m 总页数：6
2025-06-27 11:01:41.491 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637789437527072  [0;39m 返回结果: {"content":{"list":[{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"total":11},"success":true}
2025-06-27 11:01:41.492 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637789437527072  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-27 11:01:42.727 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637789478159392  [0;39m ------------- 开始 -------------
2025-06-27 11:01:42.727 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637789478159392  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:01:42.727 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637789478159392  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:01:42.727 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637789478159392  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:01:42.728 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637789478159392  [0;39m 请求参数: [{"page":6,"size":2}]
2025-06-27 11:01:42.732 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637789478159392  [0;39m 总行数：11
2025-06-27 11:01:42.732 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637789478159392  [0;39m 总页数：6
2025-06-27 11:01:42.733 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637789478159392  [0;39m 返回结果: {"content":{"list":[{"id":214,"name":"海洋底栖动物","parent":211,"sort":3}],"total":11},"success":true}
2025-06-27 11:01:42.733 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637789478159392  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-27 11:12:46.673 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637811234341920  [0;39m ------------- 开始 -------------
2025-06-27 11:12:46.673 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637811234341920  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:12:46.674 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637811234341920  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:12:46.674 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637811234341920  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:12:46.674 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637811234341920  [0;39m 请求参数: [{"page":1,"size":2}]
2025-06-27 11:12:46.680 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637811234341920  [0;39m 总行数：11
2025-06-27 11:12:46.680 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637811234341920  [0;39m 总页数：6
2025-06-27 11:12:46.681 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637811234341920  [0;39m 返回结果: {"content":{"list":[{"id":100,"name":"海洋植物","parent":0,"sort":2},{"id":101,"name":"藻类植物","parent":100,"sort":101}],"total":11},"success":true}
2025-06-27 11:12:46.681 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637811234341920  [0;39m ------------- 结束 耗时：8 ms -------------
2025-06-27 11:12:49.192 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637811316884512  [0;39m ------------- 开始 -------------
2025-06-27 11:12:49.192 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637811316884512  [0;39m 请求地址: http://localhost:8080/category/save POST
2025-06-27 11:12:49.192 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637811316884512  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.save
2025-06-27 11:12:49.192 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637811316884512  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:12:49.192 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637811316884512  [0;39m 请求参数: [{}]
2025-06-27 11:12:49.192 ERROR o.a.c.c.C.[.[localhost].[/].[dispatcherServlet]   :175  [32m4637811316884512  [0;39m Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: java.sql.SQLException: Field 'parent' doesn't have a default value
### The error may exist in com/gec/wiki/mapper/CategoryMapper.java (best guess)
### The error may involve com.gec.wiki.mapper.CategoryMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO category  ( id )  VALUES  ( ? )
### Cause: java.sql.SQLException: Field 'parent' doesn't have a default value
; Field 'parent' doesn't have a default value; nested exception is java.sql.SQLException: Field 'parent' doesn't have a default value] with root cause
java.sql.SQLException: Field 'parent' doesn't have a default value
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:1078)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:4187)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:4119)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2570)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:2731)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2815)
	at com.mysql.jdbc.PreparedStatement.executeInternal(PreparedStatement.java:2155)
	at com.mysql.jdbc.PreparedStatement.execute(PreparedStatement.java:1379)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at jdk.internal.reflect.GeneratedMethodAccessor47.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy96.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at jdk.proxy2/jdk.proxy2.$Proxy94.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at jdk.proxy2/jdk.proxy2.$Proxy93.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at jdk.proxy2/jdk.proxy2.$Proxy74.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy75.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.saveOrUpdate(ServiceImpl.java:165)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.gec.wiki.service.impl.CategoryServiceImpl$$EnhancerBySpringCGLIB$$8d056fb1.saveOrUpdate(<generated>)
	at com.gec.wiki.controller.CategoryController.save(CategoryController.java:88)
	at com.gec.wiki.controller.CategoryController$$FastClassBySpringCGLIB$$89f0d984.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.gec.wiki.aspect.LogAspect.doAround(LogAspect.java:85)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.gec.wiki.controller.CategoryController$$EnhancerBySpringCGLIB$$87172f7a.save(<generated>)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-27 11:12:56.946 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637811570967584  [0;39m ------------- 开始 -------------
2025-06-27 11:12:56.946 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637811570967584  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:12:56.946 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637811570967584  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:12:56.946 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637811570967584  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:12:56.946 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637811570967584  [0;39m 请求参数: [{"page":6,"size":2}]
2025-06-27 11:12:56.952 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637811570967584  [0;39m 总行数：11
2025-06-27 11:12:56.952 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637811570967584  [0;39m 总页数：6
2025-06-27 11:12:56.953 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637811570967584  [0;39m 返回结果: {"content":{"list":[{"id":214,"name":"海洋底栖动物","parent":211,"sort":3}],"total":11},"success":true}
2025-06-27 11:12:56.953 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637811570967584  [0;39m ------------- 结束 耗时：7 ms -------------
2025-06-27 11:12:59.008 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637811638535200  [0;39m ------------- 开始 -------------
2025-06-27 11:12:59.008 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637811638535200  [0;39m 请求地址: http://localhost:8080/category/remove GET
2025-06-27 11:12:59.008 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637811638535200  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.remove
2025-06-27 11:12:59.008 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637811638535200  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:12:59.008 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637811638535200  [0;39m 请求参数: [214]
2025-06-27 11:12:59.014 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637811638535200  [0;39m 返回结果: {"success":true}
2025-06-27 11:12:59.014 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637811638535200  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-27 11:12:59.032 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637811639321632  [0;39m ------------- 开始 -------------
2025-06-27 11:12:59.033 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637811639321632  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:12:59.033 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637811639321632  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:12:59.033 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637811639321632  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:12:59.033 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637811639321632  [0;39m 请求参数: [{"page":6,"size":2}]
2025-06-27 11:12:59.035 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637811639321632  [0;39m 总行数：10
2025-06-27 11:12:59.036 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637811639321632  [0;39m 总页数：5
2025-06-27 11:12:59.036 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637811639321632  [0;39m 返回结果: {"content":{"list":[],"total":10},"success":true}
2025-06-27 11:12:59.036 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637811639321632  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-27 11:13:01.095 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637811706922016  [0;39m ------------- 开始 -------------
2025-06-27 11:13:01.095 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637811706922016  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:13:01.097 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637811706922016  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:13:01.097 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637811706922016  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:13:01.097 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637811706922016  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-27 11:13:01.099 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637811706922016  [0;39m 总行数：10
2025-06-27 11:13:01.099 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637811706922016  [0;39m 总页数：5
2025-06-27 11:13:01.099 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637811706922016  [0;39m 返回结果: {"content":{"list":[{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1}],"total":10},"success":true}
2025-06-27 11:13:01.099 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637811706922016  [0;39m ------------- 结束 耗时：4 ms -------------
2025-06-27 11:13:05.191 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637811841139744  [0;39m ------------- 开始 -------------
2025-06-27 11:13:05.192 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637811841139744  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:13:05.192 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637811841139744  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:13:05.192 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637811841139744  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:13:05.192 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637811841139744  [0;39m 请求参数: [{"page":5,"size":2}]
2025-06-27 11:13:05.194 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637811841139744  [0;39m 总行数：10
2025-06-27 11:13:05.194 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637811841139744  [0;39m 总页数：5
2025-06-27 11:13:05.194 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637811841139744  [0;39m 返回结果: {"content":{"list":[{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1}],"total":10},"success":true}
2025-06-27 11:13:05.194 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637811841139744  [0;39m ------------- 结束 耗时：3 ms -------------
2025-06-27 11:18:58.376 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637823414305824  [0;39m ------------- 开始 -------------
2025-06-27 11:18:58.376 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637823414305824  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:18:58.376 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637823414305824  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:18:58.377 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637823414305824  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:18:58.377 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637823414305824  [0;39m 请求参数: [{"page":2,"size":2}]
2025-06-27 11:18:58.383 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637823414305824  [0;39m 总行数：10
2025-06-27 11:18:58.383 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637823414305824  [0;39m 总页数：5
2025-06-27 11:18:58.385 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637823414305824  [0;39m 返回结果: {"content":{"list":[{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3}],"total":10},"success":true}
2025-06-27 11:18:58.385 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637823414305824  [0;39m ------------- 结束 耗时：9 ms -------------
2025-06-27 11:18:59.872 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637823463326752  [0;39m ------------- 开始 -------------
2025-06-27 11:18:59.872 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637823463326752  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:18:59.872 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637823463326752  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:18:59.872 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637823463326752  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:18:59.873 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637823463326752  [0;39m 请求参数: [{"page":5,"size":2}]
2025-06-27 11:18:59.877 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637823463326752  [0;39m 总行数：10
2025-06-27 11:18:59.878 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637823463326752  [0;39m 总页数：5
2025-06-27 11:18:59.878 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637823463326752  [0;39m 返回结果: {"content":{"list":[{"id":212,"name":"海洋浮游动物","parent":211,"sort":2},{"id":213,"name":"海洋游泳动物","parent":211,"sort":1}],"total":10},"success":true}
2025-06-27 11:18:59.878 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637823463326752  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-27 11:26:00.315 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637837240402976  [0;39m ------------- 开始 -------------
2025-06-27 11:26:00.315 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637837240402976  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:26:00.315 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637837240402976  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:26:00.315 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637837240402976  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:26:00.315 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637837240402976  [0;39m 请求参数: [{"page":4,"size":2}]
2025-06-27 11:26:00.319 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637837240402976  [0;39m 总行数：10
2025-06-27 11:26:00.320 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637837240402976  [0;39m 总页数：5
2025-06-27 11:26:00.320 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637837240402976  [0;39m 返回结果: {"content":{"list":[{"id":208,"name":"test2","parent":207,"sort":2},{"id":211,"name":"海洋动物","parent":0,"sort":1}],"total":10},"success":true}
2025-06-27 11:26:00.320 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637837240402976  [0;39m ------------- 结束 耗时：5 ms -------------
2025-06-27 11:26:00.889 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637837259211808  [0;39m ------------- 开始 -------------
2025-06-27 11:26:00.890 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637837259211808  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:26:00.890 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637837259211808  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:26:00.890 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637837259211808  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:26:00.890 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637837259211808  [0;39m 请求参数: [{"page":2,"size":2}]
2025-06-27 11:26:00.894 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637837259211808  [0;39m 总行数：10
2025-06-27 11:26:00.895 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637837259211808  [0;39m 总页数：5
2025-06-27 11:26:00.895 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637837259211808  [0;39m 返回结果: {"content":{"list":[{"id":102,"name":"红树林","parent":100,"sort":102},{"id":200,"name":"微生物","parent":0,"sort":3}],"total":10},"success":true}
2025-06-27 11:26:00.895 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637837259211808  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-27 11:26:01.841 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4637837290406944  [0;39m ------------- 开始 -------------
2025-06-27 11:26:01.841 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4637837290406944  [0;39m 请求地址: http://localhost:8080/category/getcategoryListByPage GET
2025-06-27 11:26:01.841 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4637837290406944  [0;39m 类名方法: com.gec.wiki.controller.CategoryController.getcategoryListByPage
2025-06-27 11:26:01.842 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4637837290406944  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-06-27 11:26:01.842 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4637837290406944  [0;39m 请求参数: [{"page":3,"size":2}]
2025-06-27 11:26:01.846 INFO  com.gec.wiki.service.CategoryService              :61   [32m4637837290406944  [0;39m 总行数：10
2025-06-27 11:26:01.846 INFO  com.gec.wiki.service.CategoryService              :62   [32m4637837290406944  [0;39m 总页数：5
2025-06-27 11:26:01.846 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4637837290406944  [0;39m 返回结果: {"content":{"list":[{"id":201,"name":"地下微生物","parent":200,"sort":201},{"id":202,"name":"海洋微生物","parent":200,"sort":202}],"total":10},"success":true}
2025-06-27 11:26:01.847 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4637837290406944  [0;39m ------------- 结束 耗时：6 ms -------------
2025-06-27 11:35:17.863 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-06-27 11:35:17.870 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
